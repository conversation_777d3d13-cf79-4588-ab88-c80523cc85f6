<template>
  <div class="console">
    <!-- 第一行：关键指标概览区 -->
    <KeyStatistics ref="keyStatisticsRef"></KeyStatistics>

    <!-- 第二行：任务、报价与快捷操作区 -->
    <div class="column column2">
      <TodoTasks ref="todoTasksRef"></TodoTasks>
      <DailyPriceWidget ref="dailyPriceRef"></DailyPriceWidget>
      <QuickActions></QuickActions>
    </div>

    <!-- 第三行：汇报与新闻资讯区 -->
    <div class="column column3">
      <WorkReports ref="workReportsRef"></WorkReports>
      <CompanyNews ref="companyNewsRef"></CompanyNews>
    </div>

    <!--    <div class="bottom-wrap art-custom-card">
          <div>
            <h2 class="box-title">关于项目</h2>
            <p>{{ systemName }} 是一款专注于用户体验和视觉设计的后台管理系统模版</p>
            <p>使用了 Vue3、TypeScript、Vite、Element Plus 等前沿技术</p>

            <div class="button-wrap">
              <div class="btn art-custom-card" @click="goPage(WEB_LINKS.DOCS)">
                <span>项目官网</span>
                <i class="iconfont-sys">&#xe703;</i>
              </div>
              <div class="btn art-custom-card" @click="goPage(WEB_LINKS.INTRODUCE)">
                <span>文档</span>
                <i class="iconfont-sys">&#xe703;</i>
              </div>
              <div class="btn art-custom-card" @click="goPage(WEB_LINKS.GITHUB_HOME)">
                <span>Github</span>
                <i class="iconfont-sys">&#xe703;</i>
              </div>
              <div class="btn art-custom-card" @click="goPage(WEB_LINKS.BLOG)">
                <span>博客</span>
                <i class="iconfont-sys">&#xe703;</i>
              </div>
            </div>
          </div>
          <img class="right-img" src="@imgs/draw/draw1.png" />
        </div>-->
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import KeyStatistics from './widget/KeyStatistics.vue'
  import TodoTasks from './widget/TodoTasks.vue'
  import DailyPriceWidget from './widget/DailyPriceWidget.vue'
  import QuickActions from './widget/QuickActions.vue'
  import WorkReports from './widget/WorkReports.vue'
  import CompanyNews from './widget/CompanyNews.vue'
  import { useSettingStore } from '@/store/modules/setting'
  import { useCommon } from '@/composables/useCommon'

  const settingStore = useSettingStore()
  const currentGlopTheme = computed(() => settingStore.systemThemeType)

  // 组件引用
  const keyStatisticsRef = ref()
  const todoTasksRef = ref()
  const dailyPriceRef = ref()
  const workReportsRef = ref()
  const companyNewsRef = ref()

  // 系统主题风格变化时，刷新页面重写渲染 Echarts
  watch(currentGlopTheme, () => {
    settingStore.reload()
  })

  // 刷新所有组件数据
  const refreshAllData = () => {
    keyStatisticsRef.value?.refresh()
    todoTasksRef.value?.refresh()
    dailyPriceRef.value?.refresh()
    workReportsRef.value?.refresh()
    companyNewsRef.value?.refresh()
  }

  useCommon().scrollToTop()

  // 暴露刷新方法供外部调用
  defineExpose({
    refreshAllData
  })
</script>

<style lang="scss" scoped>
  .console {
    padding-bottom: 15px;

    :deep(.card-header) {
      display: flex;
      justify-content: space-between;
      padding: 20px 25px 5px 0;

      .title {
        h4 {
          font-size: 18px;
          font-weight: 500;
          color: var(--art-text-gray-800);
        }

        p {
          margin-top: 3px;
          font-size: 13px;

          span {
            margin-left: 10px;
            color: #52c41a;
          }
        }
      }
    }

    // 主标题
    :deep(.box-title) {
      color: var(--art-gray-900) !important;
    }

    // 副标题
    :deep(.subtitle) {
      color: var(--art-gray-600) !important;
    }

    :deep(.card-list li),
    .region,
    .dynamic,
    .bottom-wrap {
      background: var(--art-main-bg-color);
      border-radius: calc(var(--custom-radius) + 4px) !important;
    }

    .column {
      display: flex;
      justify-content: space-between;
      margin-top: var(--console-margin);
      background-color: transparent !important;
      gap: var(--console-margin);
    }

    .column2 {
      :deep(.todo-tasks-widget) {
        flex: 1;
      }

      :deep(.daily-price-widget) {
        flex: 1;
      }

      :deep(.quick-actions-widget) {
        width: 400px;
        flex-shrink: 0;
      }
    }

    .column3 {
      :deep(.work-reports-widget),
      :deep(.company-news-widget) {
        flex: 1;
      }
    }

    .bottom-wrap {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      height: 300px;
      padding: 20px;
      margin-top: var(--console-margin);
      background: var(--art-main-bg-color);

      h2 {
        margin-top: 10px;
        font-size: 20px;
        font-weight: 500;
      }

      p {
        margin-top: 5px;
        font-size: 14px;
        color: var(--art-gray-600);
      }

      .button-wrap {
        display: flex;
        flex-wrap: wrap;
        width: 600px;
        margin-top: 35px;

        .btn {
          display: flex;
          justify-content: space-between;
          width: 240px;
          height: 50px;
          padding: 0 15px;
          margin: 0 15px 15px 0;
          font-size: 14px;
          line-height: 50px;
          color: var(--art-gray-800);
          text-align: center;
          cursor: pointer;
          background: var(--art-bg-color);
          border-radius: calc(var(--custom-radius) / 2 + 2px) !important;
          transition: all 0.3s;

          &:hover {
            box-shadow: 0 5px 10px rgb(0 0 0 / 5%);
            transform: translateY(-4px);
          }
        }
      }
    }
  }
</style>

<!-- 移动端处理 -->
<style lang="scss" scoped>
  .console {
    @media screen and (max-width: $device-ipad-pro) {
      .column2 {
        margin-top: 15px;

        :deep(.todo-tasks-widget) {
          flex: 1;
        }

        :deep(.daily-price-widget) {
          flex: 1;
        }

        :deep(.quick-actions-widget) {
          width: 350px;
        }
      }

      .column3 {
        display: flex;
        margin-top: 15px;

        :deep(.work-reports-widget),
        :deep(.company-news-widget) {
          flex: 1;
        }
      }

      .bottom-wrap {
        height: auto;
        margin-top: 15px;

        .button-wrap {
          width: 470px;
          margin-top: 20px;

          .btn {
            width: 180px;
          }
        }

        .right-img {
          width: 300px;
          height: 230px;
        }
      }
    }

    @media screen and (max-width: $device-ipad-vertical) {
      .column2 {
        display: block;
        margin-top: 15px;

        :deep(.todo-tasks-widget) {
          width: 100%;
          margin-bottom: 15px;
        }

        :deep(.daily-price-widget) {
          width: 100%;
          margin-bottom: 15px;
        }

        :deep(.quick-actions-widget) {
          width: 100%;
        }
      }

      .column3 {
        display: block;
        margin-top: 15px;

        :deep(.work-reports-widget) {
          width: 100%;
          margin-bottom: 15px;
        }

        :deep(.company-news-widget) {
          width: 100%;
        }
      }

      .bottom-wrap {
        height: auto;
        margin-top: 15px;

        .button-wrap {
          width: 100%;
          margin-top: 20px;

          .btn {
            width: 190px;
            height: 50px;
            line-height: 50px;
          }
        }

        .right-img {
          display: none;
        }
      }
    }

    @media screen and (max-width: $device-phone) {
      .column2 {
        display: block;
        margin-top: 15px;

        :deep(.todo-tasks-widget) {
          width: 100%;
          margin-bottom: 15px;
          height: auto;
          min-height: 300px;
        }

        :deep(.daily-price-widget) {
          width: 100%;
          margin-bottom: 15px;
          height: auto;
          min-height: 300px;
        }

        :deep(.quick-actions-widget) {
          width: 100%;
          height: auto;
        }
      }

      .column3 {
        display: block;
        margin-top: 15px;

        :deep(.work-reports-widget) {
          width: 100%;
          margin-bottom: 15px;
          height: auto;
          min-height: 300px;
        }

        :deep(.company-news-widget) {
          width: 100%;
          height: auto;
          min-height: 300px;
        }
      }
    }
  }
</style>
