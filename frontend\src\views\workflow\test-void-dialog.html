<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作废弹出框测试</title>
    <script src="https://unpkg.com/element-plus@2.4.4/dist/index.full.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus@2.4.4/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="app">
        <el-button type="danger" @click="testVoidDialog">测试作废弹出框</el-button>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessageBox, ElMessage } = ElementPlus;

        createApp({
            methods: {
                async testVoidDialog() {
                    try {
                        // 使用 ElMessageBox.prompt 获取作废原因
                        const { value: reason } = await ElMessageBox.prompt(
                            '请输入作废原因（必填）：',
                            '确认作废',
                            {
                                confirmButtonText: '确定作废',
                                cancelButtonText: '取消',
                                inputType: 'textarea',
                                inputPlaceholder: '请详细说明作废原因...',
                                inputValidator: (value) => {
                                    if (!value || value.trim().length === 0) {
                                        return '作废原因不能为空'
                                    }
                                    if (value.trim().length < 5) {
                                        return '作废原因至少需要5个字符'
                                    }
                                    if (value.trim().length > 200) {
                                        return '作废原因不能超过200个字符'
                                    }
                                    return true
                                },
                                inputErrorMessage: '请输入有效的作废原因',
                                type: 'warning'
                            }
                        )

                        // 模拟API调用
                        console.log('作废原因:', reason.trim());
                        ElMessage.success('作废原因已获取: ' + reason.trim());
                        
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('获取作废原因失败:', error);
                        } else {
                            ElMessage.info('用户取消了作废操作');
                        }
                    }
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
