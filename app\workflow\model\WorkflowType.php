<?php
declare(strict_types=1);

namespace app\workflow\model;

use app\common\core\base\BaseModel;

/**
 * 工作流程类型表模型
 */
class WorkflowType extends BaseModel
{
	// 设置表名
	protected $name = 'workflow_type';
	protected bool $enableTenantIsolation = false;
	
	public function getConditionFormFieldsAttr($value): string
	{
		return $value
			? htmlspecialchars_decode($value)
			: '';
	}

	/**
	 * 获取默认搜索字段
	 *
	 * @return array
	 */
	public function getDefaultSearchFields(): array
	{
		return [
			'name' => ['type' => 'like'],
			'business_code' => ['type' => 'eq'],
			'status' => ['type' => 'eq'],
			'created_at' => ['type' => 'date'],
		];
	}
} 