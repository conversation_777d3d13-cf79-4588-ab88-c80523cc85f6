<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import { ElTag, ElTabs, ElTabPane, ElButton, ElDialog } from 'element-plus'
  import { ElMessageBox, ElMessage } from 'element-plus'
  // import type { FormRules } from 'element-plus'
  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  import { NoticeMessageApi } from '@/api/notice/noticeMessageApi'
  // import { useAuth } from '@/composables/useAuth'
  import { ApiStatus } from '@/utils/http/status'

  // const { hasAuth } = useAuth()
  const loading = ref(false)

  // 详情对话框
  const detailDialogVisible = ref(false)
  const detailData = ref<any>({})

  // 定义表单搜索初始值
  interface SearchState {
    title: string
    content: string
    type: string
    sub_type: string
    sender_name: string
    send_time: string | [string, string]
    status: string
    created_at: string
    read_status: string | number

    [key: string]: any // 允许添加任意属性
  }

  // 响应式表单数据
  const initialSearchState: SearchState = {
    title: '',
    content: '',
    type: '',
    sub_type: '',
    sender_name: '',
    send_time: '',
    status: '',
    created_at: '',
    read_status: 0
  }

  // 响应式表单数据
  const formFilters = reactive<SearchState>({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 消息分类标签
  const activeTab = ref('unread')
  const tabs = [
    { name: 'unread', label: '未读消息' },
    { name: 'read', label: '已读消息' },
    { name: 'all', label: '全部消息' }
    // { name: 'system', label: '系统通知' },
    // { name: 'workflow', label: '工作流通知' }
  ]

  // 按照类型筛选消息
  const handleTabChange = (tabName: string) => {
    activeTab.value = tabName
    if (tabName === 'all') {
      formFilters.read_status = ''
      formFilters.type = ''
    } else if (tabName === 'unread') {
      formFilters.read_status = '0'
      formFilters.type = ''
    } else if (tabName === 'read') {
      formFilters.read_status = '1'
      formFilters.type = ''
    } else {
      formFilters.type = tabName
      formFilters.read_status = ''
    }
    currentPage.value = 1
    getTableData()
  }

  // 全部标记为已读
  const markAllRead = async () => {
    try {
      ElMessageBox.confirm('确认将所有未读消息标记为已读吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        loading.value = true
        const res = await NoticeMessageApi.markAllRead({})
        if (res.code === ApiStatus.success) {
          ElMessage.success('操作成功')
          await getTableData()
        }
      })
    } catch (error) {
      console.error('标记全部已读失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 标记单条消息为已读
  const markRead = async (id: number, isMessageRead: boolean = true) => {
    try {
      loading.value = true
      const res = await NoticeMessageApi.markRead(id, {})
      if (res.code === ApiStatus.success) {
        if (isMessageRead) {
          ElMessage.success('已标记为已读')
        }
        await getTableData()
      }
    } catch (error) {
      console.error('标记已读失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 查看消息详情
  const viewMessageDetail = (row: any) => {
    detailData.value = row
    detailDialogVisible.value = true

    // 如果是未读消息，标记为已读
    if (row.read_status === 0) {
      markRead(row.id, false)
    }
  }

  // 点击标题查看详情
  const handleTitleClick = (row: any) => {
    viewMessageDetail(row)
  }

  // 跳转到相关业务页面
  const goToDetailPage = (row: any) => {
    if (row.detail_url) {
      window.open(row.detail_url, '_blank')
    } else {
      ElMessage.info('该消息未关联业务详情页')
    }
  }

  // 搜索表单配置项
  const formItems: SearchFormItem[] = [
    /*{
      label: '消息标题',
      prop: 'title',
      type: 'input',
      config: {
        clearable: true
      },
      onChange: handleFormChange
    },*/
    /*{
      label: '消息类型',
      prop: 'type',
      type: 'select',
      config: {
        clearable: true
      },
      options: () => [
        { label: '系统通知', value: 'system' },
        { label: '工作流通知', value: 'workflow' },
        { label: '业务通知', value: 'business' }
      ],
      onChange: handleFormChange
    },*/
    /*{
      label: '阅读状态',
      prop: 'read_status',
      type: 'select',
      config: {
        clearable: true
      },
      options: () => [
        { label: '未读', value: '0' },
        { label: '已读', value: '1' }
      ],
      onChange: handleFormChange
    },*/
    {
      label: '发送时间',
      prop: 'send_time',
      type: 'daterange',
      config: {
        type: 'daterange',
        clearable: true
      },
      onChange: handleFormChange
    }
  ]

  // 列配置
  const columnOptions = [{ label: '操作', prop: 'operation' }]

  // 表格数据
  const tableData = ref<any[]>([])

  const currentPage = ref(1),
    pageSize = ref(10),
    total = ref(0)

  onMounted(() => {
    getTableData()
  })

  const getTableData = async () => {
    try {
      loading.value = true
      const params: Record<string, any> = {
        ...formFilters
      }

      // 转换日期范围
      if (params.send_time && Array.isArray(params.send_time) && params.send_time.length === 2) {
        params.send_time_start = params.send_time[0]
        params.send_time_end = params.send_time[1]
        params.send_time = undefined
      }

      const res = await NoticeMessageApi.userMessages({
        page: currentPage.value,
        page_size: pageSize.value,
        ...params
      })

      if (res.code === ApiStatus.success) {
        tableData.value = res.data.list || []
        total.value = res.data.total || 0
      }
    } catch (error) {
      console.error('获取数据失败:', error)
      tableData.value = []
      total.value = 0
    } finally {
      loading.value = false
    }
  }

  const handleRefresh = () => {
    getTableData()
  }

  // 显示详情
  /*const showDetail = async (id: number) => {
    try {
      loading.value = true
      const res = await NoticeMessageApi.detail(id)
      if (res.code === 1) {
        detailData.value = res.data
        detailDialogVisible.value = true
      }
    } finally {
      loading.value = false
    }
  }*/

  // 获取消息类型标签样式
  const getMessageTypeTag = (type: string): '' | 'success' | 'warning' | 'info' | 'danger' => {
    const typeMap: Record<string, 'success' | 'warning' | 'info' | 'danger'> = {
      system: 'info',
      workflow: 'warning',
      business: 'success'
    }
    return typeMap[type] || 'info'
  }

  // 获取消息类型文本
  const getMessageTypeText = (type: string) => {
    console.log('type', type)
    /*const typeMap: Record<string, string> = {
      system: '系统通知',
      workflow: '工作流通知',
      business: '业务通知'
    }
    return typeMap[type] || '系统通知'*/
    return '系统通知'
  }

  // 批量删除消息
  /*const handleBatchDelete = () => {
    if (!selectedKeys.value.length) {
      ElMessage.warning('请选择要删除的消息')
      return
    }

    ElMessageBox.confirm(`确认删除选中的 ${selectedKeys.value.length} 条消息吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      loading.value = true
      try {
        const res = await NoticeMessageApi.delete(selectedKeys.value)
        if (res.code === ApiStatus.success) {
          ElMessage.success('删除成功')
          selectedKeys.value = []
          await getTableData()
        }
      } catch (error) {
        console.error('删除失败:', error)
      } finally {
        loading.value = false
      }
    })
  }*/

  // 定义表格相关数据
  const selectedKeys = ref<number[]>([])

  // 处理表格行选择变化
  const handleSelectionChange = (selection: any[]) => {
    // 确保selectedKeys只包含数字ID
    selectedKeys.value = selection.map((item) => item.id)
  }
</script>

<template>
  <ArtTableFullScreen>
    <div class="notice_message-page" id="table-full-screen">
      <!-- 消息分类标签页 -->
      <ElCard shadow="never" class="message-tabs-card">
        <ElTabs
          v-model="activeTab"
          @tab-click="(tab) => handleTabChange(tab.paneName as string)"
          type="border-card"
        >
          <ElTabPane v-for="tab in tabs" :key="tab.name" :label="tab.label" :name="tab.name">
            <!-- Tab内容区域 -->
            <div class="tab-content">
              <!-- 搜索栏 -->
              <ArtSearchBar
                v-model:filter="formFilters"
                :items="formItems"
                @reset="handleReset"
                @search="handleSearch"
              ></ArtSearchBar>

              <ElCard shadow="never" class="art-table-card">
                <!-- 表格头部 -->
                <ArtTableHeader :columnList="columnOptions" @refresh="handleRefresh">
                  <template #left>
                    <ElButton
                      @click="markAllRead"
                      icon="Check"
                      type="primary"
                      v-ripple
                      :loading="loading"
                      >全部已读
                    </ElButton>
                    <!--                    <ElButton
                                          @click="handleBatchDelete"
                                          type="danger"
                                          plain
                                          v-ripple
                                          icon="Delete"
                                          :loading="loading"
                                          :disabled="!selectedKeys.length"
                                        >
                                          批量删除
                                        </ElButton>-->
                  </template>
                </ArtTableHeader>

                <!-- 表格 -->
                <ArtTable
                  :loading="loading"
                  :currentPage="currentPage"
                  :pageSize="pageSize"
                  :data="tableData"
                  :total="total"
                  height="520"
                  :selectable="true"
                  v-model:selectedList="selectedKeys"
                  @selection-change="handleSelectionChange"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                >
                  <template #empty>
                    <div class="empty-container">
                      <el-empty :image-size="100" description="暂无消息数据"></el-empty>
                    </div>
                  </template>
                  <template #default>
                    <!-- 替换为手动定义的列，保留需要自定义内容的列 -->
                    <ElTableColumn type="selection" width="50" />
                    <ElTableColumn type="index" width="60" label="序号" />
                    <ElTableColumn prop="type" label="消息类型">
                      <template #default="{ row }">
                        <ElTag :type="getMessageTypeTag(row.type) || 'info'">
                          {{ getMessageTypeText(row.type) }}
                        </ElTag>
                      </template>
                    </ElTableColumn>
                    <ElTableColumn prop="title" label="消息标题">
                      <template #default="{ row }">
                        <div
                          class="message-title"
                          :class="{ unread: row.read_status === 0 }"
                          @click.stop="handleTitleClick(row)"
                        >
                          {{ row.title }}
                        </div>
                      </template>
                    </ElTableColumn>
                    <!--                    <ElTableColumn prop="content" label="消息内容" show-overflow-tooltip />-->
                    <!--                    <ElTableColumn prop="sender_name" label="发送人姓名" />-->
                    <ElTableColumn prop="send_time" label="发送时间" sortable />
                    <ElTableColumn prop="read_status" label="状态">
                      <template #default="{ row }">
                        <ElTag :type="row.read_status === 0 ? 'danger' : 'success'" effect="plain">
                          {{ row.read_status === 0 ? '未读' : '已读' }}
                        </ElTag>
                      </template>
                    </ElTableColumn>
                    <ElTableColumn prop="operation" label="操作" width="220">
                      <template #default="{ row }">
                        <ArtButtonTable
                          v-if="row.read_status === 0"
                          type="add"
                          text="已读"
                          @click.stop="markRead(row.id)"
                        />
                        <ArtButtonTable
                          text="详情"
                          type="edit"
                          @click.stop="handleTitleClick(row)"
                        />
                        <!--                        <ArtButtonTable
                                                  text="删除"
                                                  type="delete"
                                                  @click.stop="handleDelete(row.id)"
                                                />-->
                      </template>
                    </ElTableColumn>
                  </template>
                </ArtTable>
              </ElCard>
            </div>
          </ElTabPane>
        </ElTabs>
      </ElCard>
    </div>

    <!-- 消息详情对话框 -->
    <ElDialog v-model="detailDialogVisible" title="消息详情" width="600px" destroy-on-close>
      <div class="message-detail">
        <div class="detail-title">
          <h3>{{ detailData.title }}</h3>
        </div>
        <div class="detail-content">
          <div class="content-text" v-html="detailData.content"></div>
        </div>
      </div>
      <template #footer>
        <ElButton @click="detailDialogVisible = false">关闭</ElButton>
        <ElButton v-if="detailData.detail_url" type="primary" @click="goToDetailPage(detailData)">
          查看相关业务
        </ElButton>
      </template>
    </ElDialog>
  </ArtTableFullScreen>
</template>

<style lang="scss" scoped>
  .notice_message-page {
    width: 100%;

    .message-tabs-card {
      margin-bottom: 15px;
    }

    .tab-content {
      padding: 0;
    }

    .message-title {
      cursor: pointer;

      &.unread {
        font-weight: bold;
      }

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }

  .message-detail {
    .detail-title {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;

      h3 {
        margin: 0;
        color: #303133;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .detail-content {
      line-height: 1.6;
      color: #606266;
      font-size: 14px;
      max-height: 400px;
      overflow-y: auto;

      .content-text {
        white-space: pre-wrap; /* 保留换行和空格 */
        word-wrap: break-word; /* 长单词换行 */
        word-break: break-all; /* 强制换行 */
      }
    }
  }
</style>
