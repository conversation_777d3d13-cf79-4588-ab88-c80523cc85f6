<template>
  <div class="todo-tasks-widget art-custom-card">
    <div class="widget-header">
      <h4 class="box-title">我的待办</h4>
      <span class="total-count" v-if="todoData.total_count > 0">
        ({{ todoData.total_count }})
      </span>
    </div>

    <div class="todo-content" v-loading="loading">
      <div v-if="todoData.tasks.length > 0" class="todo-list">
        <div
          v-for="task in todoData.tasks"
          :key="`${task.type}-${task.id}`"
          class="task-item"
          :class="{ urgent: task.urgent }"
          @click="handleTaskClick(task)"
        >
          <div class="task-content">
            <div class="task-header">
              <el-tag :type="getTaskTypeColor(task.type)" size="small" class="task-type-tag">
                {{ getTaskTypeText(task.type) }}
              </el-tag>
              <span class="task-title">{{ task.title }}</span>
            </div>
            <div class="task-meta">
              <span class="task-time">{{ formatTime(task.time) }}</span>
              <el-tag
                v-if="task.urgent"
                type="danger"
                size="small"
                effect="plain"
                class="urgent-tag"
              >
                ⚠️ 紧急
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty :image-size="80" description="暂无数据">
          <!--          <el-button @click="loadTodoTasks" type="primary" size="small"> 刷新数据 </el-button>-->
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { WorkbenchApi, type TodoTasksData, type TodoTask } from '@/api/dashboard/workbenchApi'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'

  const router = useRouter()

  const todoData = ref<TodoTasksData>({
    tasks: [],
    total_count: 0,
    urgent_count: 0,
    type_counts: {
      workflow: 0,
      crm: 0,
      project: 0
    }
  })

  const loading = ref(false)

  /**
   * 加载待办任务数据
   */
  const loadTodoTasks = async () => {
    try {
      loading.value = true
      const res = await WorkbenchApi.getTodoTasks()

      if (res.code === 1) {
        todoData.value = res.data as TodoTasksData
      }
    } catch (error) {
      console.error('加载待办任务失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理任务点击
   */
  const handleTaskClick = (task: TodoTask) => {
    // 根据任务类型跳转到对应页面
    switch (task.type) {
      case 'workflow':
        router.push('/office/workflow/task')
        break
      case 'crm':
        router.push('/crm/crm_follow_record')
        break
      case 'project':
        router.push('/project/project_tasks')
        break
      default:
        console.warn('未知的任务类型:', task.type)
    }
  }

  /**
   * 获取任务类型文本
   */
  const getTaskTypeText = (type: string): string => {
    const typeMap: Record<string, string> = {
      workflow: '审批',
      crm: '跟进',
      project: '任务'
    }
    return typeMap[type] || '其他'
  }

  /**
   * 获取任务类型颜色
   */
  const getTaskTypeColor = (type: string): string => {
    const colorMap: Record<string, string> = {
      workflow: 'warning',
      crm: 'primary',
      project: 'success'
    }
    return colorMap[type] || 'info'
  }

  /**
   * 格式化时间显示
   */
  const formatTime = (time: string): string => {
    const timestamp = new Date(time).getTime()
    const now = Date.now()
    const diff = now - timestamp

    if (diff < 3600000) {
      // 1小时内
      const minutes = Math.floor(diff / 60000)
      return minutes <= 0 ? '刚刚' : `${minutes}分钟前`
    } else if (diff < 86400000) {
      // 24小时内
      const hours = Math.floor(diff / 3600000)
      return `${hours}小时前`
    } else if (diff < 604800000) {
      // 7天内
      const days = Math.floor(diff / 86400000)
      return `${days}天前`
    } else {
      return new Date(time).toLocaleDateString('zh-CN', {
        month: 'numeric',
        day: 'numeric',
        hour: 'numeric',
        minute: 'numeric'
      })
    }
  }

  // 组件挂载时加载数据
  onMounted(() => {
    loadTodoTasks()
  })

  // 暴露刷新方法
  defineExpose({
    refresh: loadTodoTasks
  })
</script>

<style lang="scss" scoped>
  .todo-tasks-widget {
    height: 400px;
    padding: 20px;
    background: var(--art-main-bg-color);
    border-radius: calc(var(--custom-radius) + 4px) !important;

    .widget-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--art-text-color-1);
      }

      .total-count {
        color: var(--art-text-color-3);
        font-size: 14px;
      }
    }

    .todo-content {
      height: calc(100% - 60px);
      overflow: hidden;
    }

    .todo-list {
      height: 100%;
      overflow-y: auto;

      .task-item {
        padding: 12px;
        margin-bottom: 8px;
        border-radius: 6px;
        border: 1px solid var(--el-border-color-light);
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--el-color-primary);
          background-color: var(--el-color-primary-light-9);
        }

        &.urgent {
          border-color: var(--el-border-color-light);
          background-color: var(--art-main-bg-color);

          &:hover {
            border-color: var(--el-color-primary);
            background-color: var(--el-color-primary-light-9);
          }
        }

        .task-content {
          .task-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;

            .task-type-tag {
              flex-shrink: 0;
            }

            .task-title {
              flex: 1;
              font-size: 14px;
              color: var(--art-text-color-1);
              font-weight: 500;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .task-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .task-time {
              font-size: 12px;
              color: var(--art-text-color-3);
            }

            .urgent-tag {
              font-size: 11px;
            }
          }
        }
      }
    }

    .empty-state {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .todo-tasks-widget {
      height: auto;
      min-height: 300px;

      .todo-content {
        height: auto;
        min-height: 240px;
      }
    }
  }
</style>
