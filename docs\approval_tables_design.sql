-- 企业审批系统数据表设计（纯表结构版本）
-- 适用于150人以下企业规模
-- 设计时间：2025-07-25
-- 
-- 表结构说明：
-- 1. 独立场景审批：出库、入库、出货、采购、付款
-- 2. 通用业务审批：样品邮寄、报销、出差、外出
-- 3. 集成工作流系统，统一审批状态管理
-- 4. 支持多租户数据隔离
--
-- 审批状态枚举：
-- 0=草稿, 1=审批中, 2=已通过, 3=已拒绝, 4=已终止, 5=已撤回, 6=已作废

-- =====================================================================================
-- 1. 独立场景审批表
-- =====================================================================================

-- 出库申请表
DROP TABLE IF EXISTS `ims_outbound_approval`;
CREATE TABLE `ims_outbound_approval`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '出库申请ID',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',
    
    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID',
    `approval_status`      tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废',
    `submit_time`          datetime DEFAULT NULL COMMENT '提交审批时间',
    `approval_time`        datetime DEFAULT NULL COMMENT '审批完成时间',
    `submitter_id`         bigint(20) unsigned DEFAULT NULL COMMENT '提交人ID',
    
    -- 业务字段
    `outbound_no`          varchar(50) NOT NULL DEFAULT '' COMMENT '出库单号',
    `outbound_type`        tinyint(1) NOT NULL DEFAULT 1 COMMENT '出库类型:1=销售出库,2=调拨出库,3=其他出库',
    `dept_id`              bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '所在部门ID',
    `warehouse_id`         bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '出库仓库ID',
    `customer_id`          bigint(20) unsigned DEFAULT 0 COMMENT '客户ID（销售出库时使用）',
    `target_warehouse_id`  bigint(20) unsigned DEFAULT 0 COMMENT '目标仓库ID（调拨出库时使用）',
    `outbound_date`        date NOT NULL COMMENT '计划出库日期',
    `total_amount`         decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '出库总金额',
    `total_quantity`       decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '出库总数量',
    `reason`               varchar(500) NOT NULL DEFAULT '' COMMENT '出库原因',
    `remark`               text COMMENT '备注',
    `attachment`           text COMMENT '附件',
    
    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           datetime DEFAULT NULL COMMENT '删除时间',
    
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_outbound_no` (`outbound_no`),
    KEY `idx_outbound_date` (`outbound_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='出库申请表';

-- 出库明细表
DROP TABLE IF EXISTS `ims_outbound_item`;
CREATE TABLE `ims_outbound_item`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '明细ID',
    `tenant_id`       bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',
    `outbound_id`     bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '出库申请ID',
    `product_id`      bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '产品ID',
    `product_spec_id` bigint(20) unsigned DEFAULT 0 COMMENT '产品规格ID',
    `product_unit`    varchar(20) NOT NULL DEFAULT '' COMMENT '货物单位',
    `quantity`        decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '出库数量',
    `unit_price`      decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '单价',
    `total_amount`    decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '小计金额',
    `remark`          varchar(255) DEFAULT '' COMMENT '备注',
    
    -- 系统字段
    `created_at`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime DEFAULT NULL COMMENT '删除时间',
    
    PRIMARY KEY (`id`),
    KEY `idx_outbound_id` (`outbound_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='出库明细表';

-- 入库申请表
DROP TABLE IF EXISTS `ims_inbound_approval`;
CREATE TABLE `ims_inbound_approval`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '入库申请ID',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',
    
    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID',
    `approval_status`      tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废',
    `submit_time`          datetime DEFAULT NULL COMMENT '提交审批时间',
    `approval_time`        datetime DEFAULT NULL COMMENT '审批完成时间',
    `submitter_id`         bigint(20) unsigned DEFAULT NULL COMMENT '提交人ID',
    
    -- 业务字段
    `inbound_no`           varchar(50) NOT NULL DEFAULT '' COMMENT '入库单号',
    `inbound_type`         tinyint(1) NOT NULL DEFAULT 1 COMMENT '入库类型:1=采购入库,2=调拨入库,3=退货入库,4=其他入库',
    `warehouse_id`         bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '入库仓库ID',
    `supplier_id`          bigint(20) unsigned DEFAULT 0 COMMENT '供应商ID（采购入库时使用）',
    `source_warehouse_id`  bigint(20) unsigned DEFAULT 0 COMMENT '来源仓库ID（调拨入库时使用）',
    `inbound_date`         date NOT NULL COMMENT '计划入库日期',
    `total_amount`         decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '入库总金额',
    `total_quantity`       decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '入库总数量',
    `reason`               varchar(500) NOT NULL DEFAULT '' COMMENT '入库原因',
    `remark`               text COMMENT '备注',
    `attachment`           text COMMENT '附件',
    
    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           datetime DEFAULT NULL COMMENT '删除时间',
    
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_inbound_no` (`inbound_no`),
    KEY `idx_inbound_date` (`inbound_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='入库申请表';

-- 入库明细表
DROP TABLE IF EXISTS `ims_inbound_item`;
CREATE TABLE `ims_inbound_item`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '明细ID',
    `tenant_id`       bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',
    `inbound_id`      bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '入库申请ID',
    `product_id`      bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '产品ID',
    `product_spec_id` bigint(20) unsigned DEFAULT 0 COMMENT '产品规格ID',
    `quantity`        decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '入库数量',
    `unit_price`      decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '单价',
    `total_amount`    decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '小计金额',
    `remark`          varchar(255) DEFAULT '' COMMENT '备注',
    
    -- 系统字段
    `created_at`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime DEFAULT NULL COMMENT '删除时间',
    
    PRIMARY KEY (`id`),
    KEY `idx_inbound_id` (`inbound_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='入库明细表';

-- 出货申请表（从供应商虚拟仓发货）
DROP TABLE IF EXISTS `ims_shipment_approval`;
CREATE TABLE `ims_shipment_approval`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '出货申请ID',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',
    
    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID',
    `approval_status`      tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废',
    `submit_time`          datetime DEFAULT NULL COMMENT '提交审批时间',
    `approval_time`        datetime DEFAULT NULL COMMENT '审批完成时间',
    `submitter_id`         bigint(20) unsigned DEFAULT NULL COMMENT '提交人ID',
    
    -- 业务字段
    `shipment_no`          varchar(50) NOT NULL DEFAULT '' COMMENT '出货单号',
    `dept_id`              bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '所在部门ID',
    `supplier_id`          bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '供应商ID（虚拟仓）',
    `customer_id`          bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '客户ID',
    `shipment_date`        date NOT NULL COMMENT '计划出货日期',
    `delivery_address`     text COMMENT '收货地址',
    `contact_person`       varchar(50) DEFAULT '' COMMENT '联系人',
    `contact_phone`        varchar(20) DEFAULT '' COMMENT '联系电话',
    `total_amount`         decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '出货总金额',
    `total_quantity`       decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '出货总数量',
    `logistics_company`    varchar(100) DEFAULT '' COMMENT '物流公司',
    `tracking_no`          varchar(100) DEFAULT '' COMMENT '物流单号',
    `vehicle_info`         varchar(200) DEFAULT '' COMMENT '运输车辆信息',
    `remark`               text COMMENT '备注',
    `attachment`           text COMMENT '附件',
    
    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           datetime DEFAULT NULL COMMENT '删除时间',
    
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_shipment_no` (`shipment_no`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_shipment_date` (`shipment_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='出货申请表';

-- 出货明细表
DROP TABLE IF EXISTS `ims_shipment_item`;
CREATE TABLE `ims_shipment_item`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '明细ID',
    `tenant_id`       bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',
    `shipment_id`     bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '出货申请ID',
    `product_id`      bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '产品ID',
    `product_spec_id` bigint(20) unsigned DEFAULT 0 COMMENT '产品规格ID',
    `product_unit`    varchar(20) NOT NULL DEFAULT '' COMMENT '产品单位',
    `quantity`        decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '出货数量',
    `unit_price`      decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '单价',
    `total_amount`    decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '小计金额',
    `remark`          varchar(255) DEFAULT '' COMMENT '备注',
    
    -- 系统字段
    `created_at`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime DEFAULT NULL COMMENT '删除时间',
    
    PRIMARY KEY (`id`),
    KEY `idx_shipment_id` (`shipment_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='出货明细表';

-- 采购申请表
DROP TABLE IF EXISTS `ims_purchase_approval`;
CREATE TABLE `ims_purchase_approval`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '采购申请ID',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID',
    `approval_status`      tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废',
    `submit_time`          datetime DEFAULT NULL COMMENT '提交审批时间',
    `approval_time`        datetime DEFAULT NULL COMMENT '审批完成时间',
    `submitter_id`         bigint(20) unsigned DEFAULT NULL COMMENT '提交人ID',

    -- 业务字段
    `purchase_no`          varchar(50) NOT NULL DEFAULT '' COMMENT '采购申请单号',
    `purchase_type`        tinyint(1) NOT NULL DEFAULT 1 COMMENT '采购类型:1=常规采购,2=紧急采购,3=补货采购',
    `supplier_id`          bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '供应商ID',
    `expected_date`        date NOT NULL COMMENT '期望到货日期',
    `delivery_address`     text COMMENT '收货地址',
    `contact_person`       varchar(50) DEFAULT '' COMMENT '联系人',
    `contact_phone`        varchar(20) DEFAULT '' COMMENT '联系电话',
    `total_amount`         decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '采购总金额',
    `total_amount_upper`   varchar(200) DEFAULT '' COMMENT '采购总金额大写',
    `total_quantity`       decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '采购总数量',
    `payment_terms`        varchar(100) DEFAULT '' COMMENT '付款条件',
    `reason`               varchar(500) NOT NULL DEFAULT '' COMMENT '采购原因',
    `remark`               text COMMENT '备注',
    `attachment`           text COMMENT '附件',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           datetime DEFAULT NULL COMMENT '删除时间',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_purchase_no` (`purchase_no`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_expected_date` (`expected_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='采购申请表';

-- 采购明细表
DROP TABLE IF EXISTS `ims_purchase_item`;
CREATE TABLE `ims_purchase_item`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '明细ID',
    `tenant_id`       bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',
    `purchase_id`     bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '采购申请ID',
    `product_id`      bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '产品ID',
    `product_spec_id` bigint(20) unsigned DEFAULT 0 COMMENT '产品规格ID',
    `quantity`        decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '采购数量',
    `unit_price`      decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '单价',
    `total_amount`    decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '小计金额',
    `urgent_level`    tinyint(1) NOT NULL DEFAULT 1 COMMENT '紧急程度:1=一般,2=紧急,3=特急',
    `remark`          varchar(255) DEFAULT '' COMMENT '备注',

    -- 系统字段
    `created_at`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`      datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime DEFAULT NULL COMMENT '删除时间',

    PRIMARY KEY (`id`),
    KEY `idx_purchase_id` (`purchase_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='采购明细表';

-- 付款申请表
DROP TABLE IF EXISTS `finance_payment_approval`;
CREATE TABLE `finance_payment_approval`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '付款申请ID',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID',
    `approval_status`      tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废',
    `submit_time`          datetime DEFAULT NULL COMMENT '提交审批时间',
    `approval_time`        datetime DEFAULT NULL COMMENT '审批完成时间',
    `submitter_id`         bigint(20) unsigned DEFAULT NULL COMMENT '提交人ID',

    -- 业务字段
    `payment_no`           varchar(50) NOT NULL DEFAULT '' COMMENT '付款申请单号',
    `payment_type`         tinyint(1) NOT NULL DEFAULT 1 COMMENT '付款类型:1=采购付款,2=费用报销,3=预付款,4=其他付款',
    `payee_type`           tinyint(1) NOT NULL DEFAULT 1 COMMENT '收款方类型:1=供应商,2=员工,3=其他',
    `payee_id`             bigint(20) unsigned DEFAULT 0 COMMENT '收款方ID',
    `payee_name`           varchar(100) NOT NULL DEFAULT '' COMMENT '收款方名称',
    `payee_account`        varchar(100) DEFAULT '' COMMENT '收款账户',
    `payee_bank`           varchar(100) DEFAULT '' COMMENT '开户行',
    `payment_amount`       decimal(15,2) NOT NULL DEFAULT 0.00 COMMENT '付款金额',
    `payment_amount_upper` varchar(200) DEFAULT '' COMMENT '付款金额大写',
    `payment_date`         date NOT NULL COMMENT '计划付款日期',
    `payment_method`       tinyint(1) NOT NULL DEFAULT 1 COMMENT '付款方式:1=银行转账,2=现金,3=支票,4=其他',
    `currency`             varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '币种',
    `exchange_rate`        decimal(10,4) NOT NULL DEFAULT 1.0000 COMMENT '汇率',
    `business_id`          bigint(20) unsigned DEFAULT 0 COMMENT '关联业务ID（如采购单、报销单等）',
    `business_type`        varchar(50) DEFAULT '' COMMENT '关联业务类型',
    `reason`               varchar(500) NOT NULL DEFAULT '' COMMENT '付款原因',
    `remark`               text COMMENT '备注',
    `attachment`           text COMMENT '附件',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           datetime DEFAULT NULL COMMENT '删除时间',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_payment_no` (`payment_no`),
    KEY `idx_payee` (`payee_type`, `payee_id`),
    KEY `idx_payment_date` (`payment_date`),
    KEY `idx_business` (`business_type`, `business_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='付款申请表';

-- =====================================================================================
-- 2. 通用业务审批表（集成在"我的申请"中）
-- =====================================================================================

-- 样品邮寄申请表
DROP TABLE IF EXISTS `business_sample_mail`;
CREATE TABLE `business_sample_mail`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '样品邮寄申请ID',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID',
    `approval_status`      tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废',
    `submit_time`          datetime DEFAULT NULL COMMENT '提交审批时间',
    `approval_time`        datetime DEFAULT NULL COMMENT '审批完成时间',
    `submitter_id`         bigint(20) unsigned DEFAULT NULL COMMENT '提交人ID',

    -- 业务字段
    `admin_id`             bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '申请人ID',
    `customer_id`          bigint(20) unsigned DEFAULT 0 COMMENT '客户ID',
    `customer_name`        varchar(100) NOT NULL DEFAULT '' COMMENT '客户名称',
    `contact_person`       varchar(50) NOT NULL DEFAULT '' COMMENT '联系人',
    `contact_phone`        varchar(20) DEFAULT '' COMMENT '联系电话',
    `delivery_address`     text NOT NULL COMMENT '邮寄地址',
    `sample_type`          tinyint(1) NOT NULL DEFAULT 1 COMMENT '样品类型:1=产品样品,2=包装样品,3=其他样品',
    `sample_name`          varchar(100) NOT NULL DEFAULT '' COMMENT '样品名称',
    `label_name`           varchar(100) DEFAULT '' COMMENT '标签名称',
    `sample_description`   text NOT NULL COMMENT '样品描述',
    `quantity`             int(11) NOT NULL DEFAULT 1 COMMENT '样品数量',
    `estimated_value`      decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '样品估值',
    `express_company`      varchar(50) DEFAULT '' COMMENT '快递公司',
    `express_fee`          decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '快递费用',
    `sender_name`          varchar(50) DEFAULT '' COMMENT '寄件人',
    `sender_phone`         varchar(20) DEFAULT '' COMMENT '寄件人电话',
    `is_return_required`   tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要归还:0=否,1=是',
    `expected_return_date` date DEFAULT NULL COMMENT '预期归还日期',
    `purpose`              varchar(500) NOT NULL DEFAULT '' COMMENT '邮寄目的',
    `remark`               text COMMENT '备注',
    `attachment`           text COMMENT '附件',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           datetime DEFAULT NULL COMMENT '删除时间',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='样品邮寄申请表';

-- 报销申请表
DROP TABLE IF EXISTS `finance_expense_reimbursement`;
CREATE TABLE `finance_expense_reimbursement`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '报销申请ID',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID',
    `approval_status`      tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废',
    `submit_time`          datetime DEFAULT NULL COMMENT '提交审批时间',
    `approval_time`        datetime DEFAULT NULL COMMENT '审批完成时间',
    `submitter_id`         bigint(20) unsigned DEFAULT NULL COMMENT '提交人ID',

    -- 业务字段
    `admin_id`             bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '申请人ID',
    `reimbursement_no`     varchar(50) NOT NULL DEFAULT '' COMMENT '报销单号',
    `expense_type`         tinyint(1) NOT NULL DEFAULT 1 COMMENT '费用类型:1=差旅费,2=交通费,3=餐费,4=住宿费,5=办公费,6=通讯费,7=其他',
    `total_amount`         decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '报销总金额',
    `expense_period_start` date NOT NULL COMMENT '费用发生开始日期',
    `expense_period_end`   date NOT NULL COMMENT '费用发生结束日期',
    `business_purpose`     varchar(500) NOT NULL DEFAULT '' COMMENT '业务目的',
    `payment_method`       tinyint(1) NOT NULL DEFAULT 1 COMMENT '支付方式:1=现金,2=银行卡,3=支付宝,4=微信,5=其他',
    `bank_account`         varchar(100) DEFAULT '' COMMENT '收款银行账户',
    `remark`               text COMMENT '备注',
    `attachment`           text COMMENT '附件（发票、收据等）',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           datetime DEFAULT NULL COMMENT '删除时间',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_reimbursement_no` (`reimbursement_no`),
    KEY `idx_expense_period` (`expense_period_start`, `expense_period_end`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='报销申请表';

-- 报销明细表
DROP TABLE IF EXISTS `finance_expense_item`;
CREATE TABLE `finance_expense_item`
(
    `id`                bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '报销明细ID',
    `tenant_id`         bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',
    `reimbursement_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '报销申请ID',
    `expense_date`      date NOT NULL COMMENT '费用发生日期',
    `expense_category`  varchar(50) NOT NULL DEFAULT '' COMMENT '费用类别',
    `expense_item`      varchar(100) NOT NULL DEFAULT '' COMMENT '费用项目',
    `amount`            decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '金额',
    `invoice_no`        varchar(50) DEFAULT '' COMMENT '发票号码',
    `merchant`          varchar(100) DEFAULT '' COMMENT '商家名称',
    `description`       varchar(255) DEFAULT '' COMMENT '费用说明',
    `attachment`        text COMMENT '附件（发票图片等）',

    -- 系统字段
    `created_at`        datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        datetime DEFAULT NULL COMMENT '删除时间',

    PRIMARY KEY (`id`),
    KEY `idx_reimbursement_id` (`reimbursement_id`),
    KEY `idx_expense_date` (`expense_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='报销明细表';

-- 出差申请表（优化版本，基于现有hr_travel表）
DROP TABLE IF EXISTS `hr_business_trip`;
CREATE TABLE `hr_business_trip`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '出差申请ID',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID',
    `approval_status`      tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废',
    `submit_time`          datetime DEFAULT NULL COMMENT '提交审批时间',
    `approval_time`        datetime DEFAULT NULL COMMENT '审批完成时间',
    `submitter_id`         bigint(20) unsigned DEFAULT NULL COMMENT '提交人ID',

    -- 业务字段
    `admin_id`             bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '申请人ID',
    `trip_type`            tinyint(1) NOT NULL DEFAULT 1 COMMENT '出差类型:1=国内,2=国际',
    `departure_city`       varchar(50) DEFAULT '' COMMENT '出发城市',
    `destination`          varchar(100) NOT NULL DEFAULT '' COMMENT '目的地',
    `trip_mode`            tinyint(1) NOT NULL DEFAULT 1 COMMENT '单程往返:1=往返,2=单程',
    `start_time`           datetime NOT NULL COMMENT '开始时间',
    `end_time`             datetime NOT NULL COMMENT '结束时间',
    `duration`             decimal(10,1) NOT NULL DEFAULT 0.0 COMMENT '出差时长(天)',
    `purpose`              varchar(500) NOT NULL DEFAULT '' COMMENT '出差目的',
    `budget`               decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '预算金额',
    `itinerary`            text COMMENT '行程安排',
    `is_advance_required`  tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要预支:0=否,1=是',
    `advance_amount`       decimal(10,2) DEFAULT 0.00 COMMENT '预支金额',
    `transportation`       varchar(100) DEFAULT '' COMMENT '交通方式',
    `accommodation`        varchar(100) DEFAULT '' COMMENT '住宿安排',
    `companions`           text COMMENT '同行人',
    `emergency_contact`    varchar(50) DEFAULT '' COMMENT '紧急联系人',
    `emergency_phone`      varchar(20) DEFAULT '' COMMENT '紧急联系电话',
    `remark`               text COMMENT '备注',
    `attachment`           text COMMENT '附件',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           datetime DEFAULT NULL COMMENT '删除时间',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_trip_time` (`start_time`, `end_time`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='出差申请表';

-- 外出申请表
DROP TABLE IF EXISTS `hr_outing`;
CREATE TABLE `hr_outing`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '外出申请ID',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned DEFAULT NULL COMMENT '工作流实例ID',
    `approval_status`      tinyint(1) DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废',
    `submit_time`          datetime DEFAULT NULL COMMENT '提交审批时间',
    `approval_time`        datetime DEFAULT NULL COMMENT '审批完成时间',
    `submitter_id`         bigint(20) unsigned DEFAULT NULL COMMENT '提交人ID',

    -- 业务字段
    `admin_id`             bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '申请人ID',
    `outing_type`          tinyint(1) NOT NULL DEFAULT 1 COMMENT '外出类型:1=客户拜访,2=会议,3=培训,4=办事,5=其他',
    `destination`          varchar(100) NOT NULL DEFAULT '' COMMENT '外出地点',
    `start_time`           datetime NOT NULL COMMENT '开始时间',
    `end_time`             datetime NOT NULL COMMENT '结束时间',
    `duration`             decimal(5,1) NOT NULL DEFAULT 0.0 COMMENT '外出时长(小时)',
    `purpose`              varchar(500) NOT NULL DEFAULT '' COMMENT '外出目的',
    `contact_person`       varchar(50) DEFAULT '' COMMENT '联系人',
    `contact_phone`        varchar(20) DEFAULT '' COMMENT '联系电话',
    `transportation`       varchar(50) DEFAULT '' COMMENT '交通方式',
    `is_return_office`     tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否返回办公室:0=否,1=是',
    `remark`               text COMMENT '备注',
    `attachment`           text COMMENT '附件',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人',
    `created_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           datetime DEFAULT NULL COMMENT '删除时间',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_admin_id` (`admin_id`),
    KEY `idx_outing_time` (`start_time`, `end_time`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='外出申请表';
