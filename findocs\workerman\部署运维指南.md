# Workerman自动化系统部署运维指南

## 📋 部署概述

Workerman自动化系统是一个基于PHP的长驻内存服务，需要独立于Web服务器运行。本指南详细说明了系统的部署、配置、监控和维护方法。

## 🔧 环境要求

### 系统要求
- **操作系统**：Linux (推荐) / Windows (开发环境)
- **PHP版本**：>= 7.4.0 (推荐 8.0+)
- **内存**：最小 512MB，推荐 1GB+
- **磁盘空间**：最小 100MB，推荐 1GB+

### PHP扩展要求
**必需扩展**：
- `pcntl` (Linux下进程控制，Windows不支持)
- `posix` (Linux下POSIX函数，Windows不支持)
- `sockets` (Socket支持)
- `json` (JSON支持)
- `pdo_mysql` (MySQL数据库支持)

**可选扩展**：
- `redis` (Redis缓存支持)
- `event` (高性能事件扩展)
- `libevent` (事件库支持)

### 检查命令
```bash
# 检查PHP版本
php -v

# 检查扩展
php -m | grep -E "(pcntl|posix|sockets|json|pdo_mysql)"

# 检查Workerman
php -r "echo class_exists('Workerman\Worker') ? 'Workerman已安装' : 'Workerman未安装';"
```

## 🚀 部署步骤

### 1. 代码部署
```bash
# 进入项目目录
cd /path/to/your/project

# 确保Workerman目录存在
ls -la workerman/

# 检查文件权限
chmod +x workerman/start.php
```

### 2. 数据库初始化
```sql
-- 执行数据库迁移
mysql -u username -p database_name < database/migrations/create_crm_advance_notice_log_table.sql
mysql -u username -p database_name < database/migrations/create_project_task_reminder_log_table.sql

-- 验证表创建
SHOW TABLES LIKE '%_log';
```

### 3. 配置文件检查
```bash
# 检查ThinkPHP配置
cat config/database.php
cat config/cache.php

# 检查日志目录权限
mkdir -p runtime/log
chmod 755 runtime/log
```

### 4. 启动服务

#### Linux环境
```bash
# 启动服务（前台运行，用于测试）
php workerman/start.php start

# 启动服务（后台运行，用于生产）
php workerman/start.php start -d

# 停止服务
php workerman/start.php stop

# 重启服务
php workerman/start.php restart

# 平滑重启（不断开连接）
php workerman/start.php reload

# 查看状态
php workerman/start.php status
```

#### Windows环境
```cmd
# Windows下只支持前台运行
php workerman/start.php start

# 停止服务（Ctrl+C）
```

## 📊 监控配置

### 1. 进程监控

#### 使用Supervisor (推荐)
```ini
# /etc/supervisor/conf.d/workerman.conf
[program:workerman]
command=php /path/to/project/workerman/start.php start
directory=/path/to/project
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/workerman.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=3
```

```bash
# 重新加载配置
sudo supervisorctl reread
sudo supervisorctl update

# 启动服务
sudo supervisorctl start workerman

# 查看状态
sudo supervisorctl status workerman
```

#### 使用Systemd
```ini
# /etc/systemd/system/workerman.service
[Unit]
Description=Workerman Automation System
After=network.target

[Service]
Type=forking
User=www-data
Group=www-data
WorkingDirectory=/path/to/project
ExecStart=/usr/bin/php workerman/start.php start -d
ExecReload=/usr/bin/php workerman/start.php reload
ExecStop=/usr/bin/php workerman/start.php stop
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

```bash
# 启用服务
sudo systemctl enable workerman
sudo systemctl start workerman

# 查看状态
sudo systemctl status workerman

# 查看日志
sudo journalctl -u workerman -f
```

### 2. 资源监控

#### 内存监控脚本
```bash
#!/bin/bash
# monitor_workerman.sh

PROCESS_NAME="workerman"
MAX_MEMORY=100000  # 100MB in KB

while true; do
    MEMORY=$(ps aux | grep "$PROCESS_NAME" | grep -v grep | awk '{sum+=$6} END {print sum}')
    
    if [ "$MEMORY" -gt "$MAX_MEMORY" ]; then
        echo "$(date): Memory usage too high: ${MEMORY}KB"
        # 可以添加重启逻辑或告警
    fi
    
    sleep 60
done
```

#### 连接数监控
```bash
#!/bin/bash
# monitor_connections.sh

PORT=8282
MAX_CONNECTIONS=1000

while true; do
    CONNECTIONS=$(netstat -an | grep ":$PORT" | grep ESTABLISHED | wc -l)
    
    if [ "$CONNECTIONS" -gt "$MAX_CONNECTIONS" ]; then
        echo "$(date): Too many connections: $CONNECTIONS"
    fi
    
    echo "$(date): Current connections: $CONNECTIONS"
    sleep 30
done
```

### 3. 日志监控

#### 日志轮转配置
```bash
# /etc/logrotate.d/workerman
/path/to/project/runtime/log/workerman_*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 www-data www-data
    postrotate
        # 可选：重新加载服务
        /usr/bin/php /path/to/project/workerman/start.php reload > /dev/null 2>&1 || true
    endscript
}
```

#### 错误日志监控
```bash
#!/bin/bash
# monitor_errors.sh

LOG_FILE="/path/to/project/runtime/log/workerman_$(date +%Y-%m-%d).log"
ERROR_THRESHOLD=10

if [ -f "$LOG_FILE" ]; then
    ERROR_COUNT=$(grep -c "\[error\]" "$LOG_FILE")
    
    if [ "$ERROR_COUNT" -gt "$ERROR_THRESHOLD" ]; then
        echo "$(date): Too many errors today: $ERROR_COUNT"
        # 发送告警邮件或通知
    fi
fi
```

## 🔧 配置优化

### 1. PHP配置优化
```ini
# php.ini 优化配置
memory_limit = 256M
max_execution_time = 0
max_input_time = -1
post_max_size = 100M
upload_max_filesize = 100M

# OPcache优化
opcache.enable=1
opcache.enable_cli=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

### 2. 系统参数优化
```bash
# /etc/sysctl.conf
# 增加文件描述符限制
fs.file-max = 65536

# 网络优化
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535

# 应用配置
echo "* soft nofile 65535" >> /etc/security/limits.conf
echo "* hard nofile 65535" >> /etc/security/limits.conf
```

### 3. Workerman配置优化
```php
// workerman/start.php 优化配置

// 设置进程数（通常等于CPU核心数）
$unifiedWorker->count = 1; // 单进程模式，避免数据竞争

// 设置用户和组
$unifiedWorker->user = 'www-data';
$unifiedWorker->group = 'www-data';

// 设置进程名称
$unifiedWorker->name = 'WorkermanAutomation';

// 启用端口复用（Linux 3.9+）
$unifiedWorker->reusePort = true;
```

## 🚨 故障排查

### 1. 常见问题

#### 启动失败
```bash
# 检查端口占用
netstat -tlnp | grep 8282

# 检查PHP扩展
php -m | grep -E "(pcntl|posix|sockets)"

# 检查文件权限
ls -la workerman/start.php

# 查看详细错误
php workerman/start.php start
```

#### 内存泄漏
```bash
# 监控内存使用
watch -n 1 'ps aux | grep workerman | grep -v grep'

# 检查内存使用趋势
ps -eo pid,ppid,cmd,%mem,%cpu --sort=-%mem | grep workerman
```

#### 连接问题
```bash
# 检查WebSocket连接
telnet localhost 8282

# 检查防火墙
iptables -L | grep 8282
ufw status | grep 8282
```

### 2. 调试模式

#### 启用调试日志
```php
// 在 workerman/common/WorkerBase.php 中
protected function log(string $message, string $level = 'info'): void
{
    // 启用详细日志
    if ($level === 'debug' || defined('DEBUG_MODE')) {
        echo "[DEBUG] " . date('Y-m-d H:i:s') . " {$message}\n";
    }
    
    // 原有日志逻辑...
}
```

#### 性能分析
```php
// 添加性能监控
$startTime = microtime(true);
$startMemory = memory_get_usage();

// 执行业务逻辑...

$endTime = microtime(true);
$endMemory = memory_get_usage();

$this->log("执行时间: " . round(($endTime - $startTime) * 1000, 2) . "ms");
$this->log("内存使用: " . round(($endMemory - $startMemory) / 1024, 2) . "KB");
```

## 🔄 维护操作

### 1. 日常维护

#### 每日检查
```bash
#!/bin/bash
# daily_check.sh

echo "=== Workerman Daily Check $(date) ==="

# 检查进程状态
echo "1. Process Status:"
ps aux | grep workerman | grep -v grep

# 检查内存使用
echo "2. Memory Usage:"
ps -eo pid,ppid,cmd,%mem --sort=-%mem | grep workerman | head -5

# 检查连接数
echo "3. Connection Count:"
netstat -an | grep ":8282" | grep ESTABLISHED | wc -l

# 检查错误日志
echo "4. Error Count (Today):"
LOG_FILE="runtime/log/workerman_$(date +%Y-%m-%d).log"
if [ -f "$LOG_FILE" ]; then
    grep -c "\[error\]" "$LOG_FILE"
else
    echo "No log file found"
fi

# 检查磁盘空间
echo "5. Disk Usage:"
df -h | grep -E "(/$|/var|/tmp)"

echo "=== Check Complete ==="
```

#### 每周维护
```bash
#!/bin/bash
# weekly_maintenance.sh

echo "=== Weekly Maintenance $(date) ==="

# 清理旧日志（保留30天）
find runtime/log -name "workerman_*.log" -mtime +30 -delete

# 重启服务（平滑重启）
php workerman/start.php reload

# 检查数据库连接
php -r "
require 'vendor/autoload.php';
require 'app/common.php';
try {
    \think\facade\Db::query('SELECT 1');
    echo 'Database connection: OK\n';
} catch (Exception \$e) {
    echo 'Database connection: FAILED - ' . \$e->getMessage() . '\n';
}
"

echo "=== Maintenance Complete ==="
```

### 2. 备份策略

#### 配置备份
```bash
#!/bin/bash
# backup_config.sh

BACKUP_DIR="/backup/workerman/$(date +%Y%m%d)"
mkdir -p "$BACKUP_DIR"

# 备份配置文件
cp -r config/ "$BACKUP_DIR/"
cp -r workerman/ "$BACKUP_DIR/"

# 备份数据库结构
mysqldump -u username -p --no-data database_name > "$BACKUP_DIR/schema.sql"

# 备份日志表数据
mysqldump -u username -p database_name crm_advance_notice_log project_task_reminder_log > "$BACKUP_DIR/logs.sql"

echo "Backup completed: $BACKUP_DIR"
```

### 3. 升级流程

#### 代码升级
```bash
#!/bin/bash
# upgrade.sh

echo "=== Workerman Upgrade Process ==="

# 1. 备份当前版本
cp -r workerman/ workerman_backup_$(date +%Y%m%d)/

# 2. 停止服务
php workerman/start.php stop

# 3. 更新代码
# (这里放置代码更新逻辑)

# 4. 检查语法
find workerman/ -name "*.php" -exec php -l {} \;

# 5. 运行测试
php vendor/bin/phpunit tests/Unit/

# 6. 启动服务
php workerman/start.php start -d

# 7. 验证服务
sleep 5
php workerman/start.php status

echo "=== Upgrade Complete ==="
```

## 📈 性能调优

### 1. 系统级优化
- **增加文件描述符限制**：支持更多并发连接
- **优化网络参数**：提升网络处理能力
- **使用SSD存储**：提升I/O性能
- **增加内存**：减少磁盘交换

### 2. 应用级优化
- **启用OPcache**：提升PHP执行效率
- **使用Redis缓存**：减少数据库查询
- **优化数据库索引**：提升查询性能
- **分批处理数据**：避免内存溢出

### 3. 监控指标
- **CPU使用率**：< 80%
- **内存使用率**：< 80%
- **连接数**：< 1000
- **响应时间**：< 100ms
- **错误率**：< 1%

## 🎯 最佳实践

1. **使用进程管理工具**：Supervisor或Systemd
2. **配置日志轮转**：避免日志文件过大
3. **定期监控资源**：及时发现性能问题
4. **建立告警机制**：快速响应故障
5. **定期备份配置**：确保数据安全
6. **测试升级流程**：降低升级风险
7. **文档化运维**：便于团队协作

通过遵循这些部署和运维指南，可以确保Workerman自动化系统稳定、高效地运行在生产环境中。
