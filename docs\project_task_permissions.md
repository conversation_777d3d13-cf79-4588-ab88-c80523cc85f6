# 项目任务权限配置说明

## 权限标识规范

根据系统权限命名规范 `module:controller:action`，项目任务相关权限标识如下：

### 基础任务权限
- `project:project_task:index` - 查看任务列表
- `project:project_task:detail` - 查看任务详情
- `project:project_task:add` - 添加任务
- `project:project_task:edit` - 编辑任务
- `project:project_task:delete` - 删除任务
- `project:project_task:my_tasks` - 查看我的任务
- `project:project_task:update_status` - 更新任务状态
- `project:project_task:assign` - 分配任务

### 任务评论权限
- `project:project_task:get_comments` - 获取任务评论列表
- `project:project_task:add_comment` - 添加任务评论
- `project:project_task_comment:edit_record` - 编辑评论记录
- `project:project_task_comment:delete_record` - 删除评论记录

### 任务跟进权限
- `project:project_task:get_follows` - 获取任务跟进列表
- `project:project_task:add_follow` - 添加任务跟进
- `project:project_task:edit_follow` - 编辑跟进记录
- `project:project_task:delete_follow` - 删除跟进记录

## 路由与权限映射

### 任务基础路由
```php
// route/project_task.php
Route::group('api/project/task', function () {
    Route::get('index', 'ProjectTaskController@index');                    // project:task:index
    Route::get('detail/:id', 'ProjectTaskController@detail');              // project:task:detail
    Route::post('add', 'ProjectTaskController@add');                       // project:task:add
    Route::post('edit/:id', 'ProjectTaskController@edit');                 // project:task:edit
    Route::post('delete/:id', 'ProjectTaskController@delete');             // project:task:delete
    Route::get('my', 'ProjectTaskController@myTasks');                     // project:task:my
    Route::post('update-status/:id', 'ProjectTaskController@updateStatus'); // project:task:update_status
    Route::post('assign/:id', 'ProjectTaskController@assign');             // project:task:assign
    
    // 任务记录相关路由
    Route::post('add-comment', 'ProjectTaskController@addComment');         // project:task:add_comment
    Route::post('add-follow', 'ProjectTaskController@addFollow');           // project:task:add_follow
    Route::get(':id/comments', 'ProjectTaskController@getComments');       // project:task:get_comments
    Route::get(':id/follows', 'ProjectTaskController@getFollows');         // project:task:get_follows
    Route::post('comment/edit/:record_id', 'ProjectTaskController@editRecord'); // project:task:edit_record
    Route::post('comment/delete/:record_id', 'ProjectTaskController@deleteRecord'); // project:task:delete_record
    Route::post('follow/edit/:record_id', 'ProjectTaskController@editFollow'); // project:task:edit_follow
    Route::post('follow/delete/:record_id', 'ProjectTaskController@deleteFollow'); // project:task:delete_follow
})->middleware([TokenAuthMiddleware::class, PermissionMiddleware::class]);
```

## 前端权限使用示例

### TaskDetail.vue 权限检查

TaskDetail 组件支持两种权限控制方式：

#### 1. 通过 Props 控制权限（推荐）
```vue
<TaskDetail
  v-model:visible="taskDetailVisible"
  :task-id="currentTaskId"
  :can-view-comments="true"
  :can-add-comment="true"
  :can-view-follows="true"
  :can-add-follow="true"
/>
```

#### 2. 自动权限检查
```typescript
// 权限验证 - 支持多种权限标识格式
const { hasAuth } = useAuth()

// 权限计算属性 - 优先级：props > 具体权限 > 通用权限
const canViewComments = computed(() => {
  // 1. 如果 props 明确指定了权限，使用 props 值
  if (props.canViewComments !== undefined) {
    return props.canViewComments
  }

  // 2. 检查具体权限标识
  const specificAuth = hasAuth('project:project_task:get_comments')
  const generalAuth = hasAuth('get_comments')
  return specificAuth || generalAuth
})
```

### 模板中的权限控制
```vue
<!-- 根据权限显示tabs -->
<el-tabs v-model="activeRecordTab" class="record-tabs">
  <el-tab-pane v-if="canViewComments" label="评论" name="comment" />
  <el-tab-pane v-if="canViewFollows" label="跟进" name="follow" />
</el-tabs>

<!-- 根据权限显示按钮 -->
<el-button
  v-if="activeRecordTab === 'comment' && canAddComment"
  type="primary"
  @click="showCommentForm"
>
  添加评论
</el-button>

<el-button
  v-if="activeRecordTab === 'follow' && canAddFollow"
  type="success"
  @click="showFollowForm"
>
  添加跟进
</el-button>
```

## 权限配置建议

### 角色权限配置示例

#### 项目经理
```
✅ 允许的权限:
- project:task:*           # 任务管理所有权限
- project:task:get_comments # 查看评论
- project:task:add_comment  # 添加评论
- project:task:get_follows  # 查看跟进
- project:task:add_follow   # 添加跟进
- project:task:edit_record  # 编辑记录
- project:task:delete_record # 删除记录
```

#### 普通开发者
```
✅ 允许的权限:
- project:task:index       # 查看任务列表
- project:task:detail      # 查看任务详情
- project:task:my          # 查看我的任务
- project:task:update_status # 更新任务状态
- project:task:get_comments # 查看评论
- project:task:add_comment  # 添加评论
- project:task:get_follows  # 查看跟进
- project:task:add_follow   # 添加跟进

❌ 禁止的权限:
- project:task:add         # 添加任务
- project:task:edit        # 编辑任务
- project:task:delete      # 删除任务
- project:task:assign      # 分配任务
```

#### 测试人员
```
✅ 允许的权限:
- project:task:index       # 查看任务列表
- project:task:detail      # 查看任务详情
- project:task:my          # 查看我的任务
- project:task:get_comments # 查看评论
- project:task:add_comment  # 添加评论
- project:task:get_follows  # 查看跟进

❌ 禁止的权限:
- project:task:add         # 添加任务
- project:task:edit        # 编辑任务
- project:task:delete      # 删除任务
- project:task:assign      # 分配任务
- project:task:add_follow  # 添加跟进
```

## 注意事项

1. **权限标识命名**: 严格按照 `module:controller:action` 格式
2. **路由权限**: 所有路由都通过 `PermissionMiddleware` 进行权限验证
3. **前端权限**: 使用 `useAuth()` 组合函数进行权限检查
4. **数据权限**: 任务记录支持数据权限过滤，只能操作自己创建的记录
5. **业务权限**: 任务负责人和项目负责人有额外的操作权限
