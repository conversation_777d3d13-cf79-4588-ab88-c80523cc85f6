-- 简化版项目任务管理数据库设计
-- 专注于核心功能：项目管理 + 任务管理 + 飞书UI风格
-- 适用于100人以下企业的轻量级解决方案
--
-- 核心表：
-- 1. project_project (项目表)
-- 2. project_task (任务表) 
-- 3. project_member (项目成员表)
-- 4. project_task_comment (任务评论表)
--
-- 生成器使用说明：
-- 1. 执行SQL创建表结构
-- 2. 执行生成器：php think generator:crud 表名 --module=project --frontend --overwrite
-- 3. 在system_menu表中添加菜单

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 项目表
-- ----------------------------
DROP TABLE IF EXISTS `project_project`;
CREATE TABLE `project_project` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '项目ID',
    `name` varchar(100) NOT NULL COMMENT '项目名称 | @s=like @e @exp @imp @val=required',
    `description` text COMMENT '项目描述 | @e @c=textarea @exp @imp',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '项目状态：1进行中，2已完成，3已暂停，4已取消 | @s=eq @e @c=select @exp @imp @val=required',
    `priority` tinyint(1) NOT NULL DEFAULT 2 COMMENT '优先级：1低，2中，3高 | @s=eq @e @c=select @exp @imp',
    `start_date` date NULL COMMENT '开始日期 | @s=between @e @c=date @exp @imp',
    `end_date` date NULL COMMENT '截止日期 | @s=between @e @c=date @exp @imp',
    `progress` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT '项目进度(%) | @e @exp @imp @val=number',
    `owner_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '项目负责人ID | @s=eq @e @c=select @exp @imp @val=required',
    `color` varchar(20) NOT NULL DEFAULT '#1664FF' COMMENT '项目颜色 | @e @c=color @exp @imp',
    `is_archived` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否归档：0否，1是 | @s=eq @e @c=switch @exp @imp',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_owner` (`tenant_id`, `owner_id`),
    KEY `idx_tenant_archived` (`tenant_id`, `is_archived`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='项目表';

-- ----------------------------
-- 项目成员表
-- ----------------------------
DROP TABLE IF EXISTS `project_member`;
CREATE TABLE `project_member` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '成员ID',
    `project_id` bigint(20) UNSIGNED NOT NULL COMMENT '项目ID | @s=eq @e @exp @imp @val=required',
    `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID | @s=eq @e @c=select @exp @imp @val=required',
    `role` varchar(20) NOT NULL DEFAULT 'member' COMMENT '项目角色：owner负责人，member成员 | @s=eq @e @c=select @exp @imp',
    `joined_at` datetime NULL COMMENT '加入时间 | @s=between @e @c=datetime @exp @imp',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_project_user` (`project_id`, `user_id`, `deleted_at`),
    KEY `idx_tenant_project` (`tenant_id`, `project_id`),
    KEY `idx_tenant_user` (`tenant_id`, `user_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='项目成员表';

-- ----------------------------
-- 任务表
-- ----------------------------
DROP TABLE IF EXISTS `project_task`;
CREATE TABLE `project_task` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    `project_id` bigint(20) UNSIGNED NOT NULL COMMENT '项目ID | @s=eq @e @exp @imp @val=required',
    `title` varchar(200) NOT NULL COMMENT '任务标题 | @s=like @e @exp @imp @val=required',
    `description` text COMMENT '任务描述 | @e @c=editor @exp @imp',
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '任务状态：1待办，2进行中，3已完成，4已关闭 | @s=eq @e @c=select @exp @imp @val=required',
    `priority` tinyint(1) NOT NULL DEFAULT 2 COMMENT '优先级：1低，2中，3高 | @s=eq @e @c=select @exp @imp',
    `assignee_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '负责人ID | @s=eq @e @c=select @exp @imp',
    `start_date` date NULL COMMENT '开始日期 | @s=between @e @c=date @exp @imp',
    `due_date` date NULL COMMENT '截止日期 | @s=between @e @c=date @exp @imp',
    `completed_at` datetime NULL COMMENT '完成时间 | @s=between @e @c=datetime @exp @imp',
    `estimated_hours` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '预估工时(小时) | @e @exp @imp @val=number',
    `actual_hours` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT '实际工时(小时) | @e @exp @imp @val=number',
    `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序 | @e @exp @imp @val=number',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_project` (`tenant_id`, `project_id`),
    KEY `idx_tenant_status` (`tenant_id`, `status`),
    KEY `idx_tenant_assignee` (`tenant_id`, `assignee_id`),
    KEY `idx_sort` (`sort`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='任务表';

-- ----------------------------
-- 任务记录表（统一评论和跟进功能）
-- 生成器命令: php think generator:crud project_task_record --module=project --frontend --overwrite
-- ----------------------------
DROP TABLE IF EXISTS `project_task_record`;
CREATE TABLE `project_task_record` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
    `task_id` bigint(20) UNSIGNED NOT NULL COMMENT '任务ID | @s=eq @e @exp @imp @val=required',
    `record_type` varchar(20) NOT NULL DEFAULT 'comment' COMMENT '记录类型：comment评论，follow跟进 | @s=eq @e @c=select @exp @imp @val=required',
    `content` text NOT NULL COMMENT '内容 | @e @c=textarea @exp @imp @val=required',
    `follow_type` varchar(30) DEFAULT NULL COMMENT '跟进方式：phone电话，meeting会议，email邮件，other其他 | @s=eq @e @c=select @exp @imp',
    `follow_date` datetime DEFAULT NULL COMMENT '跟进时间 | @s=between @e @c=datetime @exp @imp',
    `next_plan` text DEFAULT NULL COMMENT '下次计划 | @e @c=textarea @exp @imp',
    `next_date` datetime DEFAULT NULL COMMENT '下次跟进时间 | @s=between @e @c=datetime @exp @imp',
    `attachments` text COMMENT '附件列表JSON | @e @c=upload @col=files',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `creator_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `created_at` datetime NULL COMMENT '创建时间',
    `updated_at` datetime NULL COMMENT '更新时间',
    `deleted_at` datetime NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_task` (`tenant_id`, `task_id`),
    KEY `idx_tenant_type` (`tenant_id`, `record_type`),
    KEY `idx_follow_date` (`follow_date`),
    KEY `idx_next_date` (`next_date`),
    KEY `idx_tenant_creator` (`tenant_id`, `creator_id`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE=InnoDB COMMENT='任务记录表（评论+跟进）';

-- ----------------------------
-- 初始化数据
-- ----------------------------

-- ----------------------------
-- 生成器命令执行顺序
-- ----------------------------
-- 1. php think generator:crud project_project --module=project --frontend --overwrite
-- 2. php think generator:crud project_member --module=project --frontend --overwrite  
-- 3. php think generator:crud project_task --module=project --frontend --overwrite
-- 4. php think generator:crud project_task_comment --module=project --frontend --overwrite

SET FOREIGN_KEY_CHECKS = 1;
