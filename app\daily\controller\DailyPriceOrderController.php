<?php
declare(strict_types=1);

namespace app\daily\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\daily\service\DailyPriceOrderService;
use think\facade\Log;
use think\response\Json;

/**
 * 每日报价单表控制器
 */
class DailyPriceOrderController extends BaseController
{
	use CrudControllerTrait, ControllerImportExportTrait;
	
	/**
	 * @var DailyPriceOrderService
	 */
	protected DailyPriceOrderService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service = DailyPriceOrderService::getInstance();
	}
	
	/**
	 * 获取列表
	 *
	 * @return Json
	 */
	public function index(): Json
	{
		$params = $this->request->param();
		$result = $this->service->getOrderList($params);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 新增
	 *
	 * @return Json
	 */
	public function add(): Json
	{
		$params = $this->request->post();

		try {
			$result = $this->service->add($params);
			return $result
				? $this->success('添加成功', ['id' => $result])
				: $this->error('添加失败');
		}
		catch (\Exception $e) {
			Log::error('CRUD Add Debug - Exception: ' . $e->getMessage());
			return $this->error('添加失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 更新
	 *
	 * @param int $id
	 * @return Json
	 */
	public function edit(int $id): Json
	{
		$params = $this->request->post();
		
		try {
			$result = $this->service->edit($params, ['id' => $id]);
			return $result
				? $this->success('更新成功')
				: $this->error('更新失败');
		}
		catch (\Exception $e) {
			return $this->error('更新失败: ' . $e->getMessage());
		}
	}
	
	/**
	 * 获取详情（包含明细数据）
	 *
	 * @param int $id
	 * @return Json
	 */
	public function detail(int $id): Json
	{
		try {
			// 使用完善的getDetail方法获取详情和明细数据
			$orderData = $this->service->getDetail($id);
			
			if (!$orderData) {
				return $this->error('报价单不存在');
			}
			
			return $this->success('获取成功', $orderData);
		}
		catch (\Exception $e) {
			return $this->error('获取失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 提交审批
	 */
	public function submitApproval(): Json
	{
		$id = $this->request->post('id', 0, 'int');

		if (!$id) {
			return $this->error('参数错误');
		}

		try {
			// 获取报价单信息
			$order = $this->service->getModel()->find($id);
			if (!$order) {
				return $this->error('报价单不存在');
			}

			// 提交前检测重复
			$duplicate = $this->service->checkDuplicateDate(
				$order->price_date,
				'submit',
				$id  // 排除当前记录
			);

			if ($duplicate['exists']) {
				return $this->error($duplicate['message']);
			}

			$result = $this->service->submitApproval($id);

			return $result
				? $this->success('提交成功')
				: $this->error('提交失败');
		}
		catch (\Exception $e) {
			return $this->error('提交失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 撤回审批
	 */
	public function recallApproval(): Json
	{
		$id = $this->request->post('id', 0, 'int');
		
		if (!$id) {
			return $this->error('参数错误');
		}
		
		$result = $this->service->recallApproval($id);
		
		return $result
			? $this->success('提交成功')
			: $this->error('提交失败');
	}
	
	/**
	 * 作废报价单
	 */
	public function voidOrder(): Json
	{
		$id     = $this->request->post('id', 0, 'int');
		$reason = $this->request->post('reason', '', 'string');
		
		if (!$id || !$reason) {
			return $this->error('参数错误');
		}
		
		$result = $this->service->voidApproval($id, $reason);
		
		return $result
			? $this->success('提交成功')
			: $this->error('提交失败');
	}
	
	
	/**
	 * 获取供应商列表
	 */
	public function getSupplierList()
	{
		$result = $this->service->getSupplierList();
		
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取产品列表
	 */
	public function getProductList()
	{
		$supplierId = $this->request->param('supplier_id', 0, 'int');
		$result     = $this->service->getProductList($supplierId);
		
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取昨日价格数据
	 */
	public function getYesterdayPrices()
	{
		return $this->success('提交成功', $this->service->getYesterdayPrices());
	}

	/**
	 * 获取最近已审批的报价单（用于工作台显示）
	 */
	public function getRecentApproved(): Json
	{
		$result = $this->service->getRecentApproved();
		return $this->success('获取成功', $result);
	}

}