-- 更新 crm_contract_receivable 表的 payment_method 字段从 varchar 改为 tinyint
-- 执行时间：请在业务低峰期执行
-- 注意：此脚本会修改数据结构，请先备份数据库

-- 1. 添加临时字段
ALTER TABLE `crm_contract_receivable` 
ADD COLUMN `payment_method_new` tinyint(1) unsigned NOT NULL DEFAULT '1' 
COMMENT '付款方式:1=银行转账,2=现金,3=支票,4=支付宝,5=微信,6=其他' 
AFTER `payment_method`;

-- 2. 数据迁移：将字符串转换为对应的数字
UPDATE `crm_contract_receivable` SET `payment_method_new` = 
CASE 
    WHEN `payment_method` IN ('银行转账', 'transfer', 'bank_transfer', '1') THEN 1
    WHEN `payment_method` IN ('现金支付', '现金', 'cash', '2') THEN 2
    WHEN `payment_method` IN ('支票', '支票支付', 'check', '3') THEN 3
    WHEN `payment_method` IN ('支付宝', 'alipay', '4') THEN 4
    WHEN `payment_method` IN ('微信', '微信支付', 'wechat', '5') THEN 5
    WHEN `payment_method` IN ('其他', '其他方式', 'other', '6') THEN 6
    ELSE 1  -- 默认为银行转账
END;

-- 3. 检查数据迁移结果（可选，用于验证）
-- SELECT payment_method, payment_method_new, COUNT(*) as count 
-- FROM crm_contract_receivable 
-- GROUP BY payment_method, payment_method_new 
-- ORDER BY payment_method;

-- 4. 删除原字段
ALTER TABLE `crm_contract_receivable` DROP COLUMN `payment_method`;

-- 5. 重命名新字段
ALTER TABLE `crm_contract_receivable` 
CHANGE COLUMN `payment_method_new` `payment_method` 
tinyint(1) unsigned NOT NULL DEFAULT '1' 
COMMENT '付款方式:1=银行转账,2=现金,3=支票,4=支付宝,5=微信,6=其他';

-- 6. 更新索引（如果原来有的话）
-- ALTER TABLE `crm_contract_receivable` ADD INDEX `idx_payment_method` (`payment_method`);

-- 完成提示
SELECT '数据库字段更新完成：payment_method 已从 varchar(50) 更改为 tinyint(1)' as message;
