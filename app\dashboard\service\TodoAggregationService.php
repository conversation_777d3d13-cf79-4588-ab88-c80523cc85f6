<?php
declare(strict_types=1);

namespace app\dashboard\service;

use think\facade\Db;

/**
 * 待办任务聚合服务类
 */
class TodoAggregationService
{
    // 存储单例实例
    private static ?TodoAggregationService $instance = null;

    /**
     * 获取单例实例
     */
    public static function getInstance(): TodoAggregationService
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 私有构造函数防止外部实例化
     */
    private function __construct()
    {
        // 初始化逻辑
    }

    /**
     * 聚合用户待办任务
     * @param int $userId 用户ID
     * @return array
     */
    public function aggregateUserTodos(int $userId): array
    {
        $allTasks = [];

        // 获取工作流审批任务
        $workflowTasks = $this->getWorkflowTodos($userId);
        foreach ($workflowTasks as $task) {
            $allTasks[] = [
                'id' => $task['id'],
                'type' => 'workflow',
                'title' => $task['title'],
                'time' => $task['created_at'],
                'urgent' => $this->isUrgentTask($task, 'workflow'),
                'extra' => [
                    'applicant' => $task['applicant_name'] ?? '',
                    'workflow_name' => $task['workflow_name'] ?? ''
                ]
            ];
        }



        // 获取CRM客户跟进任务
        $customerFollowTasks = $this->getCustomerFollowTodos($userId);
        foreach ($customerFollowTasks as $task) {
            $allTasks[] = [
                'id' => $task['id'],
                'type' => 'crm',
                'title' => $task['title'],
                'time' => $task['next_date'],
                'urgent' => $this->isUrgentTask($task, 'crm'),
                'extra' => [
                    'customer_name' => $task['customer_name'] ?? '',
                    'follow_type' => $task['follow_type'] ?? ''
                ]
            ];
        }

        // 获取项目任务
        $projectTasks = $this->getProjectTaskTodos($userId);
        foreach ($projectTasks as $task) {
            $allTasks[] = [
                'id' => $task['id'],
                'type' => 'project',
                'title' => $task['title'],
                'time' => $task['due_date'],
                'urgent' => $this->isUrgentTask($task, 'project'),
                'extra' => [
                    'project_name' => $task['project_name'] ?? '',
                    'priority' => $task['priority'] ?? 1
                ]
            ];
        }

        // 按优先级和时间排序
        usort($allTasks, function ($a, $b) {
            // 紧急任务优先
            if ($a['urgent'] && !$b['urgent']) return -1;
            if (!$a['urgent'] && $b['urgent']) return 1;
            
            // 按时间排序（最近的在前）
            return strtotime($a['time']) - strtotime($b['time']);
        });

        return [
            'tasks' => $allTasks,
            'total_count' => count($allTasks),
            'urgent_count' => count(array_filter($allTasks, fn($task) => $task['urgent'])),
            'type_counts' => [
                'workflow' => count(array_filter($allTasks, fn($task) => $task['type'] === 'workflow')),
                'crm' => count(array_filter($allTasks, fn($task) => $task['type'] === 'crm')),
                'project' => count(array_filter($allTasks, fn($task) => $task['type'] === 'project'))
            ]
        ];
    }

    /**
     * 获取工作流待办任务
     * @param int $userId 用户ID
     * @return array
     */
    private function getWorkflowTodos(int $userId): array
    {
        try {
            $tasks = Db::name('workflow_task')
                ->alias('t')
                ->leftJoin('workflow_instance i', 't.instance_id = i.id')
                ->leftJoin('workflow_definition d', 'i.definition_id = d.id')
                ->leftJoin('system_admin a', 'i.creator_id = a.id')
                ->field('t.id, t.node_name, t.created_at, d.name as workflow_name, a.real_name as applicant_name, i.title as instance_title')
                ->where('t.approver_id', $userId)
                ->where('t.status', 0) // 待处理状态
                ->where('t.deleted_at', null)
                ->order('t.created_at', 'desc')
                ->limit(10)
                ->select()
                ->toArray();

            // 处理标题显示
            foreach ($tasks as &$task) {
                $applicant = $task['applicant_name'] ?: '某用户';
                $workflowName = $task['workflow_name'] ?: '工作流';
                $instanceTitle = $task['instance_title'] ?: '';

                if (!empty($instanceTitle)) {
                    $task['title'] = $instanceTitle;
                } else {
                    $task['title'] = $applicant . '的' . $workflowName . '申请';
                }
            }

            return $tasks;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 获取CRM客户跟进待办任务
     * @param int $userId 用户ID
     * @return array
     */
    private function getCustomerFollowTodos(int $userId): array
    {
        try {
            return Db::name('crm_follow_record')
                ->alias('f')
                ->leftJoin('crm_customer c', 'f.related_id = c.id AND f.related_type = "customer"')
                ->field('f.id, f.next_date, f.follow_type, c.customer_name,
                        CONCAT("需要跟进客户：", COALESCE(c.customer_name, "未知客户")) as title')
                ->where('f.creator_id', $userId)
                ->where('f.next_date', '>=', date('Y-m-d'))
                ->where('f.next_date', '<=', date('Y-m-d', strtotime('+7 days')))
                ->where('f.deleted_at', null)
                ->where('c.deleted_at', null)
                ->order('f.next_date', 'asc')
                ->limit(10)
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 获取项目任务待办
     * @param int $userId 用户ID
     * @return array
     */
    private function getProjectTaskTodos(int $userId): array
    {
        try {
            return Db::name('project_task')
                ->alias('t')
                ->leftJoin('project_project p', 't.project_id = p.id')
                ->field('t.id, t.title, t.due_date, t.priority, p.name as project_name')
                ->where('t.assignee_id', $userId)
                ->whereIn('t.status', [1, 2]) // 待开始、进行中
                ->where('t.deleted_at', null)
                ->where('p.deleted_at', null)
                ->order('t.due_date', 'asc')
                ->limit(10)
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 判断任务是否紧急
     * @param array $task 任务数据
     * @param string $type 任务类型
     * @return bool
     */
    private function isUrgentTask(array $task, string $type): bool
    {
        $now = time();
        
        switch ($type) {
            case 'workflow':
                // 审批任务超过24小时为紧急
                $createdTime = strtotime($task['created_at']);
                return ($now - $createdTime) > 24 * 3600;
                
            case 'crm':
                // 跟进任务当天或逾期为紧急
                $nextDate = strtotime($task['next_date']);
                return $nextDate <= strtotime(date('Y-m-d') . ' 23:59:59');
                
            case 'project':
                // 项目任务明天截止或逾期为紧急
                $dueDate = strtotime($task['due_date']);
                return $dueDate <= strtotime('+1 day');
                
            default:
                return false;
        }
    }

    /**
     * 获取任务类型文本
     * @param string $type 任务类型
     * @return string
     */
    public function getTaskTypeText(string $type): string
    {
        $types = [
            'workflow' => '审批',
            'crm' => '跟进',
            'project' => '任务'
        ];
        return $types[$type] ?? '其他';
    }

    /**
     * 获取任务类型颜色
     * @param string $type 任务类型
     * @return string
     */
    public function getTaskTypeColor(string $type): string
    {
        $colors = [
            'workflow' => 'warning',
            'crm' => 'primary',
            'project' => 'success'
        ];
        return $colors[$type] ?? 'info';
    }

    /**
     * 格式化时间显示
     * @param string $time 时间字符串
     * @return string
     */
    public function formatTime(string $time): string
    {
        $timestamp = strtotime($time);
        $now = time();
        $diff = $now - $timestamp;
        
        if ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes <= 0 ? '刚刚' : $minutes . '分钟前';
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . '小时前';
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return $days . '天前';
        } else {
            return date('m-d H:i', $timestamp);
        }
    }
}
