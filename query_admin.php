<?php
$pdo = new PDO('mysql:host=*************;port=3306;dbname=www_bs_com;charset=utf8mb4', 'www_bs_com', 'PdadjMXmNy8Pn9tj');

echo "=== system_admin 表结构 ===\n";
$stmt = $pdo->query("DESCRIBE system_admin");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

printf("%-20s %-30s %-10s %-10s %-15s %-20s\n", 
       "Field", "Type", "Null", "Key", "Default", "Extra");
echo str_repeat("-", 110) . "\n";

foreach ($columns as $column) {
    printf("%-20s %-30s %-10s %-10s %-15s %-20s\n",
           $column['Field'],
           $column['Type'],
           $column['Null'],
           $column['Key'],
           $column['Default'] ?? 'NULL',
           $column['Extra']
    );
}

// 查询用户数据示例
echo "\n=== 用户示例数据 ===\n";
$stmt = $pdo->query("SELECT id, username, real_name, avatar, gender, mobile FROM system_admin WHERE deleted_at IS NULL LIMIT 5");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

printf("%-5s %-15s %-15s %-20s %-8s %-15s\n", 
       "ID", "Username", "Real Name", "Avatar", "Gender", "Mobile");
echo str_repeat("-", 80) . "\n";

foreach ($users as $user) {
    printf("%-5s %-15s %-15s %-20s %-8s %-15s\n",
           $user['id'],
           $user['username'],
           $user['real_name'],
           substr($user['avatar'] ?? 'NULL', 0, 20),
           $user['gender'] ?? 'NULL',
           $user['mobile'] ?? 'NULL'
    );
}
?>
