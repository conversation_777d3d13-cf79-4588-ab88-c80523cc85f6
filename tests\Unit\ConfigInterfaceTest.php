<?php
declare(strict_types=1);

namespace tests\Unit;

use PHPUnit\Framework\TestCase;

// 定义测试环境常量
if (!defined('PHPUNIT_RUNNING')) {
    define('PHPUNIT_RUNNING', true);
}

/**
 * 配置界面功能测试
 */
class ConfigInterfaceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // 模拟租户环境
        if (!function_exists('get_tenant_id')) {
            function get_tenant_id() {
                return 1;
            }
        }
    }
    
    /**
     * 测试TenantConfigController是否支持新配置类型
     */
    public function testTenantConfigControllerSupportsNewTypes(): void
    {
        $controllerFile = __DIR__ . '/../../app/system/controller/tenant/TenantConfigController.php';
        $this->assertFileExists($controllerFile);
        
        $content = file_get_contents($controllerFile);
        
        // 检查是否包含新的配置类型
        $this->assertStringContainsString('advance_notice', $content);
        $this->assertStringContainsString('task_reminder', $content);
        
        // 检查配置数组结构
        $this->assertStringContainsString('$configArr = [', $content);
    }
    
    /**
     * 测试TenantConfigService是否有saveInfo方法
     */
    public function testTenantConfigServiceHasSaveInfoMethod(): void
    {
        $serviceFile = __DIR__ . '/../../app/system/service/TenantConfigService.php';
        $this->assertFileExists($serviceFile);
        
        $content = file_get_contents($serviceFile);
        
        // 检查是否有saveInfo方法
        $this->assertStringContainsString('public function saveInfo', $content);
        $this->assertStringContainsString('return $this->create($group, $params);', $content);
    }
    
    /**
     * 测试前端页面是否包含新的Tab页
     */
    public function testFrontendPageHasNewTabs(): void
    {
        $vueFile = __DIR__ . '/../../frontend/src/views/tenant/detail.vue';
        $this->assertFileExists($vueFile);
        
        $content = file_get_contents($vueFile);
        
        // 检查是否包含新的Tab页
        $this->assertStringContainsString('label="提前通知"', $content);
        $this->assertStringContainsString('label="任务提醒"', $content);
        $this->assertStringContainsString('name="advance_notice"', $content);
        $this->assertStringContainsString('name="task_reminder"', $content);
    }
    
    /**
     * 测试前端页面是否包含配置数据结构
     */
    public function testFrontendPageHasConfigDataStructure(): void
    {
        $vueFile = __DIR__ . '/../../frontend/src/views/tenant/detail.vue';
        $content = file_get_contents($vueFile);
        
        // 检查提前通知配置数据结构
        $this->assertStringContainsString('const advanceNotice = ref({', $content);
        $this->assertStringContainsString('notice_enabled:', $content);
        $this->assertStringContainsString('notify_days:', $content);
        $this->assertStringContainsString('notice_frequency:', $content);
        $this->assertStringContainsString('notice_template:', $content);
        
        // 检查任务提醒配置数据结构
        $this->assertStringContainsString('const taskReminder = ref({', $content);
        $this->assertStringContainsString('reminder_enabled:', $content);
        $this->assertStringContainsString('overdue_enabled:', $content);
        $this->assertStringContainsString('due_soon_enabled:', $content);
        $this->assertStringContainsString('follow_up_enabled:', $content);
    }
    
    /**
     * 测试前端页面是否包含编辑方法
     */
    public function testFrontendPageHasEditMethods(): void
    {
        $vueFile = __DIR__ . '/../../frontend/src/views/tenant/detail.vue';
        $content = file_get_contents($vueFile);
        
        // 检查编辑方法
        $this->assertStringContainsString('const editAdvanceNoticeClick', $content);
        $this->assertStringContainsString('const editTaskReminderClick', $content);
        
        // 检查编辑状态控制
        $this->assertStringContainsString('const isEditAdvanceNotice', $content);
        $this->assertStringContainsString('const isEditTaskReminder', $content);
    }
    
    /**
     * 测试配置表单字段
     */
    public function testConfigFormFields(): void
    {
        $vueFile = __DIR__ . '/../../frontend/src/views/tenant/detail.vue';
        $content = file_get_contents($vueFile);
        
        // 提前通知表单字段
        $this->assertStringContainsString('v-model="advanceNotice.notice_enabled"', $content);
        $this->assertStringContainsString('v-model="advanceNotice.notify_days"', $content);
        $this->assertStringContainsString('v-model="advanceNotice.notice_frequency"', $content);
        $this->assertStringContainsString('v-model="advanceNotice.notice_target"', $content);
        $this->assertStringContainsString('v-model="advanceNotice.notice_template"', $content);
        
        // 任务提醒表单字段
        $this->assertStringContainsString('v-model="taskReminder.reminder_enabled"', $content);
        $this->assertStringContainsString('v-model="taskReminder.overdue_enabled"', $content);
        $this->assertStringContainsString('v-model="taskReminder.due_soon_enabled"', $content);
        $this->assertStringContainsString('v-model="taskReminder.follow_up_enabled"', $content);
    }
    
    /**
     * 测试配置选项值
     */
    public function testConfigOptionValues(): void
    {
        $vueFile = __DIR__ . '/../../frontend/src/views/tenant/detail.vue';
        $content = file_get_contents($vueFile);
        
        // 通知频率选项
        $this->assertStringContainsString('value="once"', $content);
        $this->assertStringContainsString('value="daily"', $content);
        $this->assertStringContainsString('value="every_2_days"', $content);
        
        // 通知对象选项
        $this->assertStringContainsString('value="owner"', $content);
        $this->assertStringContainsString('value="manager"', $content);
        $this->assertStringContainsString('value="both"', $content);
        
        // 逾期提醒频率选项
        $this->assertStringContainsString('value="weekly"', $content);
    }
    
    /**
     * 测试表单提示信息
     */
    public function testFormTips(): void
    {
        $vueFile = __DIR__ . '/../../frontend/src/views/tenant/detail.vue';
        $content = file_get_contents($vueFile);
        
        // 检查表单提示
        $this->assertStringContainsString('class="form-tip"', $content);
        $this->assertStringContainsString('客户即将被回收到公海前多少天发送提前通知', $content);
        $this->assertStringContainsString('设置通知的发送频率', $content);
        $this->assertStringContainsString('对已逾期的任务发送提醒通知', $content);
    }
    
    /**
     * 测试模板变量支持
     */
    public function testTemplateVariableSupport(): void
    {
        $vueFile = __DIR__ . '/../../frontend/src/views/tenant/detail.vue';
        $content = file_get_contents($vueFile);
        
        // 检查模板变量提示
        $this->assertStringContainsString('{{customer_name}}', $content);
        $this->assertStringContainsString('{{days}}', $content);
        $this->assertStringContainsString('{{owner_name}}', $content);
        $this->assertStringContainsString('{{last_follow_date}}', $content);
    }
    
    /**
     * 测试配置数据初始化
     */
    public function testConfigDataInitialization(): void
    {
        // 测试提前通知默认配置
        $advanceNoticeDefaults = [
            'notice_enabled' => false,
            'notify_days' => 3,
            'notice_frequency' => 'once',
            'notice_target' => 'owner',
            'notice_template' => '您负责的客户 {{customer_name}} 将在 {{days}} 天后被回收到公海，请及时跟进。'
        ];
        
        foreach ($advanceNoticeDefaults as $key => $value) {
            $this->assertIsString($key);
            $this->assertNotNull($value);
        }
        
        // 测试任务提醒默认配置
        $taskReminderDefaults = [
            'reminder_enabled' => false,
            'overdue_enabled' => true,
            'overdue_frequency' => 'daily',
            'due_soon_enabled' => true,
            'due_soon_days' => [1, 3, 7],
            'follow_up_enabled' => true,
            'follow_up_advance_hours' => 2
        ];
        
        foreach ($taskReminderDefaults as $key => $value) {
            $this->assertIsString($key);
            $this->assertNotNull($value);
        }
        
        // 验证due_soon_days是数组
        $this->assertIsArray($taskReminderDefaults['due_soon_days']);
        $this->assertCount(3, $taskReminderDefaults['due_soon_days']);
    }
    
    /**
     * 测试API调用结构
     */
    public function testApiCallStructure(): void
    {
        $vueFile = __DIR__ . '/../../frontend/src/views/tenant/detail.vue';
        $content = file_get_contents($vueFile);
        
        // 检查API调用
        $this->assertStringContainsString('TenantConfigApi.saveTenantConfig', $content);
        $this->assertStringContainsString("group: 'advance_notice'", $content);
        $this->assertStringContainsString("group: 'task_reminder'", $content);
        $this->assertStringContainsString('config: advanceNotice.value', $content);
        $this->assertStringContainsString('config: taskReminder.value', $content);
    }
}
