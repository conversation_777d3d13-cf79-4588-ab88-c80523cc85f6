<?php
declare(strict_types=1);

namespace tests\Unit;

use PHPUnit\Framework\TestCase;

// 定义测试环境常量
if (!defined('PHPUNIT_RUNNING')) {
    define('PHPUNIT_RUNNING', true);
}

/**
 * WebSocket实时通知优化测试
 */
class WebSocketOptimizationTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // 模拟租户环境
        if (!function_exists('get_tenant_id')) {
            function get_tenant_id() {
                return 1;
            }
        }
    }
    
    /**
     * 测试UnifiedWorker是否包含心跳检测功能
     */
    public function testUnifiedWorkerHasHeartbeatFeature(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $this->assertFileExists($workerFile);
        
        $content = file_get_contents($workerFile);
        
        // 检查心跳相关属性
        $this->assertStringContainsString('private array $connectionHeartbeats', $content);
        
        // 检查心跳检测定时器
        $this->assertStringContainsString('heartbeat_check', $content);
        $this->assertStringContainsString('checkHeartbeat', $content);
        
        // 检查心跳处理方法
        $this->assertStringContainsString('public function checkHeartbeat', $content);
        $this->assertStringContainsString('handleHeartbeat', $content);
    }
    
    /**
     * 测试WebSocket消息处理优化
     */
    public function testWebSocketMessageHandlingOptimization(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查消息类型处理
        $this->assertStringContainsString('switch ($message[\'type\'])', $content);
        $this->assertStringContainsString('case \'auth\':', $content);
        $this->assertStringContainsString('case \'heartbeat\':', $content);
        $this->assertStringContainsString('case \'mark_read\':', $content);
        $this->assertStringContainsString('case \'get_notifications\':', $content);
        
        // 检查处理方法
        $this->assertStringContainsString('handleAuth', $content);
        $this->assertStringContainsString('handleHeartbeat', $content);
        $this->assertStringContainsString('handleMarkRead', $content);
        $this->assertStringContainsString('handleGetNotifications', $content);
    }
    
    /**
     * 测试系统通知发送方法
     */
    public function testSystemNoticeMethod(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查系统通知方法
        $this->assertStringContainsString('private function sendSystemNotice', $content);
        $this->assertStringContainsString('NoticeDispatcherService::getInstance()', $content);
        $this->assertStringContainsString('system\', \'notice\'', $content);
        
        // 检查推送方法
        $this->assertStringContainsString('pushNotificationToOnlineUsers', $content);
        $this->assertStringContainsString('new_notification', $content);
    }
    
    /**
     * 测试连接管理优化
     */
    public function testConnectionManagementOptimization(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查连接认证
        $this->assertStringContainsString('validateUserToken', $content);
        $this->assertStringContainsString('last_heartbeat', $content);
        
        // 检查连接关闭处理
        $this->assertStringContainsString('onClose', $content);
        $this->assertStringContainsString('unset($this->connectionHeartbeats', $content);
        
        // 检查错误处理
        $this->assertStringContainsString('sendError', $content);
        $this->assertStringContainsString('sendSuccess', $content);
    }
    
    /**
     * 测试响应消息格式
     */
    public function testResponseMessageFormat(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查响应格式
        $this->assertStringContainsString('\'type\' => $type', $content);
        $this->assertStringContainsString('\'success\' => true', $content);
        $this->assertStringContainsString('\'data\' => $data', $content);
        $this->assertStringContainsString('\'timestamp\' => time()', $content);
        
        // 检查错误响应格式
        $this->assertStringContainsString('\'success\' => false', $content);
        $this->assertStringContainsString('\'message\' => $message', $content);
    }
    
    /**
     * 测试通知服务集成
     */
    public function testNotificationServiceIntegration(): void
    {
        // 检查AdvanceNoticeService
        $advanceNoticeFile = __DIR__ . '/../../workerman/services/AdvanceNoticeService.php';
        $this->assertFileExists($advanceNoticeFile);
        
        $content = file_get_contents($advanceNoticeFile);
        $this->assertStringContainsString('NoticeDispatcherService::getInstance()', $content);
        $this->assertStringContainsString('system\', \'notice\'', $content);
        
        // 检查TaskReminderService
        $taskReminderFile = __DIR__ . '/../../workerman/services/TaskReminderService.php';
        $this->assertFileExists($taskReminderFile);
        
        $content = file_get_contents($taskReminderFile);
        $this->assertStringContainsString('NoticeDispatcherService::getInstance()', $content);
        $this->assertStringContainsString('system\', \'notice\'', $content);
    }
    
    /**
     * 测试定时器配置
     */
    public function testTimerConfiguration(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查定时器数量（应该有7个定时器）
        $timerCount = substr_count($content, 'Timer::add(');
        $this->assertGreaterThanOrEqual(7, $timerCount);
        
        // 检查心跳检测定时器（30秒）
        $this->assertStringContainsString('Timer::add(30,', $content);
        
        // 检查任务提醒定时器（30分钟 = 1800秒）
        $this->assertStringContainsString('Timer::add(1800,', $content);
    }
    
    /**
     * 测试内存监控优化
     */
    public function testMemoryMonitoringOptimization(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查内存监控
        $this->assertStringContainsString('logMemoryUsage', $content);
        $this->assertStringContainsString('connections\' => count($this->userConnections)', $content);
        $this->assertStringContainsString('heartbeats\' => count($this->connectionHeartbeats)', $content);
    }
    
    /**
     * 测试通知模板使用
     */
    public function testNotificationTemplateUsage(): void
    {
        // 测试提前通知服务
        $advanceNoticeFile = __DIR__ . '/../../workerman/services/AdvanceNoticeService.php';
        $content = file_get_contents($advanceNoticeFile);
        
        // 检查模板变量替换
        $this->assertStringContainsString('{{customer_name}}', $content);
        $this->assertStringContainsString('{{days}}', $content);
        $this->assertStringContainsString('{{owner_name}}', $content);
        $this->assertStringContainsString('str_replace', $content);
        
        // 测试任务提醒服务
        $taskReminderFile = __DIR__ . '/../../workerman/services/TaskReminderService.php';
        $content = file_get_contents($taskReminderFile);
        
        // 检查任务模板变量
        $this->assertStringContainsString('{{task_name}}', $content);
        $this->assertStringContainsString('{{due_date}}', $content);
        $this->assertStringContainsString('{{days}}', $content);
    }
    
    /**
     * 测试通知发送选项
     */
    public function testNotificationSendOptions(): void
    {
        $advanceNoticeFile = __DIR__ . '/../../workerman/services/AdvanceNoticeService.php';
        $content = file_get_contents($advanceNoticeFile);
        
        // 检查发送选项
        $this->assertStringContainsString('send_channels', $content);
        $this->assertStringContainsString('priority', $content);
        $this->assertStringContainsString('creator_id', $content);
        
        $taskReminderFile = __DIR__ . '/../../workerman/services/TaskReminderService.php';
        $content = file_get_contents($taskReminderFile);
        
        // 检查任务提醒发送
        $this->assertStringContainsString('title\' => $title', $content);
        $this->assertStringContainsString('content\' => $content', $content);
    }
    
    /**
     * 测试错误处理机制
     */
    public function testErrorHandlingMechanism(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查异常处理
        $this->assertStringContainsString('try {', $content);
        $this->assertStringContainsString('} catch (\\Throwable $e) {', $content);
        $this->assertStringContainsString('$this->log(', $content);
        
        // 检查错误响应
        $this->assertStringContainsString('sendError', $content);
        $this->assertStringContainsString('无效的消息格式', $content);
        $this->assertStringContainsString('不支持的消息类型', $content);
    }
    
    /**
     * 测试日志记录优化
     */
    public function testLoggingOptimization(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查日志记录
        $this->assertStringContainsString('用户认证成功', $content);
        $this->assertStringContainsString('用户断开连接', $content);
        $this->assertStringContainsString('用户连接超时', $content);
        $this->assertStringContainsString('系统通知发送成功', $content);
        
        // 检查错误日志
        $this->assertStringContainsString('WebSocket消息处理异常', $content);
        $this->assertStringContainsString('发送系统通知异常', $content);
    }
    
    /**
     * 测试配置验证
     */
    public function testConfigurationValidation(): void
    {
        // 测试心跳超时配置
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查超时配置
        $this->assertStringContainsString('$timeout = 90', $content); // 90秒超时
        
        // 检查定时器间隔配置
        $this->assertStringContainsString('Timer::add(30,', $content); // 30秒心跳检测
        $this->assertStringContainsString('Timer::add(1800,', $content); // 30分钟任务提醒
    }
    
    /**
     * 测试WebSocket功能完整性
     */
    public function testWebSocketFunctionalityCompleteness(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查WebSocket事件处理方法
        $this->assertStringContainsString('public function onMessage', $content);
        $this->assertStringContainsString('public function onClose', $content);
        $this->assertStringContainsString('public function onError', $content);
        
        // 检查用户连接管理
        $this->assertStringContainsString('$this->userConnections', $content);
        $this->assertStringContainsString('$connection->user_id', $content);
        
        // 检查消息发送
        $this->assertStringContainsString('$connection->send', $content);
        $this->assertStringContainsString('json_encode', $content);
    }
}
