<?php
$pdo = new PDO('mysql:host=*************;port=3306;dbname=www_bs_com;charset=utf8mb4', 'www_bs_com', 'PdadjMXmNy8Pn9tj');

echo "=== project_task_record 表结构 ===\n";
$stmt = $pdo->query("DESCRIBE project_task_record");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

printf("%-20s %-30s %-10s %-10s %-15s %-20s\n", 
       "Field", "Type", "Null", "Key", "Default", "Extra");
echo str_repeat("-", 110) . "\n";

foreach ($columns as $column) {
    printf("%-20s %-30s %-10s %-10s %-15s %-20s\n",
           $column['Field'],
           $column['Type'],
           $column['Null'],
           $column['Key'],
           $column['Default'] ?? 'NULL',
           $column['Extra']
    );
}

// 查询数据量
$stmt = $pdo->query("SELECT COUNT(*) as count FROM project_task_record");
$count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
echo "\n数据量: $count 条记录\n";

// 示例数据
if ($count > 0) {
    echo "\n示例数据 (前3条):\n";
    $stmt = $pdo->query("SELECT * FROM project_task_record LIMIT 3");
    $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($samples)) {
        $fields = array_keys($samples[0]);
        echo implode(" | ", array_map(function($field) {
            return str_pad($field, 12);
        }, $fields)) . "\n";
        echo str_repeat("-", count($fields) * 15) . "\n";
        
        foreach ($samples as $row) {
            echo implode(" | ", array_map(function($value) {
                return str_pad(substr($value ?? 'NULL', 0, 12), 12);
            }, $row)) . "\n";
        }
    }
}
?>
