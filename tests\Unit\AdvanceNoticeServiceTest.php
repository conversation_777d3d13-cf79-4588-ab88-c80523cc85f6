<?php
declare(strict_types=1);

namespace tests\Unit;

use PHPUnit\Framework\TestCase;

// 定义测试环境常量
if (!defined('PHPUNIT_RUNNING')) {
    define('PHPUNIT_RUNNING', true);
}

/**
 * 提前通知服务单元测试
 */
class AdvanceNoticeServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // 模拟租户环境
        if (!function_exists('get_tenant_id')) {
            function get_tenant_id() {
                return 1;
            }
        }
    }
    
    /**
     * 测试AdvanceNoticeService类是否存在
     */
    public function testAdvanceNoticeServiceExists(): void
    {
        require_once __DIR__ . '/../../workerman/services/AdvanceNoticeService.php';
        
        $this->assertTrue(class_exists('\workerman\services\AdvanceNoticeService'));
    }
    
    /**
     * 测试AdvanceNoticeService的基本结构
     */
    public function testAdvanceNoticeServiceStructure(): void
    {
        require_once __DIR__ . '/../../workerman/common/WorkerBase.php';
        require_once __DIR__ . '/../../workerman/services/AdvanceNoticeService.php';
        
        $reflection = new \ReflectionClass('\workerman\services\AdvanceNoticeService');
        
        // 检查继承关系
        $this->assertTrue($reflection->isSubclassOf('\workerman\common\WorkerBase'));
        
        // 检查关键方法是否存在
        $this->assertTrue($reflection->hasMethod('executeAdvanceNotice'));
        $this->assertTrue($reflection->hasMethod('onWorkerStart'));
        
        // 检查方法的可见性
        $executeMethod = $reflection->getMethod('executeAdvanceNotice');
        $this->assertTrue($executeMethod->isPublic());
    }
    
    /**
     * 测试配置验证逻辑
     */
    public function testConfigValidation(): void
    {
        // 测试配置结构
        $validConfig = [
            'notice_enabled' => true,
            'notify_days' => 3,
            'notice_channels' => ['site'],
            'notice_target' => 'owner',
            'notice_frequency' => 'daily',
            'notice_template' => '您负责的客户 {{customer_name}} 将在 {{days}} 天后被回收到公海，请及时跟进。'
        ];
        
        // 验证配置键是否完整
        $this->assertArrayHasKey('notice_enabled', $validConfig);
        $this->assertArrayHasKey('notify_days', $validConfig);
        $this->assertArrayHasKey('notice_channels', $validConfig);
        $this->assertArrayHasKey('notice_target', $validConfig);
        $this->assertArrayHasKey('notice_frequency', $validConfig);
        $this->assertArrayHasKey('notice_template', $validConfig);
        
        // 验证配置值类型
        $this->assertIsBool($validConfig['notice_enabled']);
        $this->assertIsInt($validConfig['notify_days']);
        $this->assertIsArray($validConfig['notice_channels']);
        $this->assertIsString($validConfig['notice_target']);
        $this->assertIsString($validConfig['notice_frequency']);
        $this->assertIsString($validConfig['notice_template']);
    }
    
    /**
     * 测试通知频率选项
     */
    public function testNoticeFrequencyOptions(): void
    {
        $validFrequencies = ['once', 'daily', 'every_2_days'];
        
        foreach ($validFrequencies as $frequency) {
            $this->assertIsString($frequency);
            $this->assertNotEmpty($frequency);
        }
        
        // 测试频率逻辑
        $this->assertEquals('once', $validFrequencies[0]); // 仅通知一次
        $this->assertEquals('daily', $validFrequencies[1]); // 每天通知
        $this->assertEquals('every_2_days', $validFrequencies[2]); // 每2天通知
    }
    
    /**
     * 测试通知目标选项
     */
    public function testNoticeTargetOptions(): void
    {
        $validTargets = ['owner', 'manager', 'both'];
        
        foreach ($validTargets as $target) {
            $this->assertIsString($target);
            $this->assertNotEmpty($target);
        }
        
        // 测试目标逻辑
        $this->assertEquals('owner', $validTargets[0]); // 客户负责人
        $this->assertEquals('manager', $validTargets[1]); // 部门经理
        $this->assertEquals('both', $validTargets[2]); // 两者都通知
    }
    
    /**
     * 测试模板变量替换
     */
    public function testTemplateVariables(): void
    {
        $template = '您负责的客户 {{customer_name}} 将在 {{days}} 天后被回收到公海，请及时跟进。最后跟进时间：{{last_follow_date}}';
        
        // 检查模板变量
        $this->assertStringContainsString('{{customer_name}}', $template);
        $this->assertStringContainsString('{{days}}', $template);
        $this->assertStringContainsString('{{last_follow_date}}', $template);
        
        // 模拟变量替换
        $customerData = [
            'customer_name' => '测试客户',
            'days_until_recycle' => 3,
            'last_follow_date' => '2025-08-01'
        ];
        
        $content = str_replace([
            '{{customer_name}}',
            '{{days}}',
            '{{last_follow_date}}'
        ], [
            $customerData['customer_name'],
            $customerData['days_until_recycle'],
            $customerData['last_follow_date']
        ], $template);
        
        $expectedContent = '您负责的客户 测试客户 将在 3 天后被回收到公海，请及时跟进。最后跟进时间：2025-08-01';
        $this->assertEquals($expectedContent, $content);
    }
    
    /**
     * 测试日期计算逻辑
     */
    public function testDateCalculation(): void
    {
        // 测试今天的日期格式
        $today = date('Y-m-d');
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $today);
        
        // 测试2天前的日期
        $twoDaysAgo = date('Y-m-d', strtotime('-2 days'));
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $twoDaysAgo);
        
        // 验证日期逻辑
        $todayTimestamp = strtotime($today);
        $twoDaysAgoTimestamp = strtotime($twoDaysAgo);
        $this->assertGreaterThan($twoDaysAgoTimestamp, $todayTimestamp);
        
        // 验证天数差
        $daysDiff = ($todayTimestamp - $twoDaysAgoTimestamp) / (24 * 3600);
        $this->assertEquals(2, $daysDiff);
    }
    
    /**
     * 测试数据库表结构（模拟）
     */
    public function testAdvanceNoticeLogTableStructure(): void
    {
        // 模拟数据库记录结构
        $logRecord = [
            'id' => 1,
            'customer_id' => 123,
            'notice_date' => '2025-08-04',
            'days_before_recycle' => 3,
            'sent_at' => '2025-08-04 10:30:00',
            'tenant_id' => 1
        ];
        
        // 验证记录结构
        $this->assertArrayHasKey('id', $logRecord);
        $this->assertArrayHasKey('customer_id', $logRecord);
        $this->assertArrayHasKey('notice_date', $logRecord);
        $this->assertArrayHasKey('days_before_recycle', $logRecord);
        $this->assertArrayHasKey('sent_at', $logRecord);
        $this->assertArrayHasKey('tenant_id', $logRecord);
        
        // 验证数据类型
        $this->assertIsInt($logRecord['id']);
        $this->assertIsInt($logRecord['customer_id']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $logRecord['notice_date']);
        $this->assertIsInt($logRecord['days_before_recycle']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $logRecord['sent_at']);
        $this->assertIsInt($logRecord['tenant_id']);
    }
    
    /**
     * 测试服务文件的语法正确性
     */
    public function testServiceFileSyntax(): void
    {
        $serviceFile = __DIR__ . '/../../workerman/services/AdvanceNoticeService.php';
        $this->assertFileExists($serviceFile);
        
        $content = file_get_contents($serviceFile);
        
        // 检查基本语法结构
        $this->assertStringContainsString('<?php', $content);
        $this->assertStringContainsString('declare(strict_types=1);', $content);
        $this->assertStringContainsString('namespace workerman\services;', $content);
        $this->assertStringContainsString('class AdvanceNoticeService extends WorkerBase', $content);
        
        // 检查关键方法
        $this->assertStringContainsString('public function executeAdvanceNotice', $content);
        $this->assertStringContainsString('private function getCustomersNearRecycle', $content);
        $this->assertStringContainsString('private function shouldSendNotice', $content);
        $this->assertStringContainsString('private function sendAdvanceNotice', $content);
        $this->assertStringContainsString('private function recordNoticeLog', $content);
    }
}
