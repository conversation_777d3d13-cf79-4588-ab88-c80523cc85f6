# 工作流作废通知修复与架构冲突分析报告

## 📋 修复概述

**修复日期**: 2025-01-24  
**修复类型**: 通知机制完善 + 架构冲突分析  
**影响范围**: 工作流作废功能 + 新旧架构并存问题  
**修复状态**: ✅ 已完成

## 🔧 作废通知修复实施

### **问题描述**
工作流有两个作废入口都没有成功发送作废通知：
1. **独立业务场景**（如每日报价）：通过 `UnifiedWorkflowService` → `WorkflowStatusSyncService`
2. **申请列表场景**：通过 `ApplicationController` → `WorkflowStatusSyncService`

### **根本原因**
`WorkflowStatusSyncService.syncAllWorkflowStatus()` 方法只负责状态同步，缺失通知发送逻辑。

### **修复方案**
采用**方案1**：在 `WorkflowStatusSyncService` 中添加通知发送逻辑。

#### **修复内容**

##### 1. 添加必要的导入
```php
use app\notice\service\NoticeDispatcherService;
use app\workflow\service\WorkflowUrlService;
use app\system\model\Admin as AdminModel;
```

##### 2. 在状态同步方法中添加通知调用
```php
// 7. 发送状态变更通知（在事务提交前）
$this->sendStatusChangeNotification($instance, $newStatus, $reason, $operatorId);
```

##### 3. 实现通知发送方法
```php
private function sendStatusChangeNotification($instance, int $newStatus, string $reason, int $operatorId): bool
{
    // 只处理作废状态的通知
    if ($newStatus !== WorkflowStatusConstant::STATUS_VOID) {
        return true;
    }
    
    // 获取提交人和操作人信息
    // 发送工作流作废通知
    $result = $noticeService->send(
        WorkflowStatusConstant::MODULE_NAME, 
        'task_void', 
        $variables, 
        [$submitterId], 
        $options
    );
}
```

### **修复效果**
- ✅ **每日报价作废**：现在会正确发送作废通知
- ✅ **申请页面作废**：现在会正确发送作废通知
- ✅ **事务安全**：通知发送在事务内，确保一致性
- ✅ **异常处理**：通知发送失败不影响主流程

## 🏗️ 新旧架构冲突深度分析

### **架构现状**

#### **新架构**（统一工作流服务）
```
业务Controller → UnifiedWorkflowService → WorkflowStatusSyncService
                                        ↓
                                FormService.afterWorkflowStatusChange
```

**使用模块**：
- ✅ **每日报价**：`DailyPriceOrderService`
- ✅ **申请列表**：`ApplicationController`

#### **旧架构**（直接工作流引擎）
```
业务Controller → WorkflowEngineService → WorkflowEngine
                                        ↓
                                  直接调用通知发送
```

**使用模块**：
- ⚠️ **CRM合同**：`CrmContractService.voidContract()`
- ⚠️ **CRM回款**：`CrmContractReceivableService.voidReceivable()`

### **架构冲突问题**

#### **1. 通知发送机制不统一**

| 架构类型 | 通知发送方式 | 状态 |
|---------|-------------|------|
| **新架构** | WorkflowStatusSyncService.sendStatusChangeNotification() | ✅ 已修复 |
| **旧架构** | WorkflowEngine.sendVoidNotification() | ✅ 正常 |

**冲突影响**：
- 用户体验不一致：不同模块的通知格式和时机可能不同
- 维护复杂度高：需要维护两套通知机制

#### **2. 状态同步逻辑差异**

**新架构流程**：
```
1. WorkflowStatusSyncService.syncAllWorkflowStatus()
2. 更新 workflow_instance 状态
3. 更新 workflow_task 状态  
4. 调用 FormService.afterWorkflowStatusChange()
5. 发送通知（新增）
```

**旧架构流程**：
```
1. WorkflowEngine.voidApprovedInstance()
2. 更新 workflow_instance 状态
3. 发送通知
4. 可能不调用 FormService.afterWorkflowStatusChange()
```

**冲突影响**：
- 业务逻辑处理不一致
- 数据同步可能存在差异

#### **3. 权限控制差异**

**新架构权限控制**：
- 通过 `UnifiedWorkflowService` 统一验证
- 基于 `FormService` 的业务权限检查

**旧架构权限控制**：
- 直接在业务 Service 中验证
- 可能存在权限检查不一致

### **独立业务与申请列表场景差异**

#### **独立业务场景**（CRM、每日报价）
**特点**：
- 有独立的管理页面和业务逻辑
- 复杂的业务状态和权限控制
- 需要深度集成工作流功能

**架构选择**：
- **每日报价**：已迁移到新架构 ✅
- **CRM模块**：仍使用旧架构 ⚠️

#### **申请列表场景**（HR请假等）
**特点**：
- 标准化的审批流程
- 统一的申请页面管理
- 相对简单的业务逻辑

**架构选择**：
- **统一使用新架构** ✅

### **功能冲突具体表现**

#### **1. 用户体验不一致**
- **通知内容差异**：新旧架构的通知模板和变量可能不同
- **操作反馈差异**：成功/失败提示的格式不统一
- **权限提示差异**：权限不足时的提示信息不一致

#### **2. 开发维护复杂**
- **双重维护负担**：需要同时维护两套架构
- **新功能适配困难**：新功能需要考虑两套架构的兼容性
- **问题排查复杂**：不同模块的问题排查路径不同

#### **3. 数据一致性风险**
- **状态同步差异**：新旧架构的状态同步逻辑可能不完全一致
- **历史记录差异**：操作历史的记录方式可能不同
- **业务数据处理差异**：FormService 调用的差异可能导致业务数据不一致

## 🎯 架构统一建议

### **短期方案**（立即实施）
1. **保持现状**：新旧架构并存，确保功能正常
2. **统一通知**：确保两套架构的通知格式和内容一致
3. **文档完善**：明确标注各模块使用的架构类型

### **中期方案**（3-6个月）
1. **CRM模块迁移**：将CRM合同和回款模块迁移到新架构
2. **接口统一**：统一所有模块的工作流操作接口
3. **测试验证**：全面测试迁移后的功能完整性

### **长期方案**（6-12个月）
1. **完全统一**：所有模块使用统一的新架构
2. **旧架构清理**：移除旧架构相关代码
3. **性能优化**：基于统一架构进行性能优化

## ⚠️ 风险控制

### **迁移风险**
- **功能回归风险**：迁移过程中可能影响现有功能
- **数据一致性风险**：迁移过程中需要确保数据完整性
- **用户体验风险**：迁移可能暂时影响用户操作

### **风险缓解措施**
1. **分阶段迁移**：逐个模块进行迁移，降低风险
2. **充分测试**：每个阶段都进行全面测试
3. **回滚方案**：准备快速回滚机制
4. **用户培训**：提前通知用户可能的变化

## 📊 总结

### **已完成**
- ✅ 修复了作废通知发送问题
- ✅ 统一了新架构的通知机制
- ✅ 分析了新旧架构的冲突问题

### **待解决**
- ⚠️ CRM模块仍使用旧架构
- ⚠️ 新旧架构并存导致的维护复杂性
- ⚠️ 用户体验不一致问题

### **建议优先级**
1. **高优先级**：确保当前功能稳定运行
2. **中优先级**：规划CRM模块的架构迁移
3. **低优先级**：完全统一架构并清理旧代码

本次修复解决了作废通知的核心问题，同时为后续的架构统一提供了清晰的路径和建议。

## 📋 详细架构对比分析

### **调用链路对比**

#### **新架构调用链路**
```
前端操作 → Controller → UnifiedWorkflowService
    ↓
executeWorkflowOperation('void', params)
    ↓
voidWorkflow(params)
    ↓
WorkflowStatusSyncService.syncAllWorkflowStatus()
    ↓
1. 更新 workflow_instance 状态
2. 更新 workflow_task 状态
3. 更新业务表状态
4. 调用 FormService.afterWorkflowStatusChange()
5. 记录操作历史
6. 发送状态变更通知 ← 新增
7. 提交事务
```

#### **旧架构调用链路**
```
前端操作 → Controller → BusinessService
    ↓
voidContract/voidReceivable()
    ↓
WorkflowEngineService.voidApprovedInstance()
    ↓
WorkflowEngine.voidApprovedInstance()
    ↓
1. 更新 workflow_instance 状态
2. 发送作废通知
3. 提交事务
```

### **关键差异点**

#### **1. 业务逻辑处理**
- **新架构**：通过 `FormService.afterWorkflowStatusChange()` 统一处理
- **旧架构**：在各自的业务Service中分散处理

#### **2. 状态同步范围**
- **新架构**：同步 workflow_instance + workflow_task + 业务表
- **旧架构**：主要同步 workflow_instance

#### **3. 通知发送时机**
- **新架构**：在状态同步完成后发送（修复后）
- **旧架构**：在状态更新过程中发送

#### **4. 事务管理**
- **新架构**：统一的事务管理，包含所有操作
- **旧架构**：可能存在多个独立事务

## 🔍 具体冲突场景分析

### **场景1：CRM合同作废**
**当前状态**：使用旧架构
```php
// CrmContractService.voidContract()
$workflowEngineService = WorkflowEngineService::getInstance();
$result = $workflowEngineService->voidApprovedInstance(
    $contract['workflow_instance_id'],
    $reason,
    get_user_id()
);
```

**潜在问题**：
- 可能不会调用 `CrmContractService.afterWorkflowStatusChange()`
- 业务状态同步可能不完整
- 与新架构的通知格式可能不一致

### **场景2：每日报价作废**
**当前状态**：使用新架构
```php
// DailyPriceOrderService.voidApproval()
$unifiedService = new UnifiedWorkflowService();
$result = $unifiedService->executeWorkflowOperation('void', $params);
```

**优势**：
- 完整的状态同步
- 统一的通知发送
- 标准化的业务逻辑处理

### **场景3：申请列表作废**
**当前状态**：使用新架构
```php
// ApplicationController.void()
$syncService = new WorkflowStatusSyncService();
$result = $syncService->syncAllWorkflowStatus($instanceId, STATUS_VOID, $reason);
```

**优势**：
- 直接使用状态同步服务
- 现在已支持通知发送
- 适用于标准化审批流程

## 🛠️ 迁移实施计划

### **阶段1：CRM合同模块迁移**（预计2周）

#### **步骤1：适配 CrmContractService**
```php
// 新增方法：使用统一工作流服务
public function voidContractV2(int $contractId, string $reason = ''): bool
{
    $unifiedService = new UnifiedWorkflowService();
    return $unifiedService->executeWorkflowOperation('void', [
        'business_code' => 'crm_contract',
        'business_id' => $contractId,
        'reason' => $reason,
        'operator_id' => get_user_id()
    ]);
}
```

#### **步骤2：完善 afterWorkflowStatusChange 方法**
```php
public function afterWorkflowStatusChange(int $businessId, int $status, array $extra = []): bool
{
    if ($status === WorkflowStatusConstant::STATUS_VOID) {
        // 处理合同作废后的业务逻辑
        return $this->handleContractVoided($businessId, $extra);
    }
    return true;
}
```

#### **步骤3：前端适配**
- 修改前端调用接口
- 统一错误处理和成功提示

### **阶段2：CRM回款模块迁移**（预计1周）
- 参考合同模块的迁移方案
- 适配回款业务的特殊逻辑

### **阶段3：验证和清理**（预计1周）
- 全面测试迁移后的功能
- 移除旧架构相关代码
- 更新相关文档

## 📈 预期收益

### **技术收益**
- **代码统一**：消除重复的工作流处理逻辑
- **维护简化**：只需维护一套架构
- **扩展性提升**：新功能开发更加标准化

### **业务收益**
- **用户体验一致**：所有模块的操作体验统一
- **功能完整性**：确保所有工作流功能的完整性
- **数据一致性**：统一的状态同步机制

### **运维收益**
- **问题排查简化**：统一的日志和错误处理
- **监控统一**：统一的性能和业务监控
- **部署简化**：减少架构复杂性

## 🔗 相关文档

- [工作流架构优化变更说明](./工作流架构优化变更说明.md)
- [工作流统一对接开发指南](./工作流统一对接开发指南.md)
- [工作流业务对接统一技术文档](./工作流业务对接统一技术文档.md)
- [统一工作流对接详细技术文档](./统一工作流对接详细技术文档.md)
