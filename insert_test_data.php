<?php
/**
 * 项目统计测试数据插入脚本
 * 为项目统计功能创建完整的测试数据
 */

$pdo = new PDO('mysql:host=*************;port=3306;dbname=www_bs_com;charset=utf8mb4', 'www_bs_com', 'PdadjMXmNy8Pn9tj');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

echo "=== 开始插入项目统计测试数据 ===\n\n";

try {
    $pdo->beginTransaction();
    
    // 1. 插入更多项目成员数据
    echo "1. 插入项目成员数据...\n";
    $memberData = [
        ['project_id' => 1, 'user_id' => 1, 'role' => 'owner'],
        ['project_id' => 1, 'user_id' => 201, 'role' => 'member'],
        ['project_id' => 1, 'user_id' => 202, 'role' => 'member'],
        ['project_id' => 1, 'user_id' => 203, 'role' => 'member'],
        ['project_id' => 1, 'user_id' => 204, 'role' => 'member'],
    ];
    
    // 先清理项目1的现有成员
    $pdo->exec("DELETE FROM project_member WHERE project_id = 1");
    
    foreach ($memberData as $member) {
        $stmt = $pdo->prepare("
            INSERT INTO project_member (project_id, user_id, role, joined_at, tenant_id, creator_id, created_at, updated_at) 
            VALUES (?, ?, ?, NOW(), 0, 1, NOW(), NOW())
        ");
        $stmt->execute([$member['project_id'], $member['user_id'], $member['role']]);
    }
    echo "   - 插入了 " . count($memberData) . " 条成员记录\n";
    
    // 2. 插入更多任务数据 (不同状态和优先级)
    echo "2. 插入任务数据...\n";
    $taskData = [
        // 待办任务 (status=1)
        ['project_id' => 1, 'title' => '需求分析文档', 'status' => 1, 'priority' => 2, 'assignee_id' => 201],
        ['project_id' => 1, 'title' => '技术方案设计', 'status' => 1, 'priority' => 3, 'assignee_id' => 202],
        ['project_id' => 1, 'title' => '测试用例编写', 'status' => 1, 'priority' => 1, 'assignee_id' => 203],
        ['project_id' => 1, 'title' => '部署环境准备', 'status' => 1, 'priority' => 2, 'assignee_id' => 204],
        
        // 进行中任务 (status=2)
        ['project_id' => 1, 'title' => '前端页面开发', 'status' => 2, 'priority' => 3, 'assignee_id' => 201],
        ['project_id' => 1, 'title' => '后端API开发', 'status' => 2, 'priority' => 3, 'assignee_id' => 202],
        ['project_id' => 1, 'title' => '数据库设计', 'status' => 2, 'priority' => 2, 'assignee_id' => 203],
        ['project_id' => 1, 'title' => '接口联调', 'status' => 2, 'priority' => 2, 'assignee_id' => 204],
        ['project_id' => 1, 'title' => '性能优化', 'status' => 2, 'priority' => 1, 'assignee_id' => 1],
        
        // 已完成任务 (status=3)
        ['project_id' => 1, 'title' => '项目立项', 'status' => 3, 'priority' => 3, 'assignee_id' => 1],
        ['project_id' => 1, 'title' => '团队组建', 'status' => 3, 'priority' => 2, 'assignee_id' => 1],
        ['project_id' => 1, 'title' => '原型设计', 'status' => 3, 'priority' => 2, 'assignee_id' => 201],
        ['project_id' => 1, 'title' => '技术选型', 'status' => 3, 'priority' => 3, 'assignee_id' => 202],
        ['project_id' => 1, 'title' => '开发环境搭建', 'status' => 3, 'priority' => 1, 'assignee_id' => 203],
        ['project_id' => 1, 'title' => '代码规范制定', 'status' => 3, 'priority' => 1, 'assignee_id' => 204],
        
        // 已关闭任务 (status=4)
        ['project_id' => 1, 'title' => '废弃功能清理', 'status' => 4, 'priority' => 1, 'assignee_id' => 201],
        ['project_id' => 1, 'title' => '旧版本迁移', 'status' => 4, 'priority' => 2, 'assignee_id' => 202],
    ];
    
    // 先清理项目1的现有任务
    $pdo->exec("DELETE FROM project_task WHERE project_id = 1");
    
    foreach ($taskData as $task) {
        $stmt = $pdo->prepare("
            INSERT INTO project_task (
                project_id, title, description, status, priority, assignee_id, 
                start_date, due_date, completed_at, estimated_hours, actual_hours, 
                sort, tenant_id, creator_id, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $description = $task['title'] . '的详细描述和实施方案';
        $startDate = date('Y-m-d', strtotime('-' . rand(1, 30) . ' days'));
        $dueDate = date('Y-m-d', strtotime('+' . rand(1, 30) . ' days'));
        $completedAt = $task['status'] == 3 ? date('Y-m-d H:i:s', strtotime('-' . rand(1, 10) . ' days')) : null;
        $estimatedHours = rand(8, 40);
        $actualHours = $task['status'] == 3 ? rand(8, 50) : 0;
        $sort = rand(1, 100);
        
        $stmt->execute([
            $task['project_id'], $task['title'], $description, $task['status'], $task['priority'], 
            $task['assignee_id'], $startDate, $dueDate, $completedAt, $estimatedHours, $actualHours,
            $sort, 0, 1, date('Y-m-d H:i:s'), date('Y-m-d H:i:s')
        ]);
    }
    echo "   - 插入了 " . count($taskData) . " 条任务记录\n";
    
    // 3. 插入任务记录数据 (评论和跟进)
    echo "3. 插入任务记录数据...\n";
    
    // 获取刚插入的任务ID
    $stmt = $pdo->query("SELECT id FROM project_task WHERE project_id = 1 ORDER BY id");
    $taskIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $recordData = [];
    $users = [1, 201, 202, 203, 204];
    $actions = ['创建了任务', '更新了任务', '完成了任务', '评论了任务', '跟进了任务'];
    
    // 为每个任务创建一些记录
    foreach ($taskIds as $taskId) {
        // 创建2-5条记录
        $recordCount = rand(2, 5);
        for ($i = 0; $i < $recordCount; $i++) {
            $recordType = rand(0, 1) ? 'comment' : 'follow';
            $userId = $users[array_rand($users)];
            $action = $actions[array_rand($actions)];
            
            $content = $recordType == 'comment' 
                ? "这是一条评论内容，关于任务的进展和问题讨论。"
                : "跟进内容：任务进展顺利，预计按时完成。";
                
            $followDate = $recordType == 'follow' 
                ? date('Y-m-d H:i:s', strtotime('-' . rand(1, 10) . ' days'))
                : null;
                
            $nextPlan = $recordType == 'follow' 
                ? "下一步计划：继续推进开发工作，解决技术难点。"
                : null;
                
            $nextDate = $recordType == 'follow' 
                ? date('Y-m-d H:i:s', strtotime('+' . rand(1, 7) . ' days'))
                : null;
            
            $recordData[] = [
                'task_id' => $taskId,
                'record_type' => $recordType,
                'content' => $content,
                'follow_date' => $followDate,
                'next_plan' => $nextPlan,
                'next_date' => $nextDate,
                'creator_id' => $userId,
                'created_at' => date('Y-m-d H:i:s', strtotime('-' . rand(1, 20) . ' days'))
            ];
        }
    }
    
    // 先清理现有记录
    $pdo->exec("DELETE FROM project_task_record WHERE task_id IN (" . implode(',', $taskIds) . ")");
    
    foreach ($recordData as $record) {
        $stmt = $pdo->prepare("
            INSERT INTO project_task_record (
                task_id, record_type, content, follow_date, next_plan, next_date,
                tenant_id, creator_id, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $record['task_id'], $record['record_type'], $record['content'], 
            $record['follow_date'], $record['next_plan'], $record['next_date'],
            0, $record['creator_id'], $record['created_at'], $record['created_at']
        ]);
    }
    echo "   - 插入了 " . count($recordData) . " 条任务记录\n";
    
    $pdo->commit();
    echo "\n=== 测试数据插入完成 ===\n";
    
    // 4. 验证数据
    echo "\n=== 数据验证 ===\n";
    
    $stmt = $pdo->query("
        SELECT 
            CASE status 
                WHEN 1 THEN '待办'
                WHEN 2 THEN '进行中' 
                WHEN 3 THEN '已完成'
                WHEN 4 THEN '已关闭'
            END as status_name,
            COUNT(*) as count
        FROM project_task 
        WHERE project_id = 1 AND deleted_at IS NULL
        GROUP BY status
        ORDER BY status
    ");
    
    echo "任务状态分布:\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "  {$row['status_name']}: {$row['count']}个\n";
    }
    
    $stmt = $pdo->query("
        SELECT 
            CASE priority
                WHEN 1 THEN '低'
                WHEN 2 THEN '中'
                WHEN 3 THEN '高'
            END as priority_name,
            COUNT(*) as count
        FROM project_task 
        WHERE project_id = 1 AND deleted_at IS NULL
        GROUP BY priority
        ORDER BY priority
    ");
    
    echo "\n任务优先级分布:\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "  {$row['priority_name']}: {$row['count']}个\n";
    }
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM project_member WHERE project_id = 1 AND deleted_at IS NULL");
    $memberCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "\n项目成员数量: {$memberCount}人\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM project_task_record WHERE deleted_at IS NULL");
    $recordCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "任务记录数量: {$recordCount}条\n";
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo "错误: " . $e->getMessage() . "\n";
}
?>
