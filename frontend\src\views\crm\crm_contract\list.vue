<script setup lang="ts">
  import { SearchChangeParams, SearchFormItem } from '@/types/search-form'
  import { ElMessage, ElMessageBox, ElDescriptions, ElDescriptionsItem } from 'element-plus'
  import { Delete, Warning } from '@element-plus/icons-vue'
  import { BgColorEnum } from '@/enums/appEnum'
  import { CrmContractApi } from '@/api/crm/crmContract'
  import { ApiStatus } from '@/utils/http/status'
  import { useAuth } from '@/composables/useAuth'

  import { LongTextColumn } from '@/components/core/tables/columns'

  // 表单对话框导入 - 合同创建和编辑在客户详情页面进行
  // import FormDialog from './form-dialog.vue'

  // import ImportExportDialog from './import-export-dialog.vue'

  // CRM审批组件
  // import CrmApprovalButton from '@/components/crm/CrmApprovalButton.vue'
  import CrmApprovalStatus from '@/components/crm/CrmApprovalStatus.vue'
  import {
    WorkflowStatus,
    getInstanceStatusText,
    getInstanceStatusTagType
  } from '@/constants/workflow'
  // import CrmApprovalSubmitDialog from '@/components/crm/CrmApprovalSubmitDialog.vue'

  // 权限验证
  const { hasAuth } = useAuth()

  // 表格数据与分页
  const tableData = ref<any[]>([])
  const loading = ref(false)
  const currentPage = ref(1)
  const pageSize = ref(10)
  const total = ref(0)

  // 详情对话框
  const detailDialogVisible = ref(false)
  const detailData = ref<any>({})

  // 产品列表对话框
  const productDialogVisible = ref(false)
  const productList = ref<any[]>([])

  // 审批相关
  /*const showApprovalDialog = ref(false)
  const currentApprovalData = ref<any>({})
  const approvalLoading = ref(false)*/

  // 定义表单搜索初始值
  const initialSearchState = {
    contract_number: '',
    customer_name: '',
    approval_status: null
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    currentPage.value = 1
    getTableData()
  }

  // 表单项变更处理
  const handleFormChange = (params: SearchChangeParams): void => {
    console.log('表单项变更:', params)
  }

  // 表单配置项
  const formItems: SearchFormItem[] = [
    {
      prop: 'contract_number',
      label: '合同编号',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入合同编号关键词'
      },
      onChange: handleFormChange
    },
    {
      prop: 'contract_name',
      label: '合同名称',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入合同名称关键词'
      },
      onChange: handleFormChange
    },
    /*{
      prop: 'owner_user_id',
      label: '负责人ID',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入负责人ID'
      },
      onChange: handleFormChange
    },*/
    {
      prop: 'approval_status',
      label: '审批状态',
      type: 'select',
      config: {
        clearable: true,
        placeholder: '请选择状态'
      },
      options: () => [
        { label: '草稿', value: WorkflowStatus.DRAFT },
        { label: '审批中', value: WorkflowStatus.PROCESSING },
        { label: '已通过', value: WorkflowStatus.COMPLETED },
        { label: '已拒绝', value: WorkflowStatus.REJECTED },
        { label: '已终止', value: WorkflowStatus.TERMINATED },
        { label: '已撤回', value: WorkflowStatus.RECALLED },
        { label: '已作废', value: WorkflowStatus.VOID }
      ],
      onChange: handleFormChange
    }
    /*{
      prop: 'contract_type',
      label: '合同类型',
      type: 'input',
      config: {
        clearable: true,
        placeholder: '请输入合同类型'
      },
      onChange: handleFormChange
    },*/
    /*{
      prop: 'sign_date',
      label: '签约日期',
      type: 'date',
      config: {
        clearable: true,
        placeholder: '请选择签约日期',
        type: 'date'
      },
      onChange: handleFormChange
    }*/
  ]

  // 列配置 - 声明式渲染不再需要动态列配置
  const columnOptions = [{ label: '操作', prop: 'operation' }]

  // 移除动态列配置，改为声明式渲染
  // 不再需要 useCheckedColumns 和动态列配置

  // 付款状态相关方法
  const getPaymentStatusText = (status: number) => {
    const statusMap: Record<number, string> = {
      0: '未付款',
      1: '部分付款',
      2: '已付清',
      3: '逾期'
    }
    return statusMap[status] || '未付款'
  }

  const getPaymentStatusType = (status: number) => {
    const typeMap: Record<number, 'info' | 'warning' | 'success' | 'danger'> = {
      0: 'info',
      1: 'warning',
      2: 'success',
      3: 'danger'
    }
    return typeMap[status] || 'info'
  }

  // 使用统一的工作流状态处理函数
  // 已移除 getContractStatusText 和 getContractStatusType，使用 getInstanceStatusText 和 getInstanceStatusTagType

  onMounted(() => {
    getTableData()
  })

  // 处理分页页码变化
  const handleSizeChange = (val: number) => {
    pageSize.value = val
    getTableData()
  }

  // 处理每页条数变化
  const handleCurrentChange = (val: number) => {
    currentPage.value = val
    getTableData()
  }

  // 获取表格数据
  const getTableData = async () => {
    loading.value = true
    try {
      const res = await CrmContractApi.list({
        page: currentPage.value,
        limit: pageSize.value,
        ...formFilters
      })

      if (res.code === ApiStatus.success) {
        total.value = res.data.total || 0
        currentPage.value = res.data.page || 1
        pageSize.value = res.data.limit || 10
        tableData.value = res.data.list || []
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新表格
  const handleRefresh = () => {
    getTableData()
  }

  // 显示详情
  const showDetail = async (id: number) => {
    try {
      loading.value = true
      const res = await CrmContractApi.detail(id)
      if (res.code === ApiStatus.success) {
        detailData.value = res.data
        detailDialogVisible.value = true
      }
    } finally {
      loading.value = false
    }
  }

  // 删除记录
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      loading.value = true
      const res = await CrmContractApi.delete(id)

      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')
        await getTableData()
      }
    } finally {
      loading.value = false
    }
  }

  // 判断是否有更多操作
  const hasMoreActions = (row: any) => {
    // 删除权限：草稿状态且有删除权限
    const canDelete =
      row.approval_status === WorkflowStatus.DRAFT && hasAuth('crm:crm_contract:delete')
    // 作废权限：非已作废状态且有作废权限
    const canVoid = row.approval_status !== WorkflowStatus.VOID && hasAuth('crm:crm_contract:void')

    return canDelete || canVoid
  }

  // 作废合同
  const handleVoid = async (id: number) => {
    try {
      // 使用输入框获取作废原因
      const { value: reason } = await ElMessageBox.prompt(
        '请输入作废原因（必填）：',
        '确认作废合同',
        {
          confirmButtonText: '确定作废',
          cancelButtonText: '取消',
          inputType: 'textarea',
          inputPlaceholder: '请详细说明作废原因...',
          inputValidator: (value) => {
            if (!value || value.trim().length === 0) {
              return '作废原因不能为空'
            }
            if (value.trim().length < 5) {
              return '作废原因至少需要5个字符'
            }
            if (value.trim().length > 200) {
              return '作废原因不能超过200个字符'
            }
            return true
          },
          inputErrorMessage: '请输入有效的作废原因'
        }
      )

      loading.value = true
      const res = await CrmContractApi.voidContract(id, reason.trim())

      if (res.code === ApiStatus.success) {
        ElMessage.success('合同作废成功')
        await getTableData()
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('作废合同失败:', error)
      }
    } finally {
      loading.value = false
    }
  }

  // 导入导出对话框引用
  /*const importExportDialogRef = ref()

  // 导入导出成功回调
  const handleImportExportSuccess = () => {
    getTableData()
  }

  // 显示导出对话框
  const showExportDialog = () => {
    importExportDialogRef.value?.showDialog('export')
  }*/

  // 移除表单对话框相关代码 - 合同创建和编辑在客户详情页面进行
  // const formDialogRef = ref()
  // const showFormDialog = (type: string, id?: number) => { ... }
  // const handleFormSubmitSuccess = () => { ... }

  // 处理审批提交
  /*const handleApprovalSubmit = (data: { businessType: string; businessData: any }) => {
    currentApprovalData.value = data.businessData
    showApprovalDialog.value = true
  }*/

  // 提交审批申请
  /*const submitApproval = async (approvalData: any) => {
    try {
      approvalLoading.value = true

      // 这里调用工作流API提交审批
      // const res = await WorkflowApi.submitApproval(approvalData)

      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000))

      ElMessage.success('审批申请提交成功')
      showApprovalDialog.value = false

      // 刷新表格数据以更新审批状态
      getTableData()
    } catch (error) {
      console.error('提交审批失败:', error)
      ElMessage.error('提交审批失败，请重试')
    } finally {
      approvalLoading.value = false
    }
  }*/
</script>

<template>
  <ArtTableFullScreen>
    <div class="crm-crmContract-page" id="table-full-screen">
      <!-- 搜索栏 -->
      <ArtSearchBar
        v-model:filter="formFilters"
        :items="formItems"
        @reset="handleReset"
        @search="handleSearch"
      ></ArtSearchBar>

      <ElCard shadow="never" class="art-table-card">
        <!-- 表格头部 -->
        <ArtTableHeader :columnList="columnOptions" @refresh="handleRefresh">
          <template #left>
            <!-- 屏蔽新增功能 - 合同创建在客户详情页面进行 -->
            <!-- <ElButton type="primary" @click="showFormDialog('add')">新增</ElButton> -->

            <!-- 屏蔽导入功能 - 合同管理只做展示和删除、作废 -->
            <!-- <ElButton type="success" @click="showImportDialog">导入</ElButton> -->

            <!--            <ElButton type="warning" @click="showExportDialog">导出</ElButton>-->
          </template>
        </ArtTableHeader>

        <!-- 表格 -->
        <ArtTable
          :loading="loading"
          :currentPage="currentPage"
          :pageSize="pageSize"
          :data="tableData"
          :total="total"
          :marginTop="10"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <!-- ID列 -->
          <ElTableColumn prop="id" label="ID" width="80" />

          <!-- 1. 基础信息组 -->
          <ElTableColumn label="基础信息" min-width="260" align="left">
            <template #default="scope">
              <div class="basic-info-group">
                <div class="info-row main-info">
                  <span class="info-label">编号:</span>
                  <span class="info-value contract-no">{{ scope.row.contract_no || '-' }}</span>
                </div>
                <div class="info-row main-info">
                  <span class="info-label">名称:</span>
                  <span class="info-value contract-name" :title="scope.row.contract_name">
                    {{ scope.row.contract_name || '-' }}
                  </span>
                </div>
                <div class="info-row secondary-info">
                  <span class="info-label">客户:</span>
                  <span class="info-value customer-name" :title="scope.row.customer_name">
                    {{ scope.row.customer_name || '-' }}
                  </span>
                </div>
                <div class="info-row secondary-info">
                  <span class="info-label">签约人:</span>
                  <span class="info-value contact-name">{{ scope.row.contact_name || '-' }}</span>
                  <span class="info-separator">|</span>
                  <span class="info-label">电话:</span>
                  <span class="info-value contact-mobile">{{
                    scope.row.contact_mobile || '-'
                  }}</span>
                </div>
                <div class="info-row secondary-info">
                  <span class="info-label">类型:</span>
                  <span class="info-value contract-type">{{ scope.row.type || '-' }}</span>
                  <span class="info-separator">|</span>
                  <span class="info-label">负责:</span>
                  <span class="info-value owner-name">{{ scope.row.owner_name || '-' }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 2. 合同时间组 -->
          <ElTableColumn label="合同时间" width="180" align="left">
            <template #default="scope">
              <div class="contract-time-group">
                <div class="time-item">
                  <span class="time-label">开始:</span>
                  <span class="time-value">{{ scope.row.start_date || '-' }}</span>
                </div>
                <div class="time-item">
                  <span class="time-label">结束:</span>
                  <span class="time-value">{{ scope.row.end_date || '-' }}</span>
                </div>
                <div class="time-item">
                  <span class="time-label">签约:</span>
                  <span class="time-value">{{ scope.row.sign_date || '-' }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 3. 付款信息组 -->
          <ElTableColumn label="付款信息" width="330" align="left">
            <template #default="scope">
              <div class="payment-info-group">
                <div class="payment-row payment-terms-row">
                  <span class="payment-label">条件:</span>
                  <span class="payment-value payment-terms-value" :title="scope.row.payment_terms">
                    {{ scope.row.payment_terms || '-' }}
                  </span>
                </div>
                <div class="payment-row">
                  <span class="payment-label">合同:</span>
                  <span
                    class="payment-amount"
                    :class="{ 'zero-amount': !scope.row.contract_amount }"
                  >
                    ¥{{
                      (scope.row.contract_amount || 0).toLocaleString('zh-CN', {
                        minimumFractionDigits: 2
                      })
                    }}
                  </span>
                </div>
                <div class="payment-row">
                  <span class="payment-label">已付:</span>
                  <span
                    class="payment-amount paid"
                    :class="{ 'zero-amount': !scope.row.paid_amount }"
                  >
                    ¥{{
                      (scope.row.paid_amount || 0).toLocaleString('zh-CN', {
                        minimumFractionDigits: 2
                      })
                    }}
                  </span>
                  <el-tag
                    :type="getPaymentStatusType(scope.row.payment_status)"
                    size="small"
                    class="payment-status-tag"
                  >
                    {{ getPaymentStatusText(scope.row.payment_status) }}
                  </el-tag>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 合同状态列 -->
          <!--          <TagColumn
                      prop="status"
                      label="合同状态"
                      width="100"
                      :tagMap="{
                        0: { text: '草稿', type: 'info' },
                        1: { text: '审批中', type: 'warning' },
                        2: { text: '已审批', type: 'success' },
                        3: { text: '执行中', type: 'primary' },
                        4: { text: '已完成', type: 'success' },
                        5: { text: '已作废', type: 'danger' }
                      }"
                    />-->

          <!-- 备注列 -->
          <LongTextColumn prop="remark" label="备注" :max-length="50" />

          <!-- 4. 创建信息组 -->
          <ElTableColumn label="创建信息" width="200" align="left">
            <template #default="scope">
              <div class="create-info-group">
                <div class="create-row">
                  <span class="create-label">创建人:</span>
                  <span class="create-value creator-name">{{ scope.row.creator_name || '-' }}</span>
                </div>
                <div class="create-row">
                  <span class="create-label">时间:</span>
                  <span class="create-value create-time">{{ scope.row.created_at || '-' }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <!-- 审批状态列 -->
          <ElTableColumn prop="approval_status" label="审批状态" width="120">
            <template #default="scope">
              <CrmApprovalStatus
                :status="scope.row.approval_status || 0"
                business-type="crm_contract"
                size="small"
              />
            </template>
          </ElTableColumn>

          <!-- 操作列 -->
          <ElTableColumn prop="operation" label="操作" fixed="right" width="180">
            <template #default="scope">
              <ArtButtonTable
                v-auth="'crm:crm_contract:detail'"
                text="详情"
                :iconClass="BgColorEnum.SECONDARY"
                @click="showDetail(scope.row.id)"
              />

              <el-dropdown
                v-if="hasMoreActions(scope.row)"
                trigger="hover"
                placement="bottom-end"
                class="more-actions-dropdown"
              >
                <ArtButtonTable text="更多" type="more" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- 删除按钮 - 只允许删除草稿状态的回款 -->
                    <el-dropdown-item
                      v-if="
                        scope.row.approval_status === WorkflowStatus.DRAFT &&
                        hasAuth('crm:crm_contract:delete')
                      "
                      @click="handleDelete(scope.row.id)"
                      divided
                    >
                      <el-icon>
                        <Delete />
                      </el-icon>
                      删除
                    </el-dropdown-item>
                    <!-- 作废按钮 - 使用approval_status字段判断 -->
                    <el-dropdown-item
                      v-if="
                        scope.row.approval_status !== WorkflowStatus.VOID &&
                        hasAuth('crm:crm_contract:void')
                      "
                      @click="handleVoid(scope.row.id)"
                    >
                      <el-icon>
                        <Warning />
                      </el-icon>
                      作废
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </ElTableColumn>
        </ArtTable>

        <!-- 详情对话框 -->
        <ElDialog
          v-model="detailDialogVisible"
          title="合同详情"
          width="900px"
          destroy-on-close
          class="detail-dialog"
        >
          <div class="detail-content">
            <!-- 1. 基础信息组 -->
            <div class="detail-section">
              <h3 class="section-title">基础信息</h3>
              <ElDescriptions :column="2" border class="detail-descriptions">
                <ElDescriptionsItem label="合同编号">
                  <span class="detail-value contract-no">{{ detailData.contract_no || '-' }}</span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="合同名称">
                  <span class="detail-value contract-name">{{
                    detailData.contract_name || '-'
                  }}</span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="客户名称">
                  <span class="detail-value customer-name">{{
                    detailData.customer_name || '-'
                  }}</span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="合同状态">
                  <ElTag
                    :type="getInstanceStatusTagType(detailData.approval_status) as any"
                    size="default"
                  >
                    {{ getInstanceStatusText(detailData.approval_status) }}
                  </ElTag>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="签约人姓名">
                  <span class="detail-value contact-name">{{
                    detailData.contact_name || '-'
                  }}</span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="签约人电话">
                  <span class="detail-value contact-mobile">{{
                    detailData.contact_mobile || '-'
                  }}</span>
                </ElDescriptionsItem>

                <!--                <ElDescriptionsItem label="合同类型">
                                  <span class="detail-value">{{ detailData.type || '-' }}</span>
                                </ElDescriptionsItem>-->
              </ElDescriptions>
            </div>

            <!-- 2. 金额信息组 -->
            <div class="detail-section">
              <h3 class="section-title">金额信息</h3>
              <ElDescriptions :column="2" border class="detail-descriptions">
                <ElDescriptionsItem label="合同金额">
                  <span class="detail-value amount-value">
                    ¥{{
                      (detailData.contract_amount || 0).toLocaleString('zh-CN', {
                        minimumFractionDigits: 2
                      })
                    }}
                  </span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="已付金额">
                  <span class="detail-value amount-paid">
                    ¥{{
                      (detailData.paid_amount || 0).toLocaleString('zh-CN', {
                        minimumFractionDigits: 2
                      })
                    }}
                  </span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="付款状态">
                  <ElTag :type="getPaymentStatusType(detailData.payment_status)" size="default">
                    {{ getPaymentStatusText(detailData.payment_status) }}
                  </ElTag>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="付款条件">
                  <span class="detail-value payment-terms">{{
                    detailData.payment_terms || '-'
                  }}</span>
                </ElDescriptionsItem>
              </ElDescriptions>
            </div>

            <!-- 3. 时间信息组 -->
            <div class="detail-section">
              <h3 class="section-title">时间信息</h3>
              <ElDescriptions :column="2" border class="detail-descriptions">
                <ElDescriptionsItem label="签约日期">
                  <span class="detail-value date-value">{{ detailData.sign_date || '-' }}</span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="开始日期">
                  <span class="detail-value date-value">{{ detailData.start_date || '-' }}</span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="结束日期">
                  <span class="detail-value date-value">{{ detailData.end_date || '-' }}</span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="创建时间">
                  <span class="detail-value create-time">{{ detailData.created_at || '-' }}</span>
                </ElDescriptionsItem>
              </ElDescriptions>
            </div>

            <!-- 4. 其他信息组 -->
            <div class="detail-section">
              <h3 class="section-title">其他信息</h3>
              <ElDescriptions :column="1" border class="detail-descriptions">
                <ElDescriptionsItem label="交货条件">
                  <span class="detail-value">{{ detailData.delivery_terms || '-' }}</span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="合同内容">
                  <span class="detail-value contract-content">{{
                    detailData.contract_content || '-'
                  }}</span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="备注">
                  <span class="detail-value remark">{{ detailData.remark || '-' }}</span>
                </ElDescriptionsItem>

                <ElDescriptionsItem label="负责人">
                  <span class="detail-value creator-name">{{ detailData.owner_name || '-' }}</span>
                </ElDescriptionsItem>
              </ElDescriptions>
            </div>
          </div>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="detailDialogVisible = false">关闭</ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 产品列表对话框 -->
        <ElDialog
          v-model="productDialogVisible"
          title="合同产品清单"
          width="900px"
          destroy-on-close
          class="product-dialog"
        >
          <div class="product-content" style="height: 400px; overflow-y: auto">
            <ElTable :data="productList" border stripe>
              <ElTableColumn prop="product_info" label="产品名称" min-width="200" />
              <ElTableColumn prop="quantity" label="数量" width="100" align="center">
                <template #default="scope">
                  {{ scope.row.quantity }}
                </template>
              </ElTableColumn>
              <ElTableColumn prop="unit_price" label="单价" width="120" align="right">
                <template #default="scope">
                  ¥{{ parseFloat(scope.row.unit_price || 0).toFixed(2) }}
                </template>
              </ElTableColumn>
              <ElTableColumn prop="discount_rate" label="折扣率" width="100" align="center">
                <template #default="scope"> {{ scope.row.discount_rate || 0 }}%</template>
              </ElTableColumn>
              <ElTableColumn prop="discount_amount" label="折扣金额" width="120" align="right">
                <template #default="scope">
                  ¥{{ parseFloat(scope.row.discount_amount || 0).toFixed(2) }}
                </template>
              </ElTableColumn>
              <ElTableColumn prop="subtotal" label="小计" width="120" align="right">
                <template #default="scope">
                  <span style="font-weight: bold; color: #e6a23c">
                    ¥{{ parseFloat(scope.row.subtotal || 0).toFixed(2) }}
                  </span>
                </template>
              </ElTableColumn>
            </ElTable>

            <!-- 合计信息 -->
            <div
              style="margin-top: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px"
            >
              <ElRow :gutter="20">
                <ElCol :span="8">
                  <div style="text-align: center">
                    <div style="font-size: 14px; color: #909399">产品数量</div>
                    <div style="font-size: 18px; font-weight: bold; color: #409eff">
                      {{ productList.length }} 种
                    </div>
                  </div>
                </ElCol>
                <ElCol :span="8">
                  <div style="text-align: center">
                    <div style="font-size: 14px; color: #909399">总数量</div>
                    <div style="font-size: 18px; font-weight: bold; color: #67c23a">
                      {{
                        productList.reduce((sum, item) => sum + parseFloat(item.quantity || 0), 0)
                      }}
                    </div>
                  </div>
                </ElCol>
                <ElCol :span="8">
                  <div style="text-align: center">
                    <div style="font-size: 14px; color: #909399">合计金额</div>
                    <div style="font-size: 18px; font-weight: bold; color: #f56c6c">
                      ¥{{
                        productList
                          .reduce((sum, item) => sum + parseFloat(item.subtotal || 0), 0)
                          .toFixed(2)
                      }}
                    </div>
                  </div>
                </ElCol>
              </ElRow>
            </div>
          </div>
          <template #footer>
            <div class="dialog-footer">
              <ElButton @click="productDialogVisible = false">关闭</ElButton>
            </div>
          </template>
        </ElDialog>

        <!-- 表单组件已移除 - 合同创建和编辑在客户详情页面进行 -->
        <!-- <FormDialog ref="formDialogRef" @success="handleFormSubmitSuccess" /> -->

        <!-- 导入导出对话框 -->

        <!--        <ImportExportDialog ref="importExportDialogRef" @success="handleImportExportSuccess" />-->

        <!-- 审批提交弹窗 -->
        <!--        <CrmApprovalSubmitDialog
                  :visible="showApprovalDialog"
                  business-type="crm_contract"
                  :business-data="currentApprovalData"
                  @update:visible="showApprovalDialog = $event"
                  @submit="submitApproval"
                />-->
      </ElCard>
    </div>
  </ArtTableFullScreen>
</template>

<style scoped lang="scss">
  .crm-crmContract-page {
    width: 100%;

    :deep(.el-table) {
      .el-table__inner-wrapper:before {
        display: none;
      }

      font-size: 14px; /* 适老化整体字体调大 */

      th {
        background-color: #fafafa;
        font-weight: 600;
        color: #303133;
        font-size: 15px; /* 表头字体更大 */
        padding: 12px 0; /* 增加表头高度 */
      }

      td {
        padding: 12px 0; /* 增加行高 */
        font-size: 14px;
      }
    }

    .detail-image {
      max-width: 100px;
      max-height: 100px;
    }

    /* 基础信息组样式 */
    .basic-info-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
      font-size: 14px; /* 适老化字体调大 */
      line-height: 1.4;
    }

    .info-row {
      display: flex;
      align-items: center;
      gap: 6px;
      flex-wrap: wrap;
    }

    .info-row.main-info {
      font-size: 15px; /* 主要信息字体更大 */
      font-weight: 500;
    }

    .info-row.secondary-info {
      font-size: 13px;
      color: #666;
    }

    .info-label {
      color: #909399;
      font-weight: 600;
      min-width: 36px;
      font-size: inherit;
    }

    .info-value {
      //color: #303133;
      color: var(--art-text-gray-800);
      font-weight: inherit;
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .info-value.contract-no {
      font-family: 'Consolas', 'Monaco', monospace;
      color: #409eff;
      font-weight: 600;
    }

    .info-value.contract-name {
      font-weight: 600;
      //color: #303133;
      color: var(--art-text-gray-800);
      max-width: 200px;
    }

    .info-value.customer-name {
      color: #e6a23c;
      font-weight: 500;
      max-width: 100px;
    }

    .info-separator {
      color: #dcdfe6;
      margin: 0 4px;
    }

    /* 合同时间组样式 */
    .contract-time-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
      font-size: 14px; /* 适老化字体调大 */
    }

    .time-item {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .time-label {
      color: #909399;
      font-weight: 600;
      min-width: 36px;
      font-size: 14px;
    }

    .time-value {
      color: #303133;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 14px;
      font-weight: 500;
    }

    /* 付款信息组样式 */
    .payment-info-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
      font-size: 14px; /* 适老化字体调大 */
    }

    .payment-row {
      display: flex;
      align-items: center;
      gap: 6px;
      justify-content: flex-start;
    }

    .payment-label {
      color: #909399;
      font-weight: 600;
      min-width: 36px;
      font-size: 14px;
    }

    .payment-value {
      color: #303133;
      max-width: 200px; /* 增加付款条件的显示宽度 */
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 14px;
      line-height: 1.4;
    }

    /* 付款条件专用样式 */
    .payment-terms-row {
      align-items: flex-start; /* 顶部对齐，适应可能的多行内容 */
    }

    .payment-terms-value {
      max-width: 280px; /* 付款条件给予更大的显示空间 */
      white-space: normal; /* 允许换行 */
      word-break: break-word; /* 长单词换行 */
      line-height: 1.3;
      max-height: 3.9em; /* 限制最多3行显示 */
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3; /* 标准属性 */
      -webkit-box-orient: vertical;
    }

    .payment-amount {
      color: #e6a23c;
      font-weight: 600;
      font-family: 'Consolas', 'Monaco', monospace;
      min-width: 90px;
      font-size: 14px;
    }

    .payment-amount.paid {
      color: #67c23a;
    }

    .payment-amount.zero-amount {
      text-align: left; /* 金额为0时左对齐 */
    }

    .payment-status-tag {
      margin-left: 6px;
      font-size: 12px; /* 适老化标签字体调大 */
      height: 20px;
      line-height: 18px;
      padding: 0 8px;
    }

    /* 创建信息组样式 */
    .create-info-group {
      display: flex;
      flex-direction: column;
      gap: 6px;
      font-size: 14px;
    }

    .create-row {
      display: flex;
      align-items: center;
      gap: 6px;
    }

    .create-label {
      color: #909399;
      font-weight: 600;
      min-width: 48px;
      font-size: 14px;
    }

    .create-value {
      color: #303133;
      font-size: 14px;
    }

    .create-value.creator-name {
      color: #409eff;
      font-weight: 500;
    }

    .create-value.create-time {
      font-family: 'Consolas', 'Monaco', monospace;
      color: #666;
      font-size: 13px;
    }

    /* 详情对话框样式 */
    :deep(.detail-dialog) {
      .el-dialog__body {
        padding: 20px !important;
        max-height: 60vh; /* 调整为60vh，避免太贴近底部 */
        overflow-y: auto;
      }

      .el-dialog__header {
        padding: 20px 20px 10px 20px;

        .el-dialog__title {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
        }
      }
    }

    /* 详情内容样式 */
    .detail-content {
      font-size: 14px; /* 适老化字体 */
    }

    .detail-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 12px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #e4e7ed;
      display: flex;
      align-items: center;

      &:before {
        content: '';
        width: 4px;
        height: 16px;
        background: #409eff;
        margin-right: 8px;
        border-radius: 2px;
      }
    }

    .detail-descriptions {
      :deep(.el-descriptions__label) {
        font-size: 14px;
        font-weight: 600;
        color: #606266;
        width: 120px;
      }

      :deep(.el-descriptions__content) {
        font-size: 14px;
        color: #303133;
      }

      :deep(.el-descriptions__cell) {
        padding: 12px 16px;
      }
    }

    /* 详情值样式 */
    .detail-value {
      font-size: 14px;
      line-height: 1.4;

      &.contract-no {
        font-family: 'Consolas', 'Monaco', monospace;
        color: #409eff;
        font-weight: 600;
      }

      &.contract-name {
        font-weight: 600;
        color: #303133;
      }

      &.customer-name {
        color: #e6a23c;
        font-weight: 500;
      }

      &.owner-name {
        color: #409eff;
        font-weight: 500;
      }

      &.amount-value {
        color: #e6a23c;
        font-weight: 600;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 15px;
      }

      &.amount-paid {
        color: #67c23a;
        font-weight: 600;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 15px;
      }

      &.date-value {
        font-family: 'Consolas', 'Monaco', monospace;
        color: #666;
      }

      &.create-time {
        font-family: 'Consolas', 'Monaco', monospace;
        color: #909399;
        font-size: 13px;
      }

      &.creator-name {
        color: #409eff;
        font-weight: 500;
      }

      &.payment-terms {
        line-height: 1.5;
        word-break: break-word;
      }

      &.contract-content {
        line-height: 1.5;
        word-break: break-word;
        max-height: 100px;
        overflow-y: auto;
      }

      &.remark {
        line-height: 1.5;
        word-break: break-word;
        color: #666;
        font-style: italic;
      }
    }

    /* 更多操作下拉菜单样式 */
    .more-actions-dropdown {
      //margin-left: 8px;

      :deep(.el-dropdown-menu) {
        .el-dropdown-menu__item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 16px;
          font-size: 14px;

          .el-icon {
            font-size: 16px;
          }

          &:hover {
            background-color: #f5f7fa;
            color: #409eff;
          }

          &.is-divided {
            border-top: 1px solid #ebeef5;
            margin-top: 6px;
            padding-top: 8px;
          }
        }
      }
    }
  }
</style>
