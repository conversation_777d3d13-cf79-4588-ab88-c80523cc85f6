<?php
/**
 * 测试真实API接口
 * 模拟前端调用后端API接口，验证真实数据返回
 */

// 模拟ThinkPHP环境
define('ROOT_PATH', __DIR__ . '/');
require_once 'vendor/autoload.php';

// 模拟请求参数
$projectId = 1;

echo "=== 测试真实API接口返回数据 ===\n";
echo "项目ID: {$projectId}\n";
echo "=" . str_repeat("-", 50) . "\n\n";

// 模拟HTTP请求测试
$baseUrl = 'http://localhost:8000'; // 假设本地服务器地址
$apiEndpoints = [
    'task-status-stats' => "/api/project/project/task-status-stats?project_id={$projectId}",
    'task-priority-stats' => "/api/project/project/task-priority-stats?project_id={$projectId}",
    'member-stats' => "/api/project/project/member-stats?project_id={$projectId}",
    'recent-activities' => "/api/project/project/recent-activities?project_id={$projectId}",
    'progress-trend' => "/api/project/project/progress-trend?project_id={$projectId}"
];

echo "API接口地址列表:\n";
foreach ($apiEndpoints as $name => $endpoint) {
    echo "- {$name}: {$baseUrl}{$endpoint}\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "预期返回的真实数据格式:\n";
echo str_repeat("=", 60) . "\n\n";

// 1. 任务状态分布
echo "1. 任务状态分布 (GET /api/project/project/task-status-stats):\n";
$expectedStatusStats = [
    [
        'name' => '待办',
        'value' => 4,
        'color' => '#8C8C8C',
        'percentage' => 23.5,
        'label' => '待办: 4个 (23.5%)'
    ],
    [
        'name' => '进行中',
        'value' => 5,
        'color' => '#1664FF',
        'percentage' => 29.4,
        'label' => '进行中: 5个 (29.4%)'
    ],
    [
        'name' => '已完成',
        'value' => 6,
        'color' => '#00BC70',
        'percentage' => 35.3,
        'label' => '已完成: 6个 (35.3%)'
    ],
    [
        'name' => '已关闭',
        'value' => 2,
        'color' => '#F54A45',
        'percentage' => 11.8,
        'label' => '已关闭: 2个 (11.8%)'
    ]
];

echo json_encode([
    'code' => 200,
    'message' => '获取成功',
    'data' => $expectedStatusStats
], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

// 2. 任务优先级分布
echo "2. 任务优先级分布 (GET /api/project/project/task-priority-stats):\n";
$expectedPriorityStats = [
    [
        'name' => '低',
        'value' => 5,
        'percentage' => 29.4,
        'label' => '低: 5个 (29.4%)'
    ],
    [
        'name' => '中',
        'value' => 7,
        'percentage' => 41.2,
        'label' => '中: 7个 (41.2%)'
    ],
    [
        'name' => '高',
        'value' => 5,
        'percentage' => 29.4,
        'label' => '高: 5个 (29.4%)'
    ]
];

echo json_encode([
    'code' => 200,
    'message' => '获取成功',
    'data' => $expectedPriorityStats
], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

// 3. 成员工作量统计
echo "3. 成员工作量统计 (GET /api/project/project/member-stats):\n";
$expectedMemberStats = [
    [
        'id' => 201,
        'name' => '租户超级管理员',
        'avatar' => null,
        'task_count' => 4,
        'completion_rate' => 25.0,
        'completed_tasks' => 1,
        'status_text' => '已完成 1/4 个任务'
    ],
    [
        'id' => 202,
        'name' => '系统管理员',
        'avatar' => null,
        'task_count' => 4,
        'completion_rate' => 25.0,
        'completed_tasks' => 1,
        'status_text' => '已完成 1/4 个任务'
    ],
    [
        'id' => 1,
        'name' => '超级管理',
        'avatar' => 'http://www.bs.com/uploads/2025/05/20/60886eead5bbdb7fcd992fd8f11cb649.jpg',
        'task_count' => 3,
        'completion_rate' => 66.7,
        'completed_tasks' => 2,
        'status_text' => '已完成 2/3 个任务'
    ]
];

echo json_encode([
    'code' => 200,
    'message' => '获取成功',
    'data' => $expectedMemberStats
], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

// 4. 最近活动
echo "4. 最近活动 (GET /api/project/project/recent-activities):\n";
$expectedActivities = [
    [
        'user' => 'CRM管理员',
        'action' => '跟进了任务',
        'target' => '部署环境准备',
        'time' => '1天前'
    ],
    [
        'user' => '项目管理员',
        'action' => '评论了任务',
        'target' => '性能优化',
        'time' => '3天前'
    ],
    [
        'user' => '超级管理',
        'action' => '跟进了任务',
        'target' => '代码规范制定',
        'time' => '3天前'
    ]
];

echo json_encode([
    'code' => 200,
    'message' => '获取成功',
    'data' => $expectedActivities
], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

echo str_repeat("=", 60) . "\n";
echo "前端图表显示优化建议:\n";
echo str_repeat("=", 60) . "\n\n";

echo "1. 饼图显示优化:\n";
echo "   - 图例显示: '待办: 4个 (23.5%)'\n";
echo "   - 悬停提示: '待办任务共4个，占总任务的23.5%'\n";
echo "   - 使用 label 字段作为图例文本\n\n";

echo "2. 柱状图显示优化:\n";
echo "   - X轴标签: '低: 5个 (29.4%)'\n";
echo "   - 悬停提示: '低优先级任务共5个，占总任务的29.4%'\n";
echo "   - 使用 label 字段作为轴标签\n\n";

echo "3. 成员统计显示优化:\n";
echo "   - 显示格式: '租户超级管理员 - 已完成 1/4 个任务 (25%)'\n";
echo "   - 进度条显示完成率\n";
echo "   - 使用 status_text 字段显示详细信息\n\n";

echo "4. 最近活动显示优化:\n";
echo "   - 显示格式: 'CRM管理员 跟进了任务 部署环境准备 - 1天前'\n";
echo "   - 按时间倒序排列\n";
echo "   - 支持点击查看详情\n\n";

echo str_repeat("=", 60) . "\n";
echo "测试完成！\n";
echo "现在您可以刷新前端页面，应该能看到真实的统计数据。\n";
echo str_repeat("=", 60) . "\n";
?>
