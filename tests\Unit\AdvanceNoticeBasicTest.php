<?php
declare(strict_types=1);

namespace tests\Unit;

use PHPUnit\Framework\TestCase;

// 定义测试环境常量
if (!defined('PHPUNIT_RUNNING')) {
    define('PHPUNIT_RUNNING', true);
}

/**
 * 提前通知服务基础测试
 */
class AdvanceNoticeBasicTest extends TestCase
{
    /**
     * 测试AdvanceNoticeService文件是否存在
     */
    public function testAdvanceNoticeServiceFileExists(): void
    {
        $serviceFile = __DIR__ . '/../../workerman/services/AdvanceNoticeService.php';
        $this->assertFileExists($serviceFile);
        $this->assertFileIsReadable($serviceFile);
        $this->assertGreaterThan(0, filesize($serviceFile));
    }
    
    /**
     * 测试AdvanceNoticeService文件语法
     */
    public function testAdvanceNoticeServiceSyntax(): void
    {
        $serviceFile = __DIR__ . '/../../workerman/services/AdvanceNoticeService.php';
        $content = file_get_contents($serviceFile);
        
        // 检查基本语法结构
        $this->assertStringContainsString('<?php', $content);
        $this->assertStringContainsString('declare(strict_types=1);', $content);
        $this->assertStringContainsString('namespace workerman\services;', $content);
        $this->assertStringContainsString('class AdvanceNoticeService extends WorkerBase', $content);
        
        // 检查关键方法
        $this->assertStringContainsString('public function executeAdvanceNotice', $content);
        $this->assertStringContainsString('private function getCustomersNearRecycle', $content);
        $this->assertStringContainsString('private function shouldSendNotice', $content);
        $this->assertStringContainsString('private function sendAdvanceNotice', $content);
        $this->assertStringContainsString('private function recordNoticeLog', $content);
    }
    
    /**
     * 测试UnifiedWorker是否包含提前通知功能
     */
    public function testUnifiedWorkerAdvanceNoticeIntegration(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查是否引入了AdvanceNoticeService
        $this->assertStringContainsString('use workerman\services\AdvanceNoticeService;', $content);
        
        // 检查是否添加了提前通知定时器
        $this->assertStringContainsString('advance_notice', $content);
        $this->assertStringContainsString('executeAdvanceNotice', $content);
        
        // 检查定时器配置
        $this->assertStringContainsString('提前通知检查', $content);
    }
    
    /**
     * 测试启动脚本是否包含AdvanceNoticeService
     */
    public function testStartScriptIncludesAdvanceNoticeService(): void
    {
        $startFile = __DIR__ . '/../../workerman/start.php';
        $content = file_get_contents($startFile);
        
        // 检查是否包含AdvanceNoticeService文件
        $this->assertStringContainsString('services/AdvanceNoticeService.php', $content);
        
        // 检查ThinkPHP应用初始化
        $this->assertStringContainsString('use think\App;', $content);
        $this->assertStringContainsString('$app = new App();', $content);
        $this->assertStringContainsString('$app->initialize();', $content);
        $this->assertStringContainsString('Container::setInstance($app);', $content);
    }
    
    /**
     * 测试配置结构
     */
    public function testAdvanceNoticeConfigStructure(): void
    {
        // 测试默认配置结构
        $defaultConfig = [
            'notice_enabled' => false,
            'notify_days' => 3,
            'notice_channels' => ['site'],
            'notice_target' => 'owner',
            'notice_frequency' => 'once',
            'notice_template' => '您负责的客户 {{customer_name}} 将在 {{days}} 天后被回收到公海，请及时跟进。'
        ];
        
        // 验证配置键
        $this->assertArrayHasKey('notice_enabled', $defaultConfig);
        $this->assertArrayHasKey('notify_days', $defaultConfig);
        $this->assertArrayHasKey('notice_channels', $defaultConfig);
        $this->assertArrayHasKey('notice_target', $defaultConfig);
        $this->assertArrayHasKey('notice_frequency', $defaultConfig);
        $this->assertArrayHasKey('notice_template', $defaultConfig);
        
        // 验证数据类型
        $this->assertIsBool($defaultConfig['notice_enabled']);
        $this->assertIsInt($defaultConfig['notify_days']);
        $this->assertIsArray($defaultConfig['notice_channels']);
        $this->assertIsString($defaultConfig['notice_target']);
        $this->assertIsString($defaultConfig['notice_frequency']);
        $this->assertIsString($defaultConfig['notice_template']);
    }
    
    /**
     * 测试通知频率选项
     */
    public function testNoticeFrequencyOptions(): void
    {
        $validFrequencies = ['once', 'daily', 'every_2_days'];
        
        foreach ($validFrequencies as $frequency) {
            $this->assertIsString($frequency);
            $this->assertNotEmpty($frequency);
        }
        
        // 验证频率含义
        $this->assertEquals('once', $validFrequencies[0]); // 仅通知一次
        $this->assertEquals('daily', $validFrequencies[1]); // 每天通知
        $this->assertEquals('every_2_days', $validFrequencies[2]); // 每2天通知
    }
    
    /**
     * 测试模板变量
     */
    public function testTemplateVariables(): void
    {
        $template = '您负责的客户 {{customer_name}} 将在 {{days}} 天后被回收到公海，请及时跟进。';
        
        // 检查必要的模板变量
        $this->assertStringContainsString('{{customer_name}}', $template);
        $this->assertStringContainsString('{{days}}', $template);
        
        // 测试变量替换
        $content = str_replace([
            '{{customer_name}}',
            '{{days}}'
        ], [
            '测试客户',
            '3'
        ], $template);
        
        $expected = '您负责的客户 测试客户 将在 3 天后被回收到公海，请及时跟进。';
        $this->assertEquals($expected, $content);
    }
    
    /**
     * 测试日期计算
     */
    public function testDateCalculations(): void
    {
        // 测试今天日期格式
        $today = date('Y-m-d');
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $today);
        
        // 测试时间戳计算
        $now = time();
        $threeDaysLater = $now + (3 * 24 * 3600);
        $calculatedDate = date('Y-m-d', $threeDaysLater);
        
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $calculatedDate);
        $this->assertGreaterThan($today, $calculatedDate);
    }
    
    /**
     * 测试数据库表结构（模拟）
     */
    public function testAdvanceNoticeLogStructure(): void
    {
        // 模拟crm_advance_notice_log表结构
        $logRecord = [
            'id' => 1,
            'customer_id' => 123,
            'notice_date' => '2025-08-04',
            'days_before_recycle' => 3,
            'sent_at' => '2025-08-04 10:30:00',
            'tenant_id' => 1
        ];
        
        // 验证字段存在
        $requiredFields = ['id', 'customer_id', 'notice_date', 'days_before_recycle', 'sent_at', 'tenant_id'];
        foreach ($requiredFields as $field) {
            $this->assertArrayHasKey($field, $logRecord);
        }
        
        // 验证数据格式
        $this->assertIsInt($logRecord['id']);
        $this->assertIsInt($logRecord['customer_id']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $logRecord['notice_date']);
        $this->assertIsInt($logRecord['days_before_recycle']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $logRecord['sent_at']);
        $this->assertIsInt($logRecord['tenant_id']);
    }
}
