# Workerman自动化系统技术架构详细说明

## 📋 架构概述

Workerman自动化系统采用**事件驱动 + 定时任务**的混合架构，基于PHP 8.2 + Workerman 4.2.1 + ThinkPHP 8构建，实现了高性能、高可靠性的企业级自动化解决方案。

## 🏗️ 整体架构图

```
┌─────────────────────────────────────────────────────────────────────┐
│                          客户端层                                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│  │   Web浏览器     │  │   移动端应用     │  │   API客户端     │      │
│  │  (WebSocket)    │  │  (HTTP API)     │  │  (REST API)     │      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
└─────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                        网关/负载均衡层                               │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │              Nginx / Apache (可选)                             │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                      Workerman应用层                                │
│  ┌─────────────────────────────────────────────────────────────────┐ │
│  │                    UnifiedWorker                                │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │ │
│  │  │  WebSocket服务  │  │   定时器管理     │  │   连接管理       │  │ │
│  │  │   (端口8282)    │  │  (6个定时器)    │  │  (用户会话)     │  │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘  │ │
│  └─────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                        业务服务层                                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│  │AdvanceNotice    │  │ TaskReminder    │  │  ConfigManager  │      │
│  │    Service      │  │    Service      │  │                 │      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│  │   WorkerBase    │  │  其他业务服务    │  │   工具类服务     │      │
│  │   (基础类)      │  │                 │  │                 │      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
└─────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                      ThinkPHP框架层                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│  │   模型层(ORM)   │  │   服务层        │  │   控制器层       │      │
│  │   Customer      │  │ CustomerSea     │  │   API接口       │      │
│  │   Task          │  │ NoticeDispatch  │  │                 │      │
│  │   Follow        │  │ NoticeQueue     │  │                 │      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
└─────────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────────┐
│                        数据存储层                                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │
│  │   MySQL数据库   │  │   Redis缓存     │  │   文件存储       │      │
│  │   业务数据      │  │   配置缓存      │  │   日志文件       │      │
│  │   日志数据      │  │   会话数据      │  │   临时文件       │      │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘      │
└─────────────────────────────────────────────────────────────────────┘
```

## 🔧 核心组件详解

### 1. UnifiedWorker统一工作进程

**职责**：系统的核心调度器，负责所有自动化功能的统一管理

**核心特性**：
- **单进程架构**：所有功能集成在一个Worker进程中
- **事件驱动**：基于Workerman的异步事件循环
- **定时器管理**：管理6个不同频率的定时任务
- **WebSocket服务**：提供实时双向通信能力

**技术实现**：
```php
class UnifiedWorker extends WorkerBase
{
    private Worker $wsWorker;           // WebSocket服务器
    private array $userConnections;     // 用户连接映射
    private array $timers;              // 定时器ID映射
    
    // 6个定时器的管理
    private function startTimers(): void
    {
        // 公海回收：每小时
        $this->timers['sea_recycle'] = Timer::add(3600, ...);
        
        // 消息队列：每10秒
        $this->timers['message_queue'] = Timer::add(10, ...);
        
        // 延迟消息：每5分钟
        $this->timers['delayed_messages'] = Timer::add(300, ...);
        
        // 提前通知：每小时
        $this->timers['advance_notice'] = Timer::add(3600, ...);
        
        // 任务提醒：每30分钟
        $this->timers['task_reminder'] = Timer::add(1800, ...);
        
        // 内存监控：每30分钟
        $this->timers['memory_monitor'] = Timer::add(1800, ...);
    }
}
```

### 2. 业务服务层架构

#### WorkerBase基础服务类
**职责**：为所有业务服务提供统一的基础功能

**核心功能**：
- **日志管理**：统一的日志记录机制
- **异常处理**：安全执行和异常捕获
- **内存监控**：内存使用情况监控
- **工具方法**：字节格式化等工具方法

**技术实现**：
```php
abstract class WorkerBase
{
    // 安全执行方法
    protected function safeExecute(callable $callback, string $operation): bool
    {
        try {
            $callback();
            return true;
        } catch (\Throwable $e) {
            $this->log("执行{$operation}时发生异常: " . $e->getMessage(), 'error');
            return false;
        }
    }
    
    // 环境自适应日志
    protected function log(string $message, string $level = 'info'): void
    {
        if (defined('WORKERMAN_RUNNING')) {
            // Workerman环境：文件日志
            $this->writeToFile($message, $level);
        } else {
            // ThinkPHP环境：框架日志
            Log::write($message, $level);
        }
    }
}
```

#### ConfigManager配置管理器
**职责**：统一的配置读取、缓存和验证

**核心特性**：
- **多租户支持**：按租户隔离配置
- **缓存机制**：5分钟配置缓存
- **默认值处理**：完整的默认配置
- **验证机制**：配置有效性验证

**技术实现**：
```php
class ConfigManager
{
    private const CACHE_PREFIX = 'workerman:config:';
    private const CACHE_TTL = 300; // 5分钟
    
    public static function getSeaRuleConfig(int $tenantId = 0): array
    {
        $cacheKey = self::CACHE_PREFIX . "sea_rule:{$tenantId}";
        
        $config = Cache::get($cacheKey);
        if ($config === null) {
            $config = self::getTenantConfigService()->getInfo('sea_rule');
            $config = array_merge(self::getDefaultSeaRuleConfig(), $config ?: []);
            Cache::set($cacheKey, $config, self::CACHE_TTL);
        }
        
        return $config;
    }
}
```

#### AdvanceNoticeService提前通知服务
**职责**：客户回收预警通知

**核心算法**：
```php
// 计算即将被回收的客户
private function getCustomersNearRecycle(int $notifyDays): array
{
    $followDays = 15; // 公海规则：15天未跟进
    $criticalDays = $followDays - $notifyDays; // 临界天数
    $criticalDate = date('Y-m-d H:i:s', strtotime("-{$criticalDays} days"));
    
    return Db::table('crm_customer')
        ->where('in_sea', 0)
        ->where('owner_user_id', '>', 0)
        ->where(function($query) use ($criticalDate) {
            $query->whereNull('last_follow_time')
                  ->whereOr('last_follow_time', '<=', $criticalDate);
        })
        ->limit(100)
        ->select();
}
```

#### TaskReminderService任务提醒服务
**职责**：任务状态智能提醒

**三种提醒类型**：
1. **逾期提醒**：`due_date < NOW()`
2. **即将到期**：`DATE(due_date) = '目标日期'`
3. **跟进计划**：`next_date <= '提前时间'`

### 3. 数据层架构

#### 数据表设计
**现有业务表**：
- `crm_customer`：客户信息表
- `project_task`：项目任务表
- `project_task_record`：任务跟进记录表
- `crm_follow_record`：CRM跟进记录表

**新增日志表**：
- `crm_advance_notice_log`：提前通知记录表
- `project_task_reminder_log`：任务提醒记录表

#### 防重复机制设计
```sql
-- 唯一约束防止重复
UNIQUE KEY `uk_customer_date` (`customer_id`, `notice_date`)
UNIQUE KEY `uk_task_type_date` (`task_id`, `reminder_type`, `reminder_date`)

-- 业务逻辑防重复
SELECT COUNT(*) FROM crm_advance_notice_log 
WHERE customer_id = ? AND notice_date = ?
```

## 🔄 数据流架构

### 1. 定时任务数据流
```
定时器触发 → 读取配置 → 查询数据 → 业务处理 → 发送通知 → 记录日志
     ↓           ↓         ↓         ↓         ↓         ↓
  Timer.add  ConfigManager  Database  Service  Notice   LogTable
```

### 2. WebSocket通信流
```
客户端连接 → 用户认证 → 连接管理 → 消息推送 → 状态更新
     ↓          ↓         ↓         ↓         ↓
  WebSocket   Auth     Connection  Message   UI Update
```

### 3. 配置管理流
```
用户配置 → 参数验证 → 缓存更新 → 配置生效 → 业务调整
     ↓         ↓         ↓         ↓         ↓
   UI Form   Validate   Cache    Config   Service
```

## 🚀 性能架构

### 1. 并发处理能力
- **单进程模型**：避免进程间通信开销
- **事件驱动**：高效的I/O处理
- **异步执行**：非阻塞的任务处理
- **连接复用**：WebSocket长连接复用

### 2. 内存管理策略
- **按需加载**：服务实例按需创建
- **及时释放**：处理完成后释放资源
- **内存监控**：定期检查内存使用
- **垃圾回收**：PHP自动垃圾回收

### 3. 数据库优化
- **索引优化**：为查询条件建立索引
- **分批处理**：大量数据分批处理
- **连接复用**：复用ThinkPHP连接池
- **查询优化**：避免N+1查询问题

## 🔒 安全架构

### 1. 数据安全
- **租户隔离**：多租户数据完全隔离
- **权限验证**：复用现有权限系统
- **SQL注入防护**：ORM和参数化查询
- **数据验证**：严格的输入验证

### 2. 系统安全
- **异常隔离**：单个功能异常不影响整体
- **资源限制**：处理数量限制防止资源耗尽
- **日志审计**：完整的操作日志
- **配置验证**：配置参数有效性验证

### 3. 通信安全
- **WebSocket认证**：用户身份验证
- **消息加密**：敏感消息加密传输
- **连接管理**：安全的连接建立和断开
- **防重放攻击**：消息时间戳验证

## 📊 监控架构

### 1. 系统监控
- **进程监控**：Worker进程状态监控
- **内存监控**：内存使用情况监控
- **连接监控**：WebSocket连接数监控
- **性能监控**：处理时间和吞吐量监控

### 2. 业务监控
- **执行统计**：各种任务的执行统计
- **成功率监控**：任务执行成功率
- **通知统计**：通知发送统计
- **用户活跃度**：用户连接和交互统计

### 3. 日志架构
```php
// 多层次日志系统
[时间戳] [级别] [组件] 消息内容 [上下文数据]

// 示例
[2025-08-04 23:08:08] [info] [UnifiedWorker] 任务提醒检查完成 {"sent_count": 5}
[2025-08-04 23:08:08] [error] [TaskReminderService] 发送提醒失败 {"task_id": 123}
```

## 🔧 扩展架构

### 1. 水平扩展
- **多进程部署**：支持多个Worker进程
- **负载均衡**：Nginx负载均衡
- **数据库分片**：支持数据库读写分离
- **缓存集群**：Redis集群支持

### 2. 垂直扩展
- **服务拆分**：按功能拆分独立服务
- **微服务化**：服务间API通信
- **消息队列**：异步消息处理
- **容器化部署**：Docker容器部署

### 3. 功能扩展
- **插件机制**：支持功能插件
- **规则引擎**：可配置的业务规则
- **工作流引擎**：复杂业务流程
- **API网关**：统一的API管理

## 📈 架构优势

### 1. 高性能
- **事件驱动**：高并发处理能力
- **内存常驻**：避免重复初始化
- **异步处理**：非阻塞I/O操作
- **连接复用**：减少连接开销

### 2. 高可靠
- **异常隔离**：单点故障不影响整体
- **自动恢复**：异常自动恢复机制
- **完整日志**：便于问题排查
- **监控告警**：及时发现问题

### 3. 高扩展
- **模块化设计**：功能模块独立
- **配置驱动**：灵活的配置管理
- **插件架构**：支持功能扩展
- **标准接口**：统一的服务接口

### 4. 易维护
- **清晰架构**：层次分明的架构设计
- **规范代码**：统一的代码规范
- **完整文档**：详细的技术文档
- **测试覆盖**：全面的单元测试

## 🎯 架构总结

Workerman自动化系统采用了现代化的架构设计理念，在保证高性能的同时，兼顾了可靠性、扩展性和可维护性。通过合理的分层设计和模块化架构，系统能够灵活应对业务需求的变化，为企业提供稳定可靠的自动化解决方案。

这个架构不仅解决了当前的业务需求，更为未来的功能扩展和性能优化奠定了坚实的技术基础。
