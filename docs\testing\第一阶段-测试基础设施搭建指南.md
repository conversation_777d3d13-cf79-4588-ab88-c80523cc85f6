# 第一阶段：测试基础设施搭建指南

## 📋 概述

本阶段将搭建完整的测试环境和工具链，为后续的集成测试提供坚实的基础。预计耗时2-3天。

## 🎯 阶段目标

1. 配置前端测试框架（Vitest + Vue Test Utils）
2. 准备独立的测试数据库和测试数据
3. 优化Playwright端到端测试环境
4. 完善后端API测试工具配置
5. 建立测试报告和监控系统

## 📝 任务清单

### 任务1：前端测试框架配置

#### 1.1 安装测试依赖
```bash
cd frontend
npm install --save-dev vitest @vue/test-utils jsdom @vitest/ui
npm install --save-dev @testing-library/vue @testing-library/jest-dom
```

#### 1.2 创建Vitest配置文件
**文件路径**: `frontend/vitest.config.ts`
```typescript
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./src/test/setup.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*'
      ]
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@views': resolve(__dirname, 'src/views'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@api': resolve(__dirname, 'src/api'),
      '@store': resolve(__dirname, 'src/store')
    }
  }
})
```

#### 1.3 创建测试环境配置
**文件路径**: `frontend/src/test/setup.ts`
```typescript
import { config } from '@vue/test-utils'
import { vi } from 'vitest'
import '@testing-library/jest-dom'

// 全局测试配置
config.global.mocks = {
  $t: (key: string) => key, // 模拟国际化
  $router: {
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn()
  },
  $route: {
    path: '/',
    params: {},
    query: {},
    meta: {}
  }
}

// 模拟Element Plus组件
vi.mock('element-plus', () => ({
  ElMessage: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn()
  },
  ElMessageBox: {
    confirm: vi.fn(),
    alert: vi.fn(),
    prompt: vi.fn()
  }
}))

// 模拟API请求
vi.mock('@/utils/request', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn()
  }
}))
```

#### 1.4 更新package.json脚本
```json
{
  "scripts": {
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:run": "vitest run",
    "test:coverage": "vitest run --coverage"
  }
}
```

### 任务2：测试数据库环境准备

#### 2.1 创建测试数据库
```sql
-- 创建测试数据库
CREATE DATABASE base_admin_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建测试用户
CREATE USER 'test_user'@'localhost' IDENTIFIED BY 'test_password';
GRANT ALL PRIVILEGES ON base_admin_test.* TO 'test_user'@'localhost';
FLUSH PRIVILEGES;
```

#### 2.2 配置测试环境变量
**文件路径**: `.env.testing`
```env
APP_ENV=testing
APP_DEBUG=true

# 数据库配置
DATABASE_TYPE=mysql
DATABASE_HOSTNAME=127.0.0.1
DATABASE_DATABASE=base_admin_test
DATABASE_USERNAME=test_user
DATABASE_PASSWORD=test_password
DATABASE_HOSTPORT=3306
DATABASE_CHARSET=utf8mb4

# Redis配置
REDIS_HOSTNAME=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SELECT=1

# 缓存配置
CACHE_DRIVER=redis
SESSION_DRIVER=redis
```

#### 2.3 创建测试数据初始化脚本
**文件路径**: `tests/data/init_test_data.sql`
```sql
-- 租户数据
INSERT INTO system_tenant (id, name, code, domain, status, created_at) VALUES
(1, '测试租户A', 'test_tenant_a', 'test-a.com', 1, NOW()),
(2, '测试租户B', 'test_tenant_b', 'test-b.com', 1, NOW()),
(3, '测试租户C', 'test_tenant_c', 'test-c.com', 1, NOW());

-- 部门数据
INSERT INTO system_dept (id, parent_id, name, code, sort, status, tenant_id, created_at) VALUES
(1, 0, '总公司', 'HQ', 1, 1, 1, NOW()),
(2, 1, '技术部', 'TECH', 1, 1, 1, NOW()),
(3, 1, '销售部', 'SALES', 2, 1, 1, NOW()),
(4, 2, '前端组', 'FE', 1, 1, 1, NOW()),
(5, 2, '后端组', 'BE', 2, 1, 1, NOW());

-- 角色数据
INSERT INTO system_role (id, name, code, data_scope, status, tenant_id, created_at) VALUES
(1, '超级管理员', 'super_admin', 1, 1, 1, NOW()),
(2, '部门管理员', 'dept_admin', 2, 1, 1, NOW()),
(3, '普通用户', 'user', 4, 1, 1, NOW()),
(4, '销售经理', 'sales_manager', 3, 1, 1, NOW());

-- 用户数据
INSERT INTO system_admin (id, username, password, salt, real_name, dept_id, status, tenant_id, created_at) VALUES
(1, 'admin', 'hashed_password', 'salt123', '系统管理员', 1, 1, 1, NOW()),
(2, 'dept_admin', 'hashed_password', 'salt123', '部门管理员', 2, 1, 1, NOW()),
(3, 'user1', 'hashed_password', 'salt123', '普通用户1', 4, 1, 1, NOW()),
(4, 'user2', 'hashed_password', 'salt123', '普通用户2', 5, 1, 1, NOW()),
(5, 'sales_manager', 'hashed_password', 'salt123', '销售经理', 3, 1, 1, NOW());

-- 用户角色关联
INSERT INTO system_admin_role (admin_id, role_id, tenant_id) VALUES
(1, 1, 1), (2, 2, 1), (3, 3, 1), (4, 3, 1), (5, 4, 1);
```

### 任务3：Playwright测试环境优化

#### 3.1 优化Playwright配置
**文件路径**: `playwright-mcp-config.json`（更新现有配置）
```json
{
  "browser": {
    "browserName": "chromium",
    "isolated": false,
    "launchOptions": {
      "headless": false,
      "channel": "chrome",
      "args": [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor"
      ]
    },
    "contextOptions": {
      "viewport": { "width": 1280, "height": 720 },
      "ignoreHTTPSErrors": true,
      "acceptDownloads": true,
      "recordVideo": {
        "dir": "./runtime/playwright-videos/",
        "size": { "width": 1280, "height": 720 }
      }
    }
  },
  "server": {
    "port": 8931,
    "host": "localhost"
  },
  "capabilities": [
    "tabs",
    "pdf",
    "vision",
    "screenshots",
    "videos"
  ],
  "outputDir": "./runtime/playwright-output",
  "network": {
    "allowedOrigins": [
      "http://localhost:3006",
      "http://localhost:3008", 
      "http://www.bs.com",
      "https://www.bs.com",
      "http://127.0.0.1:3006",
      "http://127.0.0.1:3008"
    ]
  },
  "imageResponses": "allow",
  "testConfig": {
    "timeout": 30000,
    "retries": 2,
    "workers": 1
  }
}
```

#### 3.2 创建页面对象模型基类
**文件路径**: `tests/e2e/pages/BasePage.js`
```javascript
class BasePage {
  constructor(page) {
    this.page = page;
    this.baseUrl = 'http://localhost:3006';
  }

  async goto(path = '') {
    await this.page.goto(`${this.baseUrl}${path}`);
  }

  async waitForLoad() {
    await this.page.waitForLoadState('networkidle');
  }

  async takeScreenshot(name) {
    await this.page.screenshot({ 
      path: `./runtime/playwright-output/screenshots/${name}.png`,
      fullPage: true 
    });
  }

  async login(username = 'admin', password = '123456') {
    await this.goto('/login');
    await this.page.fill('input[placeholder="用户名"]', username);
    await this.page.fill('input[placeholder="密码"]', password);
    await this.page.click('button[type="submit"]');
    await this.waitForLoad();
  }

  async logout() {
    await this.page.click('.user-dropdown');
    await this.page.click('text=退出登录');
    await this.waitForLoad();
  }
}

module.exports = BasePage;
```

### 任务4：API测试工具配置

#### 4.1 创建API测试基类
**文件路径**: `tests/Unit/BaseApiTest.php`
```php
<?php

namespace tests\Unit;

use PHPUnit\Framework\TestCase;
use think\facade\Db;
use think\facade\Cache;

abstract class BaseApiTest extends TestCase
{
    protected $baseUrl = 'http://localhost:3008';
    protected $token = '';
    protected $tenantId = 1;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // 开启数据库事务
        Db::startTrans();
        
        // 清理缓存
        Cache::clear();
        
        // 设置测试环境
        putenv('APP_ENV=testing');
        
        // 获取测试token
        $this->token = $this->getTestToken();
    }
    
    protected function tearDown(): void
    {
        // 回滚数据库事务
        Db::rollback();
        
        parent::tearDown();
    }
    
    protected function getTestToken($username = 'admin', $password = '123456')
    {
        $response = $this->post('/api/system/login', [
            'username' => $username,
            'password' => $password
        ]);
        
        return $response['data']['token'] ?? '';
    }
    
    protected function get($url, $headers = [])
    {
        return $this->makeRequest('GET', $url, [], $headers);
    }
    
    protected function post($url, $data = [], $headers = [])
    {
        return $this->makeRequest('POST', $url, $data, $headers);
    }
    
    protected function put($url, $data = [], $headers = [])
    {
        return $this->makeRequest('PUT', $url, $data, $headers);
    }
    
    protected function delete($url, $headers = [])
    {
        return $this->makeRequest('DELETE', $url, [], $headers);
    }
    
    private function makeRequest($method, $url, $data = [], $headers = [])
    {
        $defaultHeaders = [
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->token,
            'X-Tenant-Id' => $this->tenantId
        ];
        
        $headers = array_merge($defaultHeaders, $headers);
        
        // 这里使用curl或其他HTTP客户端发送请求
        // 返回解析后的响应数据
        return $this->sendHttpRequest($method, $this->baseUrl . $url, $data, $headers);
    }
    
    private function sendHttpRequest($method, $url, $data, $headers)
    {
        // 实现HTTP请求逻辑
        // 返回JSON解析后的数组
    }
    
    protected function assertApiSuccess($response)
    {
        $this->assertEquals(200, $response['code']);
        $this->assertEquals('success', $response['msg']);
    }
    
    protected function assertApiError($response, $expectedCode = 400)
    {
        $this->assertEquals($expectedCode, $response['code']);
        $this->assertNotEquals('success', $response['msg']);
    }
}
```

### 任务5：测试报告和监控系统

#### 5.1 配置PHPUnit测试报告
**文件路径**: `phpunit.xml`（更新现有配置）
```xml
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="tests/bootstrap.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false"
         verbose="true">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory suffix="Test.php">./tests/Integration</directory>
        </testsuite>
        <testsuite name="API">
            <directory suffix="Test.php">./tests/Api</directory>
        </testsuite>
    </testsuites>
    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">./app</directory>
        </include>
        <exclude>
            <directory>./app/common/generator</directory>
            <file>./app/common.php</file>
        </exclude>
        <report>
            <html outputDirectory="./runtime/coverage-html"/>
            <text outputFile="./runtime/coverage.txt"/>
            <xml outputDirectory="./runtime/coverage-xml"/>
        </report>
    </coverage>
    <logging>
        <junit outputFile="./runtime/junit.xml"/>
    </logging>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="QUEUE_DRIVER" value="sync"/>
    </php>
</phpunit>
```

#### 5.2 创建测试执行脚本
**文件路径**: `scripts/run_tests.sh`
```bash
#!/bin/bash

echo "=== 系统集成测试执行脚本 ==="
echo "开始时间: $(date)"

# 创建输出目录
mkdir -p runtime/test-reports
mkdir -p runtime/playwright-output
mkdir -p runtime/coverage-html

# 1. 初始化测试数据
echo "1. 初始化测试数据..."
mysql -u test_user -ptest_password base_admin_test < tests/data/init_test_data.sql

# 2. 运行后端单元测试
echo "2. 运行后端单元测试..."
./vendor/bin/phpunit --testsuite=Unit --coverage-html=runtime/coverage-html

# 3. 运行API集成测试
echo "3. 运行API集成测试..."
./vendor/bin/phpunit --testsuite=API

# 4. 运行前端单元测试
echo "4. 运行前端单元测试..."
cd frontend
npm run test:coverage
cd ..

# 5. 生成测试报告
echo "5. 生成测试报告..."
php scripts/generate_test_report.php

echo "测试完成时间: $(date)"
echo "测试报告位置: runtime/test-reports/"
```

## ✅ 验收标准

### 任务完成标准
- [ ] 前端测试框架配置完成，可以运行基本测试
- [ ] 测试数据库创建完成，包含完整的测试数据
- [ ] Playwright配置优化完成，支持截图和视频录制
- [ ] API测试基类创建完成，支持认证和数据清理
- [ ] 测试报告系统配置完成，可以生成HTML报告

### 质量检查
1. 运行 `npm run test` 确保前端测试环境正常
2. 运行 `./vendor/bin/phpunit --testsuite=Unit` 确保后端测试环境正常
3. 检查测试数据库是否包含完整的测试数据
4. 验证Playwright是否可以正常启动和截图
5. 确认测试报告可以正常生成

## 📋 下一步计划

完成本阶段后，将进入第二阶段：核心功能集成测试，重点测试：
1. 用户认证系统
2. RBAC权限系统
3. 多租户数据隔离
4. 系统管理模块
5. CRM模块
6. 项目任务管理模块

---

**预计完成时间**: 2-3天  
**负责人**: 测试团队  
**审核人**: 技术负责人
