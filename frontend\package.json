{"name": "vue-ts-vite-demo", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --open", "build": "vue-tsc --noEmit && vite build", "serve": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint", "fix": "eslint --fix", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "commit": "git-cz"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix --allow-empty-input"], "*.{scss,css}": ["stylelint --fix --allow-empty-input", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@form-create/designer": "^3.2.11", "@pureadmin/utils": "^2.6.0", "@types/uuid": "^10.0.0", "@vue/reactivity": "^3.4.35", "@vueform/multiselect": "^2.6.11", "@vueuse/core": "^11.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "next", "axios": "^1.8.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "default-passive-events": "^2.0.0", "echarts": "^5.4.0", "element-china-area-data": "^6.1.0", "element-plus": "^2.8.0", "epic-designer": "^1.0.1", "file-saver": "^2.0.5", "highlight.js": "^11.10.0", "md-editor-v3": "^4.17.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "qiniu-js": "3.4.2", "qrcode.vue": "^3.6.0", "uuid": "^11.1.0", "vue": "^3.5.12", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^9.14.0", "vue-router": "^4.4.2", "vue3-count-to": "^1.1.2", "vuedraggable": "^4.1.0", "xgplayer": "^3.0.20", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.4.1", "@commitlint/config-conventional": "^19.4.1", "@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.1.4", "@testing-library/vue": "^8.0.1", "@types/axios": "^0.14.4", "@types/node": "^22.1.0", "@types/nprogress": "^0.2.3", "@types/qiniu-js": "^3.0.0", "@types/vue-router": "^2.0.0", "@typescript-eslint/eslint-plugin": "^8.3.0", "@typescript-eslint/parser": "^8.3.0", "@vitejs/plugin-vue": "^5.2.1", "@vitest/ui": "^1.0.4", "@vue/compiler-sfc": "^3.0.5", "@vue/test-utils": "^2.4.1", "autoprefixer": "^10.4.21", "commitizen": "^4.3.0", "cz-git": "^1.9.4", "eslint": "^9.9.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "globals": "^15.9.0", "husky": "^9.1.5", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.81.0", "stylelint": "^16.8.2", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "terser": "^5.36.0", "typescript": "~5.6.3", "typescript-eslint": "^8.9.0", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^6.3.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vitest": "^1.0.4", "vue-demi": "^0.14.9", "vue-img-cutter": "^3.0.5", "vue-tsc": "~2.1.6"}}