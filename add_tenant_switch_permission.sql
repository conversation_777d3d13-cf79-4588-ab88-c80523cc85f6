-- 添加租户切换权限到系统菜单表
-- 执行时间: 2025-08-04

-- 1. 添加租户切换权限到租户列表菜单下
INSERT INTO `system_menu` (`parent_id`, `title`, `name`, `path`, `component`, `type`, `icon`, `sort`, `external`, `keep_alive`, `visible`, `status`, `remark`, `created_at`, `updated_at`) 
VALUES 
(
    (SELECT id FROM (SELECT id FROM `system_menu` WHERE `name` = 'system:tenant:tenantlist' LIMIT 1) AS temp),
    '租户切换',
    'system:tenant:switch',
    '',
    '',
    2,  -- 按钮类型
    '',
    100,
    0,
    0,
    1,
    1,
    '超级管理员租户切换权限',
    NOW(),
    NOW()
);

-- 2. 获取刚插入的权限ID和超级管理员角色ID
SET @switch_permission_id = LAST_INSERT_ID();
SET @super_admin_role_id = (SELECT id FROM `system_role` WHERE `name` = '超级管理员' OR `name` = 'Super Admin' LIMIT 1);

-- 3. 如果找不到超级管理员角色，创建一个（通常应该已存在）
INSERT IGNORE INTO `system_role` (`name`, `sort`, `data_scope`, `status`, `remark`, `creator_id`, `tenant_id`, `created_at`, `updated_at`)
VALUES ('超级管理员', 0, 1, 1, '系统超级管理员角色', 1, 0, NOW(), NOW());

-- 重新获取超级管理员角色ID
SET @super_admin_role_id = (SELECT id FROM `system_role` WHERE `name` = '超级管理员' OR `name` = 'Super Admin' LIMIT 1);

-- 4. 将租户切换权限分配给超级管理员角色
INSERT INTO `system_role_menu` (`role_id`, `menu_id`, `tenant_id`, `created_at`, `updated_at`)
VALUES (@super_admin_role_id, @switch_permission_id, 0, NOW(), NOW());

-- 5. 验证插入结果
SELECT 
    m.id,
    m.title,
    m.name,
    m.type,
    m.status,
    'Permission Added' as result
FROM `system_menu` m 
WHERE m.name = 'system:tenant:switch';

SELECT 
    r.name as role_name,
    m.title as permission_title,
    m.name as permission_name,
    'Role Permission Assigned' as result
FROM `system_role_menu` rm
JOIN `system_role` r ON rm.role_id = r.id
JOIN `system_menu` m ON rm.menu_id = m.id
WHERE m.name = 'system:tenant:switch';

-- 6. 显示租户相关的所有权限（用于验证）
SELECT 
    m.id,
    m.parent_id,
    m.title,
    m.name,
    m.type,
    CASE m.type 
        WHEN 0 THEN '目录'
        WHEN 1 THEN '菜单'
        WHEN 2 THEN '按钮'
        ELSE '未知'
    END as type_name,
    m.status
FROM `system_menu` m 
WHERE m.name LIKE 'system:tenant:%' 
ORDER BY m.parent_id, m.sort, m.id;
