<?php
declare(strict_types=1);

namespace tests\Unit;

require_once __DIR__ . '/../../app/common.php';

use PHPUnit\Framework\TestCase;
use workerman\common\ConfigManager;

/**
 * UnifiedWorker单元测试
 */
class WorkermanUnifiedWorkerTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // 模拟租户环境
        if (!function_exists('get_tenant_id')) {
            function get_tenant_id() {
                return 1;
            }
        }
    }
    
    /**
     * 测试配置管理器 - 公海规则配置
     */
    public function testGetSeaRuleConfig(): void
    {
        $config = ConfigManager::getSeaRuleConfig();
        
        // 验证默认配置
        $this->assertIsArray($config);
        $this->assertArrayHasKey('sea_status', $config);
        $this->assertArrayHasKey('follow_days', $config);
        $this->assertArrayHasKey('deal_days', $config);
        $this->assertArrayHasKey('cron_expression', $config);
        $this->assertArrayHasKey('max_process_count', $config);
        
        // 验证默认值
        $this->assertEquals(0, $config['sea_status']);
        $this->assertEquals(15, $config['follow_days']);
        $this->assertEquals(30, $config['deal_days']);
        $this->assertEquals('0 * * * *', $config['cron_expression']);
        $this->assertEquals(100, $config['max_process_count']);
    }
    
    /**
     * 测试配置管理器 - 提前通知配置
     */
    public function testGetAdvanceNoticeConfig(): void
    {
        $config = ConfigManager::getAdvanceNoticeConfig();
        
        // 验证默认配置
        $this->assertIsArray($config);
        $this->assertArrayHasKey('notice_enabled', $config);
        $this->assertArrayHasKey('notify_days', $config);
        $this->assertArrayHasKey('notice_channels', $config);
        $this->assertArrayHasKey('notice_target', $config);
        $this->assertArrayHasKey('notice_frequency', $config);
        $this->assertArrayHasKey('notice_template', $config);
        
        // 验证默认值
        $this->assertFalse($config['notice_enabled']);
        $this->assertEquals(3, $config['notify_days']);
        $this->assertEquals(['site'], $config['notice_channels']);
        $this->assertEquals('owner', $config['notice_target']);
        $this->assertEquals('once', $config['notice_frequency']);
        $this->assertStringContainsString('{{customer_name}}', $config['notice_template']);
    }
    
    /**
     * 测试配置管理器 - 任务提醒配置
     */
    public function testGetTaskReminderConfig(): void
    {
        $config = ConfigManager::getTaskReminderConfig();
        
        // 验证默认配置
        $this->assertIsArray($config);
        $this->assertArrayHasKey('reminder_enabled', $config);
        $this->assertArrayHasKey('overdue_enabled', $config);
        $this->assertArrayHasKey('overdue_frequency', $config);
        $this->assertArrayHasKey('due_soon_enabled', $config);
        $this->assertArrayHasKey('due_soon_days', $config);
        $this->assertArrayHasKey('follow_up_enabled', $config);
        $this->assertArrayHasKey('follow_up_advance_hours', $config);
        
        // 验证默认值
        $this->assertFalse($config['reminder_enabled']);
        $this->assertTrue($config['overdue_enabled']);
        $this->assertEquals('daily', $config['overdue_frequency']);
        $this->assertTrue($config['due_soon_enabled']);
        $this->assertEquals([1, 3, 7], $config['due_soon_days']);
        $this->assertTrue($config['follow_up_enabled']);
        $this->assertEquals(2, $config['follow_up_advance_hours']);
    }
    
    /**
     * 测试配置验证 - 公海规则
     */
    public function testValidateSeaRuleConfig(): void
    {
        // 测试有效配置
        $validConfig = [
            'follow_days' => 15,
            'deal_days' => 30
        ];
        $errors = ConfigManager::validateConfig('sea_rule', $validConfig);
        $this->assertEmpty($errors);
        
        // 测试无效配置
        $invalidConfig = [
            'follow_days' => 0,
            'deal_days' => -1
        ];
        $errors = ConfigManager::validateConfig('sea_rule', $invalidConfig);
        $this->assertNotEmpty($errors);
        $this->assertCount(2, $errors);
    }
    
    /**
     * 测试配置验证 - 提前通知
     */
    public function testValidateAdvanceNoticeConfig(): void
    {
        // 测试启用但天数无效的配置
        $invalidConfig = [
            'notice_enabled' => true,
            'notify_days' => 0
        ];
        $errors = ConfigManager::validateConfig('advance_notice', $invalidConfig);
        $this->assertNotEmpty($errors);
        
        // 测试未启用的配置（应该通过验证）
        $validConfig = [
            'notice_enabled' => false,
            'notify_days' => 0
        ];
        $errors = ConfigManager::validateConfig('advance_notice', $validConfig);
        $this->assertEmpty($errors);
    }
    
    /**
     * 测试配置验证 - 任务提醒
     */
    public function testValidateTaskReminderConfig(): void
    {
        // 测试启用跟进提醒但小时数无效的配置
        $invalidConfig = [
            'reminder_enabled' => true,
            'follow_up_enabled' => true,
            'follow_up_advance_hours' => 0
        ];
        $errors = ConfigManager::validateConfig('task_reminder', $invalidConfig);
        $this->assertNotEmpty($errors);
        
        // 测试有效配置
        $validConfig = [
            'reminder_enabled' => true,
            'follow_up_enabled' => true,
            'follow_up_advance_hours' => 2
        ];
        $errors = ConfigManager::validateConfig('task_reminder', $validConfig);
        $this->assertEmpty($errors);
    }
    
    /**
     * 测试配置缓存
     */
    public function testConfigCache(): void
    {
        // 第一次获取配置（从数据库）
        $config1 = ConfigManager::getSeaRuleConfig();

        // 第二次获取配置（从缓存）
        $config2 = ConfigManager::getSeaRuleConfig();

        // 应该相同
        $this->assertEquals($config1, $config2);

        // 清除缓存
        try {
            ConfigManager::clearConfigCache('sea_rule');
        } catch (\Exception $e) {
            // 忽略缓存清除错误
        }

        // 再次获取配置（从数据库）
        $config3 = ConfigManager::getSeaRuleConfig();

        // 应该仍然相同
        $this->assertEquals($config1, $config3);
    }
    
    /**
     * 测试UnifiedWorker的公海回收方法
     */
    public function testExecuteSeaRecycle(): void
    {
        // 由于UnifiedWorker依赖Workerman的Worker类，这里只测试逻辑
        // 实际测试需要在集成测试中进行
        
        $this->assertTrue(true); // 占位测试
        
        // TODO: 实现更详细的测试
        // 1. 模拟配置启用/禁用
        // 2. 模拟CustomerSeaManagementService的返回值
        // 3. 验证通知发送逻辑
    }
    
    /**
     * 测试内存使用监控
     */
    public function testMemoryUsage(): void
    {
        // 测试内存格式化函数
        $reflection = new \ReflectionClass('workerman\common\WorkerBase');
        $method = $reflection->getMethod('formatBytes');
        $method->setAccessible(true);
        
        // 创建一个WorkerBase的匿名子类进行测试
        $worker = new class extends \workerman\common\WorkerBase {
            protected function onWorkerStart($worker): void {}
            public function testFormatBytes($bytes) {
                return $this->formatBytes($bytes);
            }
        };
        
        $this->assertEquals('1.00 KB', $worker->testFormatBytes(1024));
        $this->assertEquals('1.00 MB', $worker->testFormatBytes(1024 * 1024));
        $this->assertEquals('1.00 GB', $worker->testFormatBytes(1024 * 1024 * 1024));
    }
}
