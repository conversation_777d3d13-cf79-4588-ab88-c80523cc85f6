<?php
declare(strict_types=1);

namespace app\common\utils;

use app\common\core\constants\CacheConstant;
use think\facade\Cache;

/**
 * 令牌工具类
 */
class TokenUtil
{
	
	/**
	 * 生成token并缓存
	 *
	 * @param int   $userId   用户ID
	 * @param array $userData 用户数据
	 * @param int   $expire   过期时间（秒）
	 * @return string
	 */
	public static function generate(int $adminId, array $adminData = [], int $expire = 7200): string
	{
		// 生成唯一token
		$token = md5(uniqid((string)$adminId, true) . time() . rand(100000, 999999));
		
		// 缓存数据
		$cacheData = [
			'admin_id'     => $adminId,
			'data'        => $adminData,
			'create_time' => time()
		];
		
		// 设置缓存，使用用户ID作为tag
		Cache::tag(self::getUserTag())
		     ->set($token, $cacheData, $expire);
		
		return $token;
	}
	
	/**
	 * 刷新token有效期
	 *
	 * @param string $token  token字符串
	 * @param int    $expire 过期时间（秒）
	 * @return bool
	 */
	public static function refresh(string $token, int $expire = 7200): bool
	{
		if (!$tokenData = Cache::get($token)) {
			return false;
		}
		
		// 更新缓存时间
		Cache::tag(self::getUserTag())
		     ->set($token, $tokenData, $expire);
		
		return true;
	}
	
	/**
	 * 删除指定token
	 *
	 * @param string $token token字符串
	 * @return bool
	 */
	public static function delete(string $token): bool
	{
		return Cache::delete($token);
	}
	
	/**
	 * 清空所有用户token
	 *
	 * @return bool
	 */
	public static function clear(): bool
	{
		return Cache::tag(self::getUserTag())
		            ->clear();
	}
	
	/**
	 * 获取用户缓存标签
	 *
	 * @return string
	 */
	protected static function getUserTag(): string
	{
		return CacheConstant::AUTH_TAG_PREFIX;
	}
	
	public static function getTokenInfo($token)
	{
		if (!$tokenData = Cache::get($token)) {
			return false;
		}

		return $tokenData;
	}

	/**
	 * 修改Token中的租户信息（仅超级管理员）
	 * 注意：只有超级管理员可以使用此功能，无需并发控制
	 * @param string $token Token字符串
	 * @param int $tenantId 目标租户ID（0表示系统模式）
	 * @param string $mode 切换模式：'system' | 'tenant'
	 * @return bool
	 * @throws \Exception
	 */
	public static function switchTenant(string $token, int $tenantId, string $mode = 'tenant'): bool
	{
		// 获取当前Token数据
		$tokenData = self::getTokenInfo($token);
		if (!$tokenData) {
			throw new \Exception('Token不存在或已过期');
		}

		// 权限验证：只有超级管理员可以切换
		$adminData = $tokenData['data'] ?? [];
		if (($adminData['id'] ?? 0) !== \app\common\core\constants\SystemConstant::SUPER_ADMIN_ID) {
			throw new \Exception('只有系统超级管理员才能切换租户');
		}

		// 保存原始租户ID（如果是首次切换）
		if (!isset($tokenData['tenant_switch'])) {
			$originalTenantId = $adminData['tenant_id'] ?? 0;
		} else {
			$originalTenantId = $tokenData['tenant_switch']['original_tenant_id'];
		}

		// 更新切换信息
		// 注意：由于只有超级管理员可以切换，无需考虑并发问题
		$tokenData['tenant_switch'] = [
			'is_switched' => $mode !== 'original',
			'original_tenant_id' => $originalTenantId,
			'current_tenant_id' => $tenantId,
			'switch_mode' => $mode,
			'switch_time' => time()
		];

		// 计算剩余过期时间
		$createTime = $tokenData['create_time'] ?? time();
		$defaultExpire = 7200; // 2小时
		$remainingTime = $defaultExpire - (time() - $createTime);

		// 优化：如果Token即将过期（少于5分钟），自动延长Token有效期
		if ($remainingTime <= 300) { // 5分钟
			// 延长Token有效期到2小时
			$tokenData['create_time'] = time();
			$remainingTime = $defaultExpire;

			// 记录Token延期日志
			\think\facade\Log::info('Token自动延期', [
				'admin_id' => $tokenData['data']['id'] ?? 0,
				'original_remaining_time' => $remainingTime + (time() - $createTime),
				'new_expire_time' => $defaultExpire,
				'reason' => '租户切换操作触发自动延期',
				'timestamp' => date('Y-m-d H:i:s')
			]);
		}

		// 更新缓存 - 确保过期时间为正整数
		$expireTime = max((int)$remainingTime, 300); // 至少5分钟

		// 调试信息
		if (!is_int($expireTime)) {
			\think\facade\Log::error('Token缓存过期时间类型错误', [
				'expire_time' => $expireTime,
				'expire_time_type' => gettype($expireTime),
				'remaining_time' => $remainingTime,
				'remaining_time_type' => gettype($remainingTime)
			]);
			$expireTime = 7200; // 默认2小时
		}

		$result = Cache::set($token, $tokenData, $expireTime);

		// 记录操作日志
		\think\facade\Log::info('Token租户切换', [
			'admin_id' => $adminData['id'],
			'original_tenant_id' => $originalTenantId,
			'target_tenant_id' => $tenantId,
			'switch_mode' => $mode,
			'token_hash' => md5($token),
			'timestamp' => date('Y-m-d H:i:s')
		]);

		return $result;
	}

	/**
	 * 恢复到原始租户
	 * @param string $token Token字符串
	 * @return bool
	 * @throws \Exception
	 */
	public static function restoreOriginalTenant(string $token): bool
	{
		$tokenData = self::getTokenInfo($token);
		if (!$tokenData || !isset($tokenData['tenant_switch'])) {
			throw new \Exception('没有找到切换信息');
		}

		// 权限验证
		$adminData = $tokenData['data'] ?? [];
		if (($adminData['id'] ?? 0) !== \app\common\core\constants\SystemConstant::SUPER_ADMIN_ID) {
			throw new \Exception('只有系统超级管理员才能恢复租户');
		}

		// 清除切换信息
		unset($tokenData['tenant_switch']);

		// 计算剩余过期时间并更新缓存
		$createTime = $tokenData['create_time'] ?? time();
		$remainingTime = 7200 - (time() - $createTime);

		// 优化：如果Token即将过期，自动延长有效期
		if ($remainingTime <= 300) { // 5分钟
			$tokenData['create_time'] = time();
			$remainingTime = 7200;

			\think\facade\Log::info('Token自动延期(恢复操作)', [
				'admin_id' => $tokenData['data']['id'] ?? 0,
				'reason' => '恢复租户操作触发自动延期',
				'timestamp' => date('Y-m-d H:i:s')
			]);
		}

		// 确保过期时间为正整数
		$expireTime = max((int)$remainingTime, 300); // 至少5分钟

		// 调试信息
		if (!is_int($expireTime)) {
			\think\facade\Log::error('Token恢复缓存过期时间类型错误', [
				'expire_time' => $expireTime,
				'expire_time_type' => gettype($expireTime),
				'remaining_time' => $remainingTime,
				'remaining_time_type' => gettype($remainingTime)
			]);
			$expireTime = 7200; // 默认2小时
		}

		$result = Cache::set($token, $tokenData, $expireTime);

		// 记录操作日志
		\think\facade\Log::info('Token租户恢复', [
			'admin_id' => $adminData['id'],
			'restored_to_tenant_id' => $adminData['tenant_id'] ?? 0,
			'token_hash' => md5($token),
			'timestamp' => date('Y-m-d H:i:s')
		]);

		return $result;
	}

	/**
	 * 获取当前有效的租户ID
	 * @param string $token Token字符串
	 * @return int
	 */
	public static function getEffectiveTenantId(string $token): int
	{
		$tokenData = self::getTokenInfo($token);
		if (!$tokenData) {
			return 0;
		}

		// 如果有切换信息，使用切换后的租户ID
		if (isset($tokenData['tenant_switch']) && $tokenData['tenant_switch']['is_switched']) {
			return $tokenData['tenant_switch']['current_tenant_id'];
		}

		// 否则使用原始租户ID
		return $tokenData['data']['tenant_id'] ?? 0;
	}

	/**
	 * 获取Token切换状态信息
	 * @param string $token Token字符串
	 * @return array
	 */
	public static function getSwitchStatus(string $token): array
	{
		$tokenData = self::getTokenInfo($token);

		if (!$tokenData) {
			return ['is_switched' => false];
		}

		$switchInfo = $tokenData['tenant_switch'] ?? null;

		return [
			'is_switched' => $switchInfo ? $switchInfo['is_switched'] : false,
			'original_tenant_id' => $switchInfo ? $switchInfo['original_tenant_id'] : ($tokenData['data']['tenant_id'] ?? 0),
			'current_tenant_id' => $switchInfo ? $switchInfo['current_tenant_id'] : ($tokenData['data']['tenant_id'] ?? 0),
			'switch_mode' => $switchInfo ? $switchInfo['switch_mode'] : 'original',
			'switch_time' => $switchInfo ? $switchInfo['switch_time'] : null
		];
	}
}