<?php
/**
 * 租户切换功能测试脚本
 * 用于快速测试租户切换功能是否正常工作
 */

// 测试配置
$baseUrl = 'http://localhost'; // 修改为你的域名
$token = 'YOUR_TOKEN_HERE'; // 修改为实际的超级管理员Token

// 测试接口列表
$testApis = [
    'status' => [
        'method' => 'GET',
        'url' => '/api/tenant-switch-test/status',
        'description' => '获取当前状态'
    ],
    'diagnose' => [
        'method' => 'GET',
        'url' => '/api/tenant-switch-test/diagnose-config',
        'description' => '诊断配置问题'
    ],
    'tenant_data_access' => [
        'method' => 'GET',
        'url' => '/api/tenant-switch-test/tenant-data-access',
        'description' => '测试租户数据访问'
    ],
    'tenant_list_management' => [
        'method' => 'GET',
        'url' => '/api/tenant-switch-test/tenant-list-management',
        'description' => '测试租户管理页面数据访问'
    ],
    'management_integration' => [
        'method' => 'GET',
        'url' => '/api/tenant-switch-test/management-integration',
        'description' => '测试租户管理页面集成'
    ],
    'switch_and_verify' => [
        'method' => 'POST',
        'url' => '/api/tenant-switch-test/switch-and-verify',
        'data' => ['tenant_id' => 2], // 修改为实际存在的租户ID
        'description' => '切换到租户并验证访问权限'
    ]
];

/**
 * 发送HTTP请求
 */
function sendRequest($method, $url, $data = null, $token = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    // 设置请求头
    $headers = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    if ($token) {
        $headers[] = 'Authorization: Bearer ' . $token;
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    // 设置请求方法和数据
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

/**
 * 格式化输出结果
 */
function formatOutput($title, $result) {
    echo "\n" . str_repeat("=", 80) . "\n";
    echo "测试: {$title}\n";
    echo str_repeat("-", 80) . "\n";
    
    if ($result['error']) {
        echo "错误: " . $result['error'] . "\n";
        return;
    }
    
    echo "HTTP状态码: " . $result['http_code'] . "\n";
    
    if ($result['response']) {
        $data = json_decode($result['response'], true);
        if ($data) {
            echo "响应数据:\n";
            echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        } else {
            echo "原始响应:\n" . $result['response'] . "\n";
        }
    }
}

// 主测试流程
echo "租户切换功能测试开始\n";
echo "基础URL: {$baseUrl}\n";
echo "Token: " . (empty($token) || $token === 'YOUR_TOKEN_HERE' ? '未设置' : '已设置') . "\n";

if (empty($token) || $token === 'YOUR_TOKEN_HERE') {
    echo "\n警告: 请先设置有效的超级管理员Token!\n";
    echo "修改脚本中的 \$token 变量为实际的Token值。\n";
    exit(1);
}

// 执行测试
foreach ($testApis as $key => $api) {
    $url = $baseUrl . $api['url'];
    $method = $api['method'];
    $data = $api['data'] ?? null;
    $description = $api['description'];
    
    $result = sendRequest($method, $url, $data, $token);
    formatOutput($description, $result);
    
    // 短暂延迟，避免请求过快
    sleep(1);
}

echo "\n" . str_repeat("=", 80) . "\n";
echo "测试完成\n";
echo "\n关键检查点:\n";
echo "1. 检查 should_apply_tenant_isolation 是否始终为 true\n";
echo "2. 检查切换后 effective_tenant_id 是否正确变化\n";
echo "3. 检查数据访问权限是否按预期工作\n";
echo "4. 查看诊断结果中的修复建议\n";
echo "\n如果发现问题，请参考 '租户切换功能测试指南.md' 文件中的修复建议。\n";
