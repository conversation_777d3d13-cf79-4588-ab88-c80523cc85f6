-- =====================================================
-- 系统集成测试 - 综合测试数据初始化脚本
-- =====================================================

-- 清理现有测试数据
SET FOREIGN_KEY_CHECKS = 0;

-- 清理业务数据
DELETE FROM project_task_record WHERE tenant_id IN (1, 2, 3);
DELETE FROM project_task WHERE tenant_id IN (1, 2, 3);
DELETE FROM project_member WHERE tenant_id IN (1, 2, 3);
DELETE FROM project_project WHERE tenant_id IN (1, 2, 3);
DELETE FROM crm_contract WHERE tenant_id IN (1, 2, 3);
DELETE FROM crm_business WHERE tenant_id IN (1, 2, 3);
DELETE FROM crm_leads WHERE tenant_id IN (1, 2, 3);
DELETE FROM crm_customer WHERE tenant_id IN (1, 2, 3);

-- 清理系统数据
DELETE FROM system_admin_role WHERE tenant_id IN (1, 2, 3);
DELETE FROM system_role_menu WHERE tenant_id IN (1, 2, 3);
DELETE FROM system_admin WHERE tenant_id IN (1, 2, 3);
DELETE FROM system_role WHERE tenant_id IN (1, 2, 3);
DELETE FROM system_dept WHERE tenant_id IN (1, 2, 3);
DELETE FROM system_tenant WHERE id IN (1, 2, 3);

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 1. 租户数据
-- =====================================================
INSERT INTO system_tenant (id, name, code, domain, logo, package_id, expired_at, status, created_at, updated_at) VALUES
(1, '测试租户A - 科技公司', 'test_tenant_a', 'test-a.local', '', 1, '2025-12-31 23:59:59', 1, NOW(), NOW()),
(2, '测试租户B - 贸易公司', 'test_tenant_b', 'test-b.local', '', 1, '2025-12-31 23:59:59', 1, NOW(), NOW()),
(3, '测试租户C - 服务公司', 'test_tenant_c', 'test-c.local', '', 1, '2025-12-31 23:59:59', 1, NOW(), NOW());

-- =====================================================
-- 2. 部门数据（租户1）
-- =====================================================
INSERT INTO system_dept (id, parent_id, name, code, leader_name, phone, email, sort, status, tenant_id, created_at, updated_at) VALUES
-- 租户1部门结构
(1, 0, '总公司', 'HQ', '张总', '13800000001', '<EMAIL>', 1, 1, 1, NOW(), NOW()),
(2, 1, '技术部', 'TECH', '李技术总监', '13800000002', '<EMAIL>', 1, 1, 1, NOW(), NOW()),
(3, 1, '销售部', 'SALES', '王销售总监', '13800000003', '<EMAIL>', 2, 1, 1, NOW(), NOW()),
(4, 1, '人事部', 'HR', '赵人事经理', '13800000004', '<EMAIL>', 3, 1, 1, NOW(), NOW()),
(5, 2, '前端开发组', 'FE', '前端组长', '13800000005', '<EMAIL>', 1, 1, 1, NOW(), NOW()),
(6, 2, '后端开发组', 'BE', '后端组长', '13800000006', '<EMAIL>', 2, 1, 1, NOW(), NOW()),
(7, 3, '华北销售区', 'SALES_NORTH', '华北区经理', '13800000007', '<EMAIL>', 1, 1, 1, NOW(), NOW()),
(8, 3, '华南销售区', 'SALES_SOUTH', '华南区经理', '13800000008', '<EMAIL>', 2, 1, 1, NOW(), NOW()),

-- 租户2部门结构（简化）
(11, 0, '贸易总公司', 'TRADE_HQ', '刘总', '13800000011', '<EMAIL>', 1, 1, 2, NOW(), NOW()),
(12, 11, '采购部', 'PURCHASE', '采购经理', '13800000012', '<EMAIL>', 1, 1, 2, NOW(), NOW()),
(13, 11, '销售部', 'TRADE_SALES', '销售经理', '13800000013', '<EMAIL>', 2, 1, 2, NOW(), NOW()),

-- 租户3部门结构（简化）
(21, 0, '服务总公司', 'SERVICE_HQ', '陈总', '13800000021', '<EMAIL>', 1, 1, 3, NOW(), NOW()),
(22, 21, '客服部', 'SERVICE', '客服经理', '13800000022', '<EMAIL>', 1, 1, 3, NOW(), NOW());

-- =====================================================
-- 3. 角色数据
-- =====================================================
INSERT INTO system_role (id, name, code, data_scope, status, remark, tenant_id, created_at, updated_at) VALUES
-- 租户1角色
(1, '超级管理员', 'super_admin', 1, 1, '拥有所有权限', 1, NOW(), NOW()),
(2, '技术部经理', 'tech_manager', 2, 1, '技术部门管理权限', 1, NOW(), NOW()),
(3, '销售部经理', 'sales_manager', 3, 1, '销售部门及下级权限', 1, NOW(), NOW()),
(4, '普通员工', 'employee', 4, 1, '仅本人数据权限', 1, NOW(), NOW()),
(5, '前端开发', 'frontend_dev', 4, 1, '前端开发人员', 1, NOW(), NOW()),
(6, '后端开发', 'backend_dev', 4, 1, '后端开发人员', 1, NOW(), NOW()),
(7, '销售代表', 'sales_rep', 4, 1, '销售代表', 1, NOW(), NOW()),

-- 租户2角色
(11, '贸易管理员', 'trade_admin', 1, 1, '贸易公司管理员', 2, NOW(), NOW()),
(12, '采购员', 'purchaser', 4, 1, '采购人员', 2, NOW(), NOW()),
(13, '销售员', 'trader', 4, 1, '贸易销售人员', 2, NOW(), NOW()),

-- 租户3角色
(21, '服务管理员', 'service_admin', 1, 1, '服务公司管理员', 3, NOW(), NOW()),
(22, '客服代表', 'service_rep', 4, 1, '客服代表', 3, NOW(), NOW());

-- =====================================================
-- 4. 用户数据
-- =====================================================
INSERT INTO system_admin (id, username, password, salt, real_name, nickname, avatar, phone, email, dept_id, post_ids, status, login_ip, login_date, remark, tenant_id, created_at, updated_at) VALUES
-- 租户1用户
(1, 'admin', '96e79218965eb72c92a549dd5a330112', 'salt123', '系统管理员', '管理员', '', '13800000001', '<EMAIL>', 1, '', 1, '', NULL, '超级管理员账号', 1, NOW(), NOW()),
(2, 'tech_manager', '96e79218965eb72c92a549dd5a330112', 'salt123', '李技术总监', '技术总监', '', '13800000002', '<EMAIL>', 2, '', 1, '', NULL, '技术部经理', 1, NOW(), NOW()),
(3, 'sales_manager', '96e79218965eb72c92a549dd5a330112', 'salt123', '王销售总监', '销售总监', '', '13800000003', '<EMAIL>', 3, '', 1, '', NULL, '销售部经理', 1, NOW(), NOW()),
(4, 'fe_dev1', '96e79218965eb72c92a549dd5a330112', 'salt123', '前端开发1', '小前', '', '13800000101', '<EMAIL>', 5, '', 1, '', NULL, '前端开发人员', 1, NOW(), NOW()),
(5, 'fe_dev2', '96e79218965eb72c92a549dd5a330112', 'salt123', '前端开发2', '小前2', '', '13800000102', '<EMAIL>', 5, '', 1, '', NULL, '前端开发人员', 1, NOW(), NOW()),
(6, 'be_dev1', '96e79218965eb72c92a549dd5a330112', 'salt123', '后端开发1', '小后', '', '13800000201', '<EMAIL>', 6, '', 1, '', NULL, '后端开发人员', 1, NOW(), NOW()),
(7, 'be_dev2', '96e79218965eb72c92a549dd5a330112', 'salt123', '后端开发2', '小后2', '', '13800000202', '<EMAIL>', 6, '', 1, '', NULL, '后端开发人员', 1, NOW(), NOW()),
(8, 'sales_rep1', '96e79218965eb72c92a549dd5a330112', 'salt123', '销售代表1', '小销1', '', '13800000301', '<EMAIL>', 7, '', 1, '', NULL, '华北区销售', 1, NOW(), NOW()),
(9, 'sales_rep2', '96e79218965eb72c92a549dd5a330112', 'salt123', '销售代表2', '小销2', '', '13800000302', '<EMAIL>', 8, '', 1, '', NULL, '华南区销售', 1, NOW(), NOW()),

-- 租户2用户
(11, 'trade_admin', '96e79218965eb72c92a549dd5a330112', 'salt123', '贸易管理员', '贸易管理', '', '13800000011', '<EMAIL>', 11, '', 1, '', NULL, '贸易公司管理员', 2, NOW(), NOW()),
(12, 'purchaser1', '96e79218965eb72c92a549dd5a330112', 'salt123', '采购员1', '采购1', '', '13800000012', '<EMAIL>', 12, '', 1, '', NULL, '采购人员', 2, NOW(), NOW()),
(13, 'trader1', '96e79218965eb72c92a549dd5a330112', 'salt123', '销售员1', '贸易1', '', '13800000013', '<EMAIL>', 13, '', 1, '', NULL, '贸易销售', 2, NOW(), NOW()),

-- 租户3用户
(21, 'service_admin', '96e79218965eb72c92a549dd5a330112', 'salt123', '服务管理员', '服务管理', '', '13800000021', '<EMAIL>', 21, '', 1, '', NULL, '服务公司管理员', 3, NOW(), NOW()),
(22, 'service_rep1', '96e79218965eb72c92a549dd5a330112', 'salt123', '客服代表1', '客服1', '', '13800000022', '<EMAIL>', 22, '', 1, '', NULL, '客服代表', 3, NOW(), NOW());

-- =====================================================
-- 5. 用户角色关联
-- =====================================================
INSERT INTO system_admin_role (admin_id, role_id, tenant_id) VALUES
-- 租户1用户角色
(1, 1, 1), -- 管理员 -> 超级管理员
(2, 2, 1), -- 技术总监 -> 技术部经理
(3, 3, 1), -- 销售总监 -> 销售部经理
(4, 5, 1), -- 前端开发1 -> 前端开发
(5, 5, 1), -- 前端开发2 -> 前端开发
(6, 6, 1), -- 后端开发1 -> 后端开发
(7, 6, 1), -- 后端开发2 -> 后端开发
(8, 7, 1), -- 销售代表1 -> 销售代表
(9, 7, 1), -- 销售代表2 -> 销售代表

-- 租户2用户角色
(11, 11, 2), -- 贸易管理员 -> 贸易管理员
(12, 12, 2), -- 采购员1 -> 采购员
(13, 13, 2), -- 销售员1 -> 销售员

-- 租户3用户角色
(21, 21, 3), -- 服务管理员 -> 服务管理员
(22, 22, 3); -- 客服代表1 -> 客服代表

-- =====================================================
-- 6. CRM客户数据
-- =====================================================
INSERT INTO crm_customer (id, name, phone, email, address, industry, source, level, status, owner_id, dept_id, follow_time, deal_time, remark, tenant_id, created_at, updated_at) VALUES
-- 租户1客户数据
(1, '北京科技有限公司', '010-12345678', '<EMAIL>', '北京市朝阳区', '软件开发', '网络推广', 'A', 1, 8, 7, '2024-12-01 10:00:00', NULL, '重要客户', 1, NOW(), NOW()),
(2, '上海创新企业', '021-87654321', '<EMAIL>', '上海市浦东新区', '人工智能', '朋友介绍', 'B', 1, 9, 8, '2024-12-02 14:30:00', NULL, '潜在大客户', 1, NOW(), NOW()),
(3, '深圳智能科技', '0755-11111111', '<EMAIL>', '深圳市南山区', '物联网', '展会', 'A', 2, 8, 7, '2024-11-15 09:00:00', '2024-12-01 16:00:00', '已成交客户', 1, NOW(), NOW()),
(4, '广州数据公司', '020-22222222', '<EMAIL>', '广州市天河区', '大数据', '电话营销', 'C', 1, 9, 8, '2024-12-03 11:00:00', NULL, '小客户', 1, NOW(), NOW()),

-- 租户2客户数据
(11, '进出口贸易A', '010-33333333', '<EMAIL>', '北京市东城区', '进出口贸易', '老客户介绍', 'A', 13, 13, '2024-12-01 08:00:00', NULL, '长期合作伙伴', 2, NOW(), NOW()),
(12, '国际物流B', '021-44444444', '<EMAIL>', '上海市黄浦区', '物流运输', '网络搜索', 'B', 13, 13, '2024-12-02 15:00:00', NULL, '新客户', 2, NOW(), NOW()),

-- 租户3客户数据
(21, '咨询服务X', '010-55555555', '<EMAIL>', '北京市西城区', '管理咨询', '推荐', 'A', 22, 22, '2024-12-01 13:00:00', NULL, '咨询类客户', 3, NOW(), NOW());

-- =====================================================
-- 7. 项目数据
-- =====================================================
INSERT INTO project_project (id, name, code, description, template_id, status, priority, start_date, end_date, budget, owner_id, dept_id, progress, tenant_id, created_at, updated_at) VALUES
-- 租户1项目
(1, '电商平台开发项目', 'ECOM2024001', '开发一个完整的电商平台系统', 1, 2, 3, '2024-11-01', '2025-03-31', 500000.00, 2, 2, 35, 1, NOW(), NOW()),
(2, '移动APP开发', 'APP2024001', '开发企业移动应用', 1, 2, 2, '2024-12-01', '2025-02-28', 200000.00, 2, 2, 20, 1, NOW(), NOW()),
(3, '客户管理系统升级', 'CRM2024001', '升级现有CRM系统功能', 1, 1, 2, '2025-01-01', '2025-04-30', 150000.00, 3, 3, 0, 1, NOW(), NOW()),

-- 租户2项目
(11, '供应链管理系统', 'SCM2024001', '建设供应链管理平台', 1, 2, 3, '2024-10-01', '2025-01-31', 300000.00, 11, 11, 60, 2, NOW(), NOW()),

-- 租户3项目
(21, '客服系统优化', 'CS2024001', '优化客服工作流程', 1, 2, 2, '2024-11-15', '2025-01-15', 80000.00, 21, 21, 45, 3, NOW(), NOW());

-- =====================================================
-- 8. 项目成员
-- =====================================================
INSERT INTO project_member (id, project_id, admin_id, role, join_date, status, tenant_id, created_at, updated_at) VALUES
-- 电商平台项目成员
(1, 1, 2, 'manager', '2024-11-01', 1, 1, NOW(), NOW()),
(2, 1, 4, 'developer', '2024-11-01', 1, 1, NOW(), NOW()),
(3, 1, 5, 'developer', '2024-11-01', 1, 1, NOW(), NOW()),
(4, 1, 6, 'developer', '2024-11-01', 1, 1, NOW(), NOW()),
(5, 1, 7, 'developer', '2024-11-01', 1, 1, NOW(), NOW()),

-- 移动APP项目成员
(6, 2, 2, 'manager', '2024-12-01', 1, 1, NOW(), NOW()),
(7, 2, 4, 'developer', '2024-12-01', 1, 1, NOW(), NOW()),
(8, 2, 6, 'developer', '2024-12-01', 1, 1, NOW(), NOW()),

-- 其他项目成员
(11, 11, 11, 'manager', '2024-10-01', 1, 2, NOW(), NOW()),
(21, 21, 21, 'manager', '2024-11-15', 1, 3, NOW(), NOW()),
(22, 21, 22, 'member', '2024-11-15', 1, 3, NOW(), NOW());

-- =====================================================
-- 9. 项目任务数据
-- =====================================================
INSERT INTO project_task (id, project_id, parent_id, title, description, type, status, priority, assignee_id, start_date, end_date, estimated_hours, actual_hours, progress, tags, tenant_id, created_at, updated_at) VALUES
-- 电商平台项目任务
(1, 1, 0, '需求分析', '分析电商平台功能需求', 'requirement', 4, 3, 2, '2024-11-01', '2024-11-15', 80, 85, 100, '需求,分析', 1, NOW(), NOW()),
(2, 1, 0, '系统设计', '设计系统架构和数据库', 'design', 4, 3, 2, '2024-11-16', '2024-11-30', 120, 110, 100, '设计,架构', 1, NOW(), NOW()),
(3, 1, 0, '前端开发', '开发用户界面', 'development', 2, 2, 4, '2024-12-01', '2025-01-31', 300, 120, 40, '前端,开发', 1, NOW(), NOW()),
(4, 1, 0, '后端开发', '开发后端API', 'development', 2, 2, 6, '2024-12-01', '2025-01-31', 400, 150, 35, '后端,API', 1, NOW(), NOW()),
(5, 1, 0, '测试', '系统测试', 'testing', 1, 2, 7, '2025-02-01', '2025-02-28', 100, 0, 0, '测试', 1, NOW(), NOW()),

-- 移动APP项目任务
(11, 2, 0, 'UI设计', '设计移动端界面', 'design', 3, 2, 4, '2024-12-01', '2024-12-20', 60, 45, 75, 'UI,设计', 1, NOW(), NOW()),
(12, 2, 0, '功能开发', '开发核心功能', 'development', 2, 3, 6, '2024-12-15', '2025-01-31', 200, 40, 20, '开发,功能', 1, NOW(), NOW());

-- =====================================================
-- 10. 任务记录数据
-- =====================================================
INSERT INTO project_task_record (id, task_id, admin_id, type, content, hours, record_date, tenant_id, created_at, updated_at) VALUES
-- 任务跟进记录
(1, 1, 2, 'follow', '需求分析已完成，整理了详细的功能清单', 8, '2024-11-15', 1, NOW(), NOW()),
(2, 2, 2, 'follow', '系统架构设计完成，数据库设计需要优化', 10, '2024-11-30', 1, NOW(), NOW()),
(3, 3, 4, 'follow', '首页和商品列表页面开发完成', 8, '2024-12-15', 1, NOW(), NOW()),
(4, 4, 6, 'follow', '用户认证和商品管理API开发完成', 8, '2024-12-15', 1, NOW(), NOW()),
(5, 11, 4, 'comment', 'UI设计稿已提交，等待确认', 0, '2024-12-10', 1, NOW(), NOW());

-- 提交测试数据统计
SELECT 
    '租户数据' as '数据类型',
    COUNT(*) as '记录数'
FROM system_tenant 
WHERE id IN (1,2,3)

UNION ALL

SELECT 
    '部门数据' as '数据类型',
    COUNT(*) as '记录数'
FROM system_dept 
WHERE tenant_id IN (1,2,3)

UNION ALL

SELECT 
    '用户数据' as '数据类型',
    COUNT(*) as '记录数'
FROM system_admin 
WHERE tenant_id IN (1,2,3)

UNION ALL

SELECT 
    '客户数据' as '数据类型',
    COUNT(*) as '记录数'
FROM crm_customer 
WHERE tenant_id IN (1,2,3)

UNION ALL

SELECT 
    '项目数据' as '数据类型',
    COUNT(*) as '记录数'
FROM project_project 
WHERE tenant_id IN (1,2,3)

UNION ALL

SELECT 
    '任务数据' as '数据类型',
    COUNT(*) as '记录数'
FROM project_task 
WHERE tenant_id IN (1,2,3);

-- 测试数据初始化完成
SELECT '测试数据初始化完成！' as '状态', NOW() as '完成时间';
