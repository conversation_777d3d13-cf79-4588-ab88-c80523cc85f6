<template>
  <div class="page-content">
    <el-tabs v-model="activeTab" type="card" class="settings-tabs" v-loading="loading">
      <el-tab-pane label="公海规则" name="sea_rule">
        <el-form label-width="120px">
          <el-form-item label="是否开启：">
            <el-switch
              v-model="seaRule.sea_status"
              :disabled="!isEditSeaRule"
              inline-prompt
              active-text="是"
              inactive-text="否"
              :active-value="1"
              :inactive-value="0"
            />
          </el-form-item>
          <el-form-item label="未跟进天数：">
            <el-input v-model="seaRule.follow_days" :disabled="!isEditSeaRule">
              <template #append>天</template>
            </el-input>
            <div class="form-tip">用于设置未成交状态的客户，多少天未跟进自动退回公海</div>
          </el-form-item>
          <el-form-item label="未成交天数：">
            <el-input v-model="seaRule.deal_days" :disabled="!isEditSeaRule">
              <template #append>天</template>
            </el-input>
            <div class="form-tip">用于设置客户在未成交状态滞留多少天自动退回公海</div>
          </el-form-item>
          <el-form-item label="退回公海提醒：">
            <el-input v-model="seaRule.notify_days" :disabled="!isEditSeaRule">
              <template #append>天</template>
            </el-input>
            <div class="form-tip">用于客户退回公海提前多少天进行提醒</div>
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="isEditSeaRule"
              style="width: 90px"
              v-ripple
              @click="isEditSeaRule = false"
            >
              取消
            </el-button>
            <el-button type="primary" style="width: 90px" v-ripple @click="editSeaRuleClick">
              {{ isEditSeaRule ? '保存' : '编辑' }}
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="提前通知" name="advance_notice">
        <el-form label-width="120px">
          <el-form-item label="启用提前通知：">
            <el-switch
              v-model="advanceNotice.notice_enabled"
              :disabled="!isEditAdvanceNotice"
              inline-prompt
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
          <el-form-item label="提前通知天数：">
            <el-input v-model="advanceNotice.notify_days" :disabled="!isEditAdvanceNotice">
              <template #append>天</template>
            </el-input>
            <div class="form-tip">客户即将被回收到公海前多少天发送提前通知</div>
          </el-form-item>
          <el-form-item label="通知频率：">
            <el-select v-model="advanceNotice.notice_frequency" :disabled="!isEditAdvanceNotice">
              <el-option label="仅通知一次" value="once" />
              <el-option label="每天通知" value="daily" />
              <el-option label="每2天通知" value="every_2_days" />
            </el-select>
            <div class="form-tip">设置通知的发送频率</div>
          </el-form-item>
          <el-form-item label="通知对象：">
            <el-select v-model="advanceNotice.notice_target" :disabled="!isEditAdvanceNotice">
              <el-option label="客户负责人" value="owner" />
              <el-option label="部门经理" value="manager" />
              <el-option label="两者都通知" value="both" />
            </el-select>
          </el-form-item>
          <el-form-item label="通知模板：">
            <el-input
              v-model="advanceNotice.notice_template"
              :disabled="!isEditAdvanceNotice"
              type="textarea"
              :rows="3"
              placeholder="支持变量：{{customer_name}} {{days}} {{owner_name}} {{last_follow_date}}"
            />
            <div class="form-tip">自定义通知消息模板，支持变量替换</div>
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="isEditAdvanceNotice"
              style="width: 90px"
              v-ripple
              @click="isEditAdvanceNotice = false"
            >
              取消
            </el-button>
            <el-button type="primary" style="width: 90px" v-ripple @click="editAdvanceNoticeClick">
              {{ isEditAdvanceNotice ? '保存' : '编辑' }}
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="任务提醒" name="task_reminder">
        <el-form label-width="120px">
          <el-form-item label="启用任务提醒：">
            <el-switch
              v-model="taskReminder.reminder_enabled"
              :disabled="!isEditTaskReminder"
              inline-prompt
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>
          <el-form-item label="逾期任务提醒：">
            <el-switch
              v-model="taskReminder.overdue_enabled"
              :disabled="!isEditTaskReminder"
              inline-prompt
              active-text="是"
              inactive-text="否"
            />
            <el-select v-model="taskReminder.overdue_frequency" :disabled="!isEditTaskReminder" style="margin-left: 10px; width: 120px;">
              <el-option label="每天" value="daily" />
              <el-option label="每周" value="weekly" />
            </el-select>
            <div class="form-tip">对已逾期的任务发送提醒通知</div>
          </el-form-item>
          <el-form-item label="即将到期提醒：">
            <el-switch
              v-model="taskReminder.due_soon_enabled"
              :disabled="!isEditTaskReminder"
              inline-prompt
              active-text="是"
              inactive-text="否"
            />
            <div class="form-tip">提前提醒即将到期的任务</div>
          </el-form-item>
          <el-form-item label="提醒天数：">
            <el-checkbox-group v-model="taskReminder.due_soon_days" :disabled="!isEditTaskReminder">
              <el-checkbox :label="1">1天前</el-checkbox>
              <el-checkbox :label="3">3天前</el-checkbox>
              <el-checkbox :label="7">7天前</el-checkbox>
            </el-checkbox-group>
            <div class="form-tip">选择在任务到期前多少天发送提醒</div>
          </el-form-item>
          <el-form-item label="跟进计划提醒：">
            <el-switch
              v-model="taskReminder.follow_up_enabled"
              :disabled="!isEditTaskReminder"
              inline-prompt
              active-text="是"
              inactive-text="否"
            />
            <el-input v-model="taskReminder.follow_up_advance_hours" :disabled="!isEditTaskReminder" style="margin-left: 10px; width: 100px;">
              <template #append>小时</template>
            </el-input>
            <div class="form-tip">基于跟进计划的提前提醒时间</div>
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="isEditTaskReminder"
              style="width: 90px"
              v-ripple
              @click="isEditTaskReminder = false"
            >
              取消
            </el-button>
            <el-button type="primary" style="width: 90px" v-ripple @click="editTaskReminderClick">
              {{ isEditTaskReminder ? '保存' : '编辑' }}
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="企业设置" name="enterprise">
        <el-form label-width="120px">
          <el-form-item label="企业名称：">
            <el-input v-model="enterprise.enterprise_name" :disabled="!isEnterprise"></el-input>
            <div class="form-tip">每日报价时使用</div>
          </el-form-item>
          <el-form-item label="工作时间：">
            <el-input v-model="enterprise.work_time" :disabled="!isEnterprise"></el-input>
            <div class="form-tip">格式：08:00-12:00,14:00-18:00；用于审批相关时间计算</div>
          </el-form-item>
          <el-form-item>
            <el-button
              v-if="isEnterprise"
              style="width: 90px"
              v-ripple
              @click="isEnterprise = false"
            >
              取消
            </el-button>
            <el-button type="primary" style="width: 90px" v-ripple @click="editEnterpriseClick">
              {{ isEnterprise ? '保存' : '编辑' }}
            </el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
  import { ElMessage } from 'element-plus'
  import { onMounted, ref } from 'vue'
  import { TenantConfigApi } from '@/api/TenantConfigApi'
  import { ApiStatus } from '@/utils/http/status'
  import { isEmpty, convertSeaRuleConfigTypes } from '@/utils/utils'

  // 当前选中的标签页
  const activeTab = ref('sea_rule')

  // 加载状态
  const loading = ref(false)

  // 网站配置数据初始化
  const seaRule = ref({
    sea_status: 0,
    follow_days: 15,
    deal_days: 30,
    notify_days: 3
  })

  // 提前通知配置数据初始化
  const advanceNotice = ref({
    notice_enabled: false,
    notify_days: 3,
    notice_frequency: 'once',
    notice_target: 'owner',
    notice_template: '您负责的客户 {{customer_name}} 将在 {{days}} 天后被回收到公海，请及时跟进。'
  })

  // 任务提醒配置数据初始化
  const taskReminder = ref({
    reminder_enabled: false,
    overdue_enabled: true,
    overdue_frequency: 'daily',
    due_soon_enabled: true,
    due_soon_days: [1, 3, 7],
    follow_up_enabled: true,
    follow_up_advance_hours: 2
  })

  // 企业配置数据初始化
  const enterprise = ref({
    enterprise_name: '',
    work_time: ''
  })

  // 页面加载时初始化系统信息
  onMounted(async () => {
    await getConfig()
  })

  // 编辑状态控制
  const isEditSeaRule = ref(false)
  const isEditAdvanceNotice = ref(false)
  const isEditTaskReminder = ref(false)
  const isEnterprise = ref(false)

  // 获取系统配置
  const getConfig = async () => {
    try {
      loading.value = true
      const res = await TenantConfigApi.getTenantConfigDetail()
      if (res.code === ApiStatus.success && res.data) {
        if (!isEmpty(res.data)) {
          // 处理公海规则配置
          if (res.data.sea_rule) {
            // 使用工具函数进行类型转换
            seaRule.value = convertSeaRuleConfigTypes(res.data.sea_rule)
          }
          // 处理提前通知配置
          if (res.data.advance_notice) {
            advanceNotice.value = { ...advanceNotice.value, ...res.data.advance_notice }
          }
          // 处理任务提醒配置
          if (res.data.task_reminder) {
            taskReminder.value = { ...taskReminder.value, ...res.data.task_reminder }
          }
          // 处理企业配置
          if (res.data.enterprise) {
            enterprise.value = res.data.enterprise
          }
        }
      }
    } catch (error) {
      console.error('获取系统配置失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 编辑网站配置
  const editSeaRuleClick = async () => {
    if (isEditSeaRule.value) {
      // 保存网站配置逻辑
      try {
        loading.value = true
        const res = await TenantConfigApi.saveTenantConfig({
          group: 'sea_rule',
          config: seaRule.value
        })
        if (res.code === ApiStatus.success) {
          ElMessage.success('配置保存成功')
        }
      } catch (error) {
        console.error('保存网站配置失败:', error)
        return
      } finally {
        loading.value = false
      }
    }
    isEditSeaRule.value = !isEditSeaRule.value
  }

  // 编辑提前通知配置
  const editAdvanceNoticeClick = async () => {
    if (isEditAdvanceNotice.value) {
      // 保存提前通知配置逻辑
      try {
        loading.value = true
        const res = await TenantConfigApi.saveTenantConfig({
          group: 'advance_notice',
          config: advanceNotice.value
        })
        if (res.code === ApiStatus.success) {
          ElMessage.success('提前通知配置保存成功')
        }
      } catch (error) {
        console.error('保存提前通知配置失败:', error)
        return
      } finally {
        loading.value = false
      }
    }
    isEditAdvanceNotice.value = !isEditAdvanceNotice.value
  }

  // 编辑任务提醒配置
  const editTaskReminderClick = async () => {
    if (isEditTaskReminder.value) {
      // 保存任务提醒配置逻辑
      try {
        loading.value = true
        const res = await TenantConfigApi.saveTenantConfig({
          group: 'task_reminder',
          config: taskReminder.value
        })
        if (res.code === ApiStatus.success) {
          ElMessage.success('任务提醒配置保存成功')
        }
      } catch (error) {
        console.error('保存任务提醒配置失败:', error)
        return
      } finally {
        loading.value = false
      }
    }
    isEditTaskReminder.value = !isEditTaskReminder.value
  }

  // 编辑企业配置
  const editEnterpriseClick = async () => {
    if (isEnterprise.value) {
      // 保存企业配置逻辑
      try {
        loading.value = true
        const res = await TenantConfigApi.saveTenantConfig({
          group: 'enterprise',
          config: enterprise.value
        })
        if (res.code === ApiStatus.success) {
          ElMessage.success('企业配置保存成功')
        }
      } catch (error) {
        console.error('保存企业配置失败:', error)
        return
      } finally {
        loading.value = false
      }
    }
    isEnterprise.value = !isEnterprise.value
  }
</script>

<style lang="scss" scoped>
  .page-content {
    width: 100%;
    height: 100%;

    .header {
      padding-bottom: 15px;
      border-bottom: 1px solid var(--art-border-color);

      h3 {
        font-size: 18px;
        font-weight: 500;
      }
    }

    .settings-tabs {
      margin-top: 20px;

      :deep(.el-tabs__header) {
        margin-bottom: 20px;
      }

      :deep(.el-tabs__nav) {
        border-radius: 4px;
      }

      :deep(.el-tabs__item) {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
      }
    }

    .oss-config-title {
      font-size: 16px;
      font-weight: 500;
      margin: 10px 0 20px;
      padding-bottom: 10px;
      border-bottom: 1px dashed var(--art-border-color);
      color: var(--art-text-color);
    }

    :deep(.el-form) {
      max-width: 800px;
      margin: 0 auto;
    }
  }
</style>
