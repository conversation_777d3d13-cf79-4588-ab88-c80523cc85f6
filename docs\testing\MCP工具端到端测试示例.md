# MCP工具端到端测试示例

## 📋 概述

本文档展示如何使用现有的MCP工具（特别是Playwright相关工具）进行系统的端到端测试。通过具体的测试用例演示测试流程和最佳实践。

## 🎯 测试场景设计

### 核心测试场景
1. **用户登录流程测试**
2. **权限控制验证测试**
3. **多租户数据隔离测试**
4. **CRM客户管理流程测试**
5. **项目任务管理流程测试**

## 🔧 MCP工具使用指南

### 可用的Playwright MCP工具
```
browser_navigate_Playwright      # 页面导航
browser_click_Playwright         # 点击操作
browser_type_Playwright          # 输入操作
browser_snapshot_Playwright      # 页面快照
browser_take_screenshot_Playwright # 截图
browser_wait_for_Playwright      # 等待元素或时间
browser_evaluate_Playwright      # 执行JavaScript
browser_select_option_Playwright # 下拉选择
browser_hover_Playwright         # 鼠标悬停
browser_drag_Playwright          # 拖拽操作
```

### 数据库操作工具
```
execute_query_python_exe         # 执行SQL查询
fetch_data_python_exe           # 查询数据
insert_data_python_exe          # 插入数据
create_table_python_exe         # 创建表
```

## 📝 测试用例示例

### 测试用例1：用户登录流程测试

#### 测试目标
验证用户登录功能的完整性，包括正常登录、错误处理、权限验证等。

#### 测试步骤（使用MCP工具）

**步骤1：准备测试数据**
```python
# 使用数据库工具准备测试用户
insert_data_python_exe({
    "table": "system_admin",
    "data": {
        "username": "test_user",
        "password": "hashed_password",
        "real_name": "测试用户",
        "status": 1,
        "tenant_id": 1
    }
})
```

**步骤2：导航到登录页面**
```javascript
// 使用browser_navigate_Playwright导航到登录页
browser_navigate_Playwright({
    "url": "http://localhost:3006/login"
})
```

**步骤3：页面快照验证**
```javascript
// 使用browser_snapshot_Playwright获取页面结构
browser_snapshot_Playwright()
// 验证登录表单元素是否存在
```

**步骤4：输入登录信息**
```javascript
// 输入用户名
browser_type_Playwright({
    "element": "用户名输入框",
    "ref": "input[placeholder='用户名']",
    "text": "test_user"
})

// 输入密码
browser_type_Playwright({
    "element": "密码输入框", 
    "ref": "input[placeholder='密码']",
    "text": "123456"
})
```

**步骤5：点击登录按钮**
```javascript
browser_click_Playwright({
    "element": "登录按钮",
    "ref": "button[type='submit']"
})
```

**步骤6：等待页面跳转**
```javascript
browser_wait_for_Playwright({
    "text": "欢迎",
    "time": 5
})
```

**步骤7：验证登录成功**
```javascript
// 截图保存登录后状态
browser_take_screenshot_Playwright({
    "filename": "login_success.png"
})

// 验证URL是否跳转到首页
browser_evaluate_Playwright({
    "function": "() => window.location.pathname"
})
```

### 测试用例2：权限控制验证测试

#### 测试目标
验证不同角色用户的权限控制是否正确，确保用户只能访问被授权的功能。

#### 测试步骤

**步骤1：创建不同权限的测试用户**
```python
# 创建普通用户
insert_data_python_exe({
    "table": "system_admin",
    "data": {
        "id": 100,
        "username": "normal_user",
        "real_name": "普通用户",
        "tenant_id": 1
    }
})

# 分配普通用户角色
insert_data_python_exe({
    "table": "system_admin_role", 
    "data": {
        "admin_id": 100,
        "role_id": 3,  # 普通用户角色
        "tenant_id": 1
    }
})
```

**步骤2：以普通用户身份登录**
```javascript
// 导航到登录页
browser_navigate_Playwright({
    "url": "http://localhost:3006/login"
})

// 输入普通用户凭据
browser_type_Playwright({
    "element": "用户名输入框",
    "ref": "input[placeholder='用户名']", 
    "text": "normal_user"
})

browser_type_Playwright({
    "element": "密码输入框",
    "ref": "input[placeholder='密码']",
    "text": "123456"
})

browser_click_Playwright({
    "element": "登录按钮",
    "ref": "button[type='submit']"
})
```

**步骤3：验证菜单权限**
```javascript
// 等待页面加载
browser_wait_for_Playwright({
    "time": 3
})

// 获取页面快照，检查菜单项
browser_snapshot_Playwright()

// 验证管理员菜单不可见
browser_evaluate_Playwright({
    "function": "() => document.querySelector('.admin-menu') === null"
})
```

**步骤4：尝试访问受限页面**
```javascript
// 直接导航到管理员页面
browser_navigate_Playwright({
    "url": "http://localhost:3006/system/admin"
})

// 验证是否被重定向或显示权限错误
browser_wait_for_Playwright({
    "text": "权限不足",
    "time": 3
})

browser_take_screenshot_Playwright({
    "filename": "permission_denied.png"
})
```

### 测试用例3：多租户数据隔离测试

#### 测试目标
验证不同租户之间的数据完全隔离，确保租户A无法访问租户B的数据。

#### 测试步骤

**步骤1：准备多租户测试数据**
```python
# 为租户1创建客户数据
insert_data_python_exe({
    "table": "crm_customer",
    "data": {
        "name": "租户1客户A",
        "phone": "13800138001", 
        "tenant_id": 1
    }
})

# 为租户2创建客户数据
insert_data_python_exe({
    "table": "crm_customer", 
    "data": {
        "name": "租户2客户B",
        "phone": "13800138002",
        "tenant_id": 2
    }
})
```

**步骤2：以租户1用户登录**
```javascript
browser_navigate_Playwright({
    "url": "http://localhost:3006/login"
})

// 登录租户1用户
browser_type_Playwright({
    "element": "用户名输入框",
    "ref": "input[placeholder='用户名']",
    "text": "tenant1_user"
})

browser_type_Playwright({
    "element": "密码输入框", 
    "ref": "input[placeholder='密码']",
    "text": "123456"
})

browser_click_Playwright({
    "element": "登录按钮",
    "ref": "button[type='submit']"
})
```

**步骤3：访问客户列表页面**
```javascript
// 导航到客户管理页面
browser_navigate_Playwright({
    "url": "http://localhost:3006/crm/customer"
})

browser_wait_for_Playwright({
    "text": "客户列表",
    "time": 5
})
```

**步骤4：验证数据隔离**
```javascript
// 获取页面快照
browser_snapshot_Playwright()

// 验证只能看到租户1的客户
browser_evaluate_Playwright({
    "function": "() => { const rows = document.querySelectorAll('.el-table__row'); return Array.from(rows).map(row => row.textContent); }"
})

// 截图保存结果
browser_take_screenshot_Playwright({
    "filename": "tenant1_customer_list.png"
})
```

**步骤5：切换到租户2验证**
```python
# 查询租户2的数据，验证隔离性
fetch_data_python_exe({
    "query": "SELECT * FROM crm_customer WHERE tenant_id = 2"
})
```

### 测试用例4：CRM客户管理流程测试

#### 测试目标
测试CRM模块的完整业务流程，包括客户创建、编辑、删除、状态变更等操作。

#### 测试步骤

**步骤1：导航到客户管理页面**
```javascript
browser_navigate_Playwright({
    "url": "http://localhost:3006/crm/customer"
})

browser_wait_for_Playwright({
    "text": "客户管理",
    "time": 5
})
```

**步骤2：创建新客户**
```javascript
// 点击新增按钮
browser_click_Playwright({
    "element": "新增客户按钮",
    "ref": "button:has-text('新增')"
})

// 等待弹窗出现
browser_wait_for_Playwright({
    "text": "新增客户",
    "time": 3
})

// 填写客户信息
browser_type_Playwright({
    "element": "客户名称输入框",
    "ref": "input[placeholder='请输入客户名称']",
    "text": "测试客户公司"
})

browser_type_Playwright({
    "element": "联系电话输入框", 
    "ref": "input[placeholder='请输入联系电话']",
    "text": "13800138000"
})

// 选择客户类型
browser_select_option_Playwright({
    "element": "客户类型下拉框",
    "ref": "select[name='customer_type']",
    "values": ["企业客户"]
})

// 提交表单
browser_click_Playwright({
    "element": "确定按钮",
    "ref": "button:has-text('确定')"
})
```

**步骤3：验证客户创建成功**
```javascript
// 等待成功提示
browser_wait_for_Playwright({
    "text": "创建成功",
    "time": 3
})

// 刷新页面验证数据
browser_navigate_Playwright({
    "url": "http://localhost:3006/crm/customer"
})

// 搜索新创建的客户
browser_type_Playwright({
    "element": "搜索输入框",
    "ref": "input[placeholder='请输入客户名称']", 
    "text": "测试客户公司"
})

browser_click_Playwright({
    "element": "搜索按钮",
    "ref": "button:has-text('搜索')"
})

// 截图保存结果
browser_take_screenshot_Playwright({
    "filename": "customer_created.png"
})
```

## 🔍 测试验证方法

### 1. 页面元素验证
```javascript
// 使用browser_snapshot_Playwright获取页面结构
// 验证关键元素是否存在
browser_evaluate_Playwright({
    "function": "() => document.querySelector('.target-element') !== null"
})
```

### 2. 数据库状态验证
```python
# 使用fetch_data_python_exe验证数据库状态
fetch_data_python_exe({
    "query": "SELECT COUNT(*) as count FROM crm_customer WHERE name = '测试客户公司'"
})
```

### 3. API响应验证
```javascript
// 使用browser_evaluate_Playwright监听网络请求
browser_evaluate_Playwright({
    "function": "() => { window.testApiResponses = []; window.fetch = new Proxy(window.fetch, { apply: (target, thisArg, args) => { return target.apply(thisArg, args).then(response => { window.testApiResponses.push({url: args[0], status: response.status}); return response; }); } }); }"
})
```

## 📊 测试报告生成

### 自动化测试报告
```javascript
// 在测试结束时生成报告
browser_evaluate_Playwright({
    "function": "() => { const report = { timestamp: new Date().toISOString(), testResults: window.testResults || [], screenshots: window.screenshots || [] }; return JSON.stringify(report); }"
})
```

### 测试数据清理
```python
# 测试完成后清理测试数据
execute_query_python_exe({
    "query": "DELETE FROM crm_customer WHERE name LIKE '测试%'"
})
```

## 🎯 最佳实践

### 1. 测试数据管理
- 每个测试用例使用独立的测试数据
- 测试前准备数据，测试后清理数据
- 使用事务确保数据一致性

### 2. 页面等待策略
- 使用`browser_wait_for_Playwright`等待特定元素或文本
- 避免使用固定时间等待，优先使用条件等待
- 设置合理的超时时间

### 3. 错误处理
- 每个关键步骤后进行验证
- 使用截图记录错误状态
- 提供详细的错误信息

### 4. 测试可维护性
- 使用页面对象模型封装页面操作
- 将常用操作封装为可复用的函数
- 保持测试用例的独立性

## 📋 测试执行清单

- [ ] 环境准备：确保测试环境正常运行
- [ ] 数据准备：创建必要的测试数据
- [ ] 工具配置：确认MCP工具配置正确
- [ ] 测试执行：按照测试用例逐步执行
- [ ] 结果验证：检查测试结果和截图
- [ ] 数据清理：清理测试产生的数据
- [ ] 报告生成：生成测试报告

---

**文档版本**: v1.0  
**创建日期**: 2025-01-05  
**适用范围**: 系统集成测试  
**工具依赖**: Playwright MCP工具、数据库操作工具
