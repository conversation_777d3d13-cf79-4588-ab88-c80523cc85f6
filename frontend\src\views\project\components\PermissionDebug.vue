<template>
  <div class="permission-debug">
    <el-card header="权限调试信息">
      <div class="debug-section">
        <h4>当前路由信息</h4>
        <p><strong>路径:</strong> {{ route.path }}</p>
        <p><strong>名称:</strong> {{ route.name }}</p>
        
        <h4>权限列表 ({{ authList.length }} 个)</h4>
        <div class="auth-list">
          <el-tag 
            v-for="auth in authList" 
            :key="auth.auth_mark" 
            size="small" 
            class="auth-tag"
          >
            {{ auth.auth_mark }}
          </el-tag>
        </div>
        
        <h4>任务相关权限检查 (正确格式)</h4>
        <div class="permission-checks">
          <div class="permission-item">
            <span class="permission-name">project:project_task:get_comments</span>
            <el-tag :type="hasAuth('project:project_task:get_comments') ? 'success' : 'danger'">
              {{ hasAuth('project:project_task:get_comments') ? '✅ 有权限' : '❌ 无权限' }}
            </el-tag>
          </div>

          <div class="permission-item">
            <span class="permission-name">project:project_task:add_comment</span>
            <el-tag :type="hasAuth('project:project_task:add_comment') ? 'success' : 'danger'">
              {{ hasAuth('project:project_task:add_comment') ? '✅ 有权限' : '❌ 无权限' }}
            </el-tag>
          </div>

          <div class="permission-item">
            <span class="permission-name">project:project_task:get_follows</span>
            <el-tag :type="hasAuth('project:project_task:get_follows') ? 'success' : 'danger'">
              {{ hasAuth('project:project_task:get_follows') ? '✅ 有权限' : '❌ 无权限' }}
            </el-tag>
          </div>

          <div class="permission-item">
            <span class="permission-name">project:project_task:add_follow</span>
            <el-tag :type="hasAuth('project:project_task:add_follow') ? 'success' : 'danger'">
              {{ hasAuth('project:project_task:add_follow') ? '✅ 有权限' : '❌ 无权限' }}
            </el-tag>
          </div>
        </div>
        
        <h4>测试其他权限格式</h4>
        <div class="permission-checks">
          <div class="permission-item">
            <span class="permission-name">project:task:get_comments (错误格式)</span>
            <el-tag :type="hasAuth('project:task:get_comments') ? 'success' : 'danger'">
              {{ hasAuth('project:task:get_comments') ? '✅ 有权限' : '❌ 无权限' }}
            </el-tag>
          </div>

          <div class="permission-item">
            <span class="permission-name">get_comments (简短格式)</span>
            <el-tag :type="hasAuth('get_comments') ? 'success' : 'danger'">
              {{ hasAuth('get_comments') ? '✅ 有权限' : '❌ 无权限' }}
            </el-tag>
          </div>

          <div class="permission-item">
            <span class="permission-name">add_comment (简短格式)</span>
            <el-tag :type="hasAuth('add_comment') ? 'success' : 'danger'">
              {{ hasAuth('add_comment') ? '✅ 有权限' : '❌ 无权限' }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuth } from '@/composables/useAuth'

const route = useRoute()
const { hasAuth } = useAuth()

// 获取权限列表
const authList = computed(() => {
  return (route.meta.authList as Array<{ auth_mark: string }>) || []
})
</script>

<style scoped>
.permission-debug {
  margin: 20px 0;
}

.debug-section h4 {
  margin: 20px 0 10px 0;
  color: #409eff;
}

.auth-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 10px 0;
}

.auth-tag {
  margin: 2px;
}

.permission-checks {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
}

.permission-name {
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #606266;
}
</style>
