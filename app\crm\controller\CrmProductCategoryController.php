<?php
declare(strict_types=1);

namespace app\crm\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\common\core\crud\traits\ControllerImportExportTrait;
use app\crm\service\CrmProductCategoryService;
use think\facade\Log;
use think\response\Json;

/**
 * 产品分类表控制器
 */
class CrmProductCategoryController extends BaseController
{
	use CrudControllerTrait, ControllerImportExportTrait;
	
	/**
	 * @var CrmProductCategoryService
	 */
	protected $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service = CrmProductCategoryService::getInstance();
	}
	
	/**
	 * 状态切换
	 */
	public function status($id)
	{
		$status = $this->request->post('status');
		$result = $this->service->updateField($id, 'status', $status);
		return $this->success('状态更新成功', $result);
	}
	
	/**
	 * 获取列表（重写以包含父级分类名称）
	 */
	public function index()
	{
		$result = $this->service->searchTree($this->request->param());
		return $this->success('获取成功', [
			'list' => $result
		]);
	}
	
	public function add(): Json
	{
		$params = $this->request->post();
		if (empty($params['parent_id'])) {
			$params['parent_id'] = 0;
		}
		try {
			$result = $this->service->add($params);
			return $result
				? $this->success('添加成功')
				: $this->error('添加失败');
		}
		catch (\Exception $e) {
			Log::error('CRUD Add Debug - Exception: ' . $e->getMessage());
			return $this->error('添加失败');
		}
	}
	
	/**
	 * 获取产品分类树形选项
	 */
	public function options()
	{
		$result = $this->service->getOptions();
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 获取详情（重写以包含父级分类名称）
	 */
	public function detail($id)
	{
		$result = $this->service->getOne([
			[
				'id',
				'=',
				$id
			]
		]);
		$result = $this->service->addParentName($result);
		return $this->success('获取成功', $result);
	}
	
}