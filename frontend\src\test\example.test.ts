import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { defineComponent } from 'vue'

// 创建一个简单的测试组件
const TestComponent = defineComponent({
  name: 'TestComponent',
  template: `
    <div class="test-component">
      <h1>{{ title }}</h1>
      <button @click="increment">{{ count }}</button>
    </div>
  `,
  data() {
    return {
      title: 'Test Component',
      count: 0
    }
  },
  methods: {
    increment() {
      this.count++
    }
  }
})

describe('测试环境验证', () => {
  it('应该能够正常运行基本测试', () => {
    expect(1 + 1).toBe(2)
  })

  it('应该能够挂载Vue组件', () => {
    const wrapper = mount(TestComponent)
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('h1').text()).toBe('Test Component')
  })

  it('应该能够测试组件交互', async () => {
    const wrapper = mount(TestComponent)
    const button = wrapper.find('button')
    
    expect(button.text()).toBe('0')
    
    await button.trigger('click')
    expect(button.text()).toBe('1')
    
    await button.trigger('click')
    expect(button.text()).toBe('2')
  })

  it('应该能够测试DOM查询', () => {
    const wrapper = mount(TestComponent)
    
    expect(wrapper.find('.test-component').exists()).toBe(true)
    expect(wrapper.find('h1').exists()).toBe(true)
    expect(wrapper.find('button').exists()).toBe(true)
  })

  it('应该能够访问组件数据', () => {
    const wrapper = mount(TestComponent)
    
    expect(wrapper.vm.title).toBe('Test Component')
    expect(wrapper.vm.count).toBe(0)
  })
})

describe('Mock功能验证', () => {
  it('应该能够使用localStorage mock', () => {
    localStorage.setItem('test', 'value')
    expect(localStorage.setItem).toHaveBeenCalledWith('test', 'value')
  })

  it('应该能够使用sessionStorage mock', () => {
    sessionStorage.setItem('test', 'value')
    expect(sessionStorage.setItem).toHaveBeenCalledWith('test', 'value')
  })

  it('应该能够访问window.location mock', () => {
    expect(window.location.href).toBe('http://localhost:3006')
    expect(window.location.hostname).toBe('localhost')
    expect(window.location.port).toBe('3006')
  })
})
