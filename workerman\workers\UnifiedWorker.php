<?php
declare(strict_types=1);

namespace workerman\workers;

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../../app/common.php';

use Workerman\Worker;
use Workerman\Lib\Timer;
use workerman\common\WorkerBase;
use workerman\common\ConfigManager;
use app\crm\service\CustomerSeaManagementService;
use app\notice\service\NoticeQueueService;
use app\notice\service\NoticeMessageService;
use app\notice\service\NoticeDispatcherService;
use workerman\services\AdvanceNoticeService;
use workerman\services\TaskReminderService;
use think\facade\Log;

/**
 * 统一Worker类
 * 集成所有自动化功能：公海回收、消息队列处理、WebSocket服务
 */
class UnifiedWorker extends WorkerBase
{
    /**
     * WebSocket Worker实例
     */
    private Worker $wsWorker;
    
    /**
     * 用户WebSocket连接映射
     */
    private array $userConnections = [];

    /**
     * 连接心跳映射
     */
    private array $connectionHeartbeats = [];

    /**
     * 定时器ID映射
     */
    private array $timers = [];
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct('UnifiedWorker');
        
        // 创建WebSocket服务器
        $this->wsWorker = new Worker("websocket://0.0.0.0:8282");
        $this->wsWorker->name = 'UnifiedWorker';
        $this->wsWorker->count = 1;
        
        // 设置回调函数
        $this->wsWorker->onWorkerStart = [$this, 'onWorkerStart'];
        $this->wsWorker->onConnect = [$this, 'onConnect'];
        $this->wsWorker->onMessage = [$this, 'onMessage'];
        $this->wsWorker->onClose = [$this, 'onClose'];
        $this->wsWorker->onError = [$this, 'onError'];
    }
    
    /**
     * Worker启动时的回调
     */
    public function onWorkerStart($worker): void
    {
        $this->log("统一Worker启动，集成所有自动化功能");
        
        // 启动各种定时任务
        $this->startTimers();
        
        $this->log("所有定时任务已启动");
        $this->logMemoryUsage();
    }
    
    /**
     * 启动所有定时器
     */
    private function startTimers(): void
    {
        // 1. 公海回收定时任务（每小时执行）
        $this->timers['sea_recycle'] = Timer::add(3600, function() {
            $this->safeExecute([$this, 'executeSeaRecycle'], '公海回收');
        });
        
        // 2. 消息队列处理（每10秒执行）
        $this->timers['message_queue'] = Timer::add(10, function() {
            $this->safeExecute([$this, 'processMessageQueue'], '消息队列处理');
        });
        
        // 3. 延迟消息处理（每5分钟执行）
        $this->timers['delayed_messages'] = Timer::add(300, function() {
            $this->safeExecute([$this, 'processDelayedMessages'], '延迟消息处理');
        });

        // 4. 提前通知检查（每小时执行）
        $this->timers['advance_notice'] = Timer::add(3600, function() {
            $this->safeExecute([$this, 'executeAdvanceNotice'], '提前通知检查');
        });

        // 5. 任务提醒检查（每30分钟执行）
        $this->timers['task_reminder'] = Timer::add(1800, function() {
            $this->safeExecute([$this, 'executeTaskReminder'], '任务提醒检查');
        });

        // 6. WebSocket心跳检测（每30秒执行）
        $this->timers['heartbeat_check'] = Timer::add(30, function() {
            $this->safeExecute([$this, 'checkHeartbeat'], 'WebSocket心跳检测');
        });

        // 7. 内存监控（每30分钟执行）
        $this->timers['memory_monitor'] = Timer::add(1800, function() {
            $this->logMemoryUsage();
        });
        
        $this->log("定时器启动完成", 'info', [
            'timers' => array_keys($this->timers)
        ]);
    }
    
    /**
     * 执行公海回收
     */
    public function executeSeaRecycle(): void
    {
        $this->log("开始执行公海回收");
        
        // 获取公海规则配置
        $config = ConfigManager::getSeaRuleConfig();
        
        if (empty($config['sea_status'])) {
            $this->log("公海功能未启用，跳过执行");
            return;
        }
        
        try {
            // 调用现有的公海回收服务
            $service = CustomerSeaManagementService::getInstance();
            $result = $service->executeSeaRules();
            
            if ($result['success']) {
                $this->log("公海回收完成", 'info', [
                    'recycled_count' => $result['data']['recycled_count'] ?? 0,
                    'processed_rules' => $result['data']['processed_rules'] ?? 0
                ]);
                
                // 如果有回收操作，发送通知
                if (!empty($result['data']['recycled_count'])) {
                    $this->sendSeaRecycleNotifications($result['data']);
                }
            } else {
                $this->log("公海回收失败: " . $result['message'], 'error');
            }
            
        } catch (\Throwable $e) {
            $this->log("公海回收异常: " . $e->getMessage(), 'error', [
                'exception' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 发送公海回收通知
     */
    private function sendSeaRecycleNotifications(array $data): void
    {
        // 这里需要根据实际的返回数据结构来实现
        // 暂时记录日志，后续完善
        $this->log("发送公海回收通知", 'info', $data);
        
        // TODO: 实现具体的通知发送逻辑
        // 1. 获取受影响的用户列表
        // 2. 发送系统通知
        // 3. 推送WebSocket实时通知
    }
    
    /**
     * 处理消息队列
     */
    public function processMessageQueue(): void
    {
        try {
            $queueService = NoticeQueueService::getInstance();
            $result = $queueService->processQueue(50);
            
            // 如果处理了消息，更新用户未读数
            if ($result['success'] > 0) {
                $this->updateUnreadCounts();
            }
            
            if ($result['success'] > 0 || $result['failed'] > 0) {
                $this->log("队列处理完成", 'info', [
                    'success' => $result['success'],
                    'failed' => $result['failed']
                ]);
            }
            
        } catch (\Throwable $e) {
            $this->log("消息队列处理异常: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 处理延迟消息
     */
    public function processDelayedMessages(): void
    {
        try {
            $messageService = NoticeMessageService::getInstance();
            $count = $messageService->processDelayedMessages(100);

            if ($count > 0) {
                $this->log("延迟消息处理完成", 'info', ['count' => $count]);
                $this->updateUnreadCounts();
            }

        } catch (\Throwable $e) {
            $this->log("延迟消息处理异常: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 执行提前通知检查
     */
    public function executeAdvanceNotice(): void
    {
        $this->log("开始执行提前通知检查");

        // 获取提前通知配置
        $config = ConfigManager::getAdvanceNoticeConfig();

        if (empty($config['notice_enabled'])) {
            $this->log("提前通知功能未启用，跳过执行");
            return;
        }

        try {
            $advanceNoticeService = new AdvanceNoticeService();
            $result = $advanceNoticeService->executeAdvanceNotice($config);

            if ($result['success']) {
                $this->log("提前通知检查完成", 'info', $result['data']);

                // 如果发送了通知，更新用户未读数
                if (!empty($result['data']['sent_count'])) {
                    $this->updateUnreadCounts();
                }
            } else {
                $this->log("提前通知检查失败: " . $result['message'], 'error');
            }

        } catch (\Throwable $e) {
            $this->log("提前通知检查异常: " . $e->getMessage(), 'error', [
                'exception' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 执行任务提醒检查
     */
    public function executeTaskReminder(): void
    {
        $this->log("开始执行任务提醒检查");

        // 获取任务提醒配置
        $config = ConfigManager::getTaskReminderConfig();

        if (empty($config['reminder_enabled'])) {
            $this->log("任务提醒功能未启用，跳过执行");
            return;
        }

        try {
            $taskReminderService = new TaskReminderService();
            $result = $taskReminderService->executeTaskReminder($config);

            if ($result['success']) {
                $this->log("任务提醒检查完成", 'info', $result['data']);

                // 如果发送了提醒，更新用户未读数
                if (!empty($result['data']['total_processed'])) {
                    $this->updateUnreadCounts();
                }
            } else {
                $this->log("任务提醒检查失败: " . $result['message'], 'error');
            }

        } catch (\Throwable $e) {
            $this->log("任务提醒检查异常: " . $e->getMessage(), 'error', [
                'exception' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * WebSocket连接建立
     */
    public function onConnect($connection): void
    {
        $this->log("WebSocket新连接: {$connection->id}");
    }

    /**
     * WebSocket消息处理
     */
    public function onMessage($connection, $data): void
    {
        try {
            $message = json_decode($data, true);

            if (!$message || !isset($message['type'])) {
                $this->sendError($connection, '无效的消息格式');
                return;
            }

            switch ($message['type']) {
                case 'auth':
                    $this->handleAuth($connection, $message);
                    break;

                case 'heartbeat':
                    $this->handleHeartbeat($connection, $message);
                    break;

                case 'mark_read':
                    $this->handleMarkRead($connection, $message);
                    break;

                case 'get_notifications':
                    $this->handleGetNotifications($connection, $message);
                    break;

                default:
                    $this->sendError($connection, '不支持的消息类型');
            }

        } catch (\Throwable $e) {
            $this->log("WebSocket消息处理异常: " . $e->getMessage(), 'error');
            $this->sendError($connection, '消息处理失败');
        }
    }
    
    /**
     * 处理用户认证
     */
    private function handleAuth($connection, array $message): void
    {
        $userId = $message['user_id'] ?? 0;
        $token = $message['token'] ?? '';

        if ($userId > 0 && $this->validateUserToken($userId, $token)) {
            $this->userConnections[$userId] = $connection;
            $connection->user_id = $userId;
            $connection->last_heartbeat = time();
            $this->connectionHeartbeats[$connection->id] = time();

            $this->sendSuccess($connection, 'auth_success', [
                'message' => '认证成功',
                'user_id' => $userId
            ]);

            // 发送当前未读数和最新通知
            $this->sendUnreadCount($userId);
            $this->sendLatestNotifications($userId);

            $this->log("用户认证成功: {$userId}");
        } else {
            $this->sendError($connection, '认证失败');
        }
    }

    /**
     * 处理心跳消息
     */
    private function handleHeartbeat($connection, array $message): void
    {
        if (isset($connection->user_id)) {
            $connection->last_heartbeat = time();
            $this->connectionHeartbeats[$connection->id] = time();

            $this->sendSuccess($connection, 'heartbeat_response', [
                'timestamp' => time()
            ]);
        }
    }

    /**
     * 处理标记已读
     */
    private function handleMarkRead($connection, array $message): void
    {
        if (!isset($connection->user_id)) {
            $this->sendError($connection, '未认证');
            return;
        }

        $notificationIds = $message['notification_ids'] ?? [];
        if (empty($notificationIds)) {
            $this->sendError($connection, '通知ID不能为空');
            return;
        }

        try {
            // 调用通知服务标记已读
            $messageService = NoticeMessageService::getInstance();
            $result = $messageService->markAsRead($connection->user_id, $notificationIds);

            if ($result) {
                $this->sendSuccess($connection, 'mark_read_success', [
                    'marked_count' => count($notificationIds)
                ]);

                // 更新未读数
                $this->sendUnreadCount($connection->user_id);
            } else {
                $this->sendError($connection, '标记已读失败');
            }

        } catch (\Throwable $e) {
            $this->log("标记已读异常: " . $e->getMessage(), 'error');
            $this->sendError($connection, '标记已读失败');
        }
    }

    /**
     * 处理获取通知列表
     */
    private function handleGetNotifications($connection, array $message): void
    {
        if (!isset($connection->user_id)) {
            $this->sendError($connection, '未认证');
            return;
        }

        $page = $message['page'] ?? 1;
        $limit = $message['limit'] ?? 20;
        $type = $message['notification_type'] ?? '';

        try {
            $messageService = NoticeMessageService::getInstance();
            $notifications = $messageService->getNotificationList($connection->user_id, $page, $limit, $type);

            $this->sendSuccess($connection, 'notifications_list', [
                'notifications' => $notifications,
                'page' => $page,
                'limit' => $limit
            ]);

        } catch (\Throwable $e) {
            $this->log("获取通知列表异常: " . $e->getMessage(), 'error');
            $this->sendError($connection, '获取通知列表失败');
        }
    }

    /**
     * WebSocket连接关闭
     */
    public function onClose($connection): void
    {
        if (isset($connection->user_id)) {
            unset($this->userConnections[$connection->user_id]);
            $this->log("用户断开连接: {$connection->user_id}");
        }

        if (isset($connection->id)) {
            unset($this->connectionHeartbeats[$connection->id]);
        }
    }
    
    /**
     * WebSocket错误处理
     */
    public function onError($connection, $code, $msg): void
    {
        $this->log("WebSocket错误: {$code} - {$msg}", 'error');
    }
    
    /**
     * 向指定用户发送未读消息数
     */
    private function sendUnreadCount(int $userId): void
    {
        if (isset($this->userConnections[$userId])) {
            try {
                $unreadCount = NoticeMessageService::getInstance()->getUnreadCount($userId);
                $this->userConnections[$userId]->send(json_encode([
                    'type' => 'unread_count_update',
                    'data' => ['count' => $unreadCount]
                ]));
            } catch (\Throwable $e) {
                $this->log("发送未读数异常: " . $e->getMessage(), 'error');
            }
        }
    }
    
    /**
     * 更新所有在线用户的未读数
     */
    private function updateUnreadCounts(): void
    {
        foreach ($this->userConnections as $userId => $connection) {
            $this->sendUnreadCount($userId);
        }
    }

    /**
     * 发送系统通知（使用现有的system_notice模板）
     */
    private function sendSystemNotice(string $title, string $content, array $recipients): void
    {
        try {
            $noticeService = \app\notice\service\NoticeDispatcherService::getInstance();
            $result = $noticeService->send('system', 'notice', [
                'title' => $title,
                'content' => $content
            ], $recipients, [
                'send_channels' => 'site',
                'priority' => 1,
                'creator_id' => 1
            ]);

            if ($result) {
                $this->log("系统通知发送成功: {$title}, 接收人数: " . count($recipients));

                // 实时推送给在线用户
                $this->pushNotificationToOnlineUsers($recipients, $title, $content);

                // 更新在线用户的未读数
                foreach ($recipients as $userId) {
                    if (isset($this->userConnections[$userId])) {
                        $this->sendUnreadCount($userId);
                    }
                }
            } else {
                $this->log("系统通知发送失败: {$title}", 'error');
            }

        } catch (\Throwable $e) {
            $this->log("发送系统通知异常: " . $e->getMessage(), 'error');
        }
    }

    /**
     * 推送通知给在线用户
     */
    private function pushNotificationToOnlineUsers(array $recipients, string $title, string $content): void
    {
        foreach ($recipients as $userId) {
            if (isset($this->userConnections[$userId])) {
                try {
                    $this->sendSuccess($this->userConnections[$userId], 'new_notification', [
                        'title' => $title,
                        'content' => $content,
                        'timestamp' => time(),
                        'type' => 'system'
                    ]);
                } catch (\Throwable $e) {
                    $this->log("推送通知给用户{$userId}失败: " . $e->getMessage(), 'error');
                }
            }
        }
    }
    
    /**
     * 检查心跳
     */
    public function checkHeartbeat(): void
    {
        $now = time();
        $timeout = 90; // 90秒超时

        foreach ($this->connectionHeartbeats as $connectionId => $lastHeartbeat) {
            if ($now - $lastHeartbeat > $timeout) {
                // 查找对应的连接
                foreach ($this->userConnections as $userId => $connection) {
                    if ($connection->id == $connectionId) {
                        $this->log("用户连接超时，断开连接: {$userId}");
                        $connection->close();
                        unset($this->userConnections[$userId]);
                        break;
                    }
                }
                unset($this->connectionHeartbeats[$connectionId]);
            }
        }
    }

    /**
     * 验证用户Token
     */
    private function validateUserToken(int $userId, string $token): bool
    {
        // 这里应该实现真正的Token验证逻辑
        // 暂时简单验证非空
        return !empty($token) && $userId > 0;
    }

    /**
     * 发送成功响应
     */
    private function sendSuccess($connection, string $type, array $data = []): void
    {
        $response = [
            'type' => $type,
            'success' => true,
            'data' => $data,
            'timestamp' => time()
        ];

        $connection->send(json_encode($response, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 发送错误响应
     */
    private function sendError($connection, string $message): void
    {
        $response = [
            'type' => 'error',
            'success' => false,
            'message' => $message,
            'timestamp' => time()
        ];

        $connection->send(json_encode($response, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 发送最新通知
     */
    private function sendLatestNotifications(int $userId): void
    {
        if (isset($this->userConnections[$userId])) {
            try {
                $messageService = NoticeMessageService::getInstance();
                $notifications = $messageService->getLatestNotifications($userId, 5);

                $this->sendSuccess($this->userConnections[$userId], 'latest_notifications', [
                    'notifications' => $notifications
                ]);

            } catch (\Throwable $e) {
                $this->log("发送最新通知异常: " . $e->getMessage(), 'error');
            }
        }
    }

    /**
     * 广播通知给所有在线用户
     */
    public function broadcastNotification(array $notification): void
    {
        foreach ($this->userConnections as $userId => $connection) {
            try {
                $this->sendSuccess($connection, 'new_notification', [
                    'notification' => $notification
                ]);
            } catch (\Throwable $e) {
                $this->log("广播通知异常: " . $e->getMessage(), 'error');
            }
        }
    }

    /**
     * 记录内存使用情况
     */
    private function logMemoryUsage(): void
    {
        $memory = $this->getMemoryUsage();
        $this->log("内存使用情况", 'info', [
            'current' => $memory['current_formatted'],
            'peak' => $memory['peak_formatted'],
            'connections' => count($this->userConnections),
            'heartbeats' => count($this->connectionHeartbeats)
        ]);
    }
}
