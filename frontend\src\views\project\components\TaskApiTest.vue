<template>
  <div class="task-api-test">
    <el-card header="任务API测试">
      <div class="test-section">
        <h3>API 调用测试</h3>

        <div class="test-buttons">
          <el-button @click="testAddComment" type="primary"> 测试添加评论 </el-button>

          <el-button @click="testAddFollow" type="success"> 测试添加跟进 </el-button>

          <el-button @click="testGetComments" type="info"> 测试获取评论 </el-button>

          <el-button @click="testGetFollows" type="warning"> 测试获取跟进 </el-button>
        </div>

        <div class="test-results">
          <h4>测试结果:</h4>
          <pre>{{ testResults }}</pre>
        </div>
      </div>

      <div class="permission-section">
        <h3>权限测试</h3>

        <div class="permission-status">
          <p
            >查看评论权限:
            <span :class="canViewComments ? 'success' : 'error'">{{
              canViewComments ? '✅' : '❌'
            }}</span>
          </p>
          <p
            >添加评论权限:
            <span :class="canAddComment ? 'success' : 'error'">{{
              canAddComment ? '✅' : '❌'
            }}</span></p
          >
          <p
            >查看跟进权限:
            <span :class="canViewFollows ? 'success' : 'error'">{{
              canViewFollows ? '✅' : '❌'
            }}</span>
          </p>
          <p
            >添加跟进权限:
            <span :class="canAddFollow ? 'success' : 'error'">{{
              canAddFollow ? '✅' : '❌'
            }}</span></p
          >
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import { TaskApi } from '@/api/project/projectApi'
  import { useAuth } from '@/composables/useAuth'

  // 测试用的任务ID
  const testTaskId = ref(1)
  const testResults = ref('')

  // 权限验证
  const { hasAuth } = useAuth()

  // 权限计算属性
  const canViewComments = computed(() => hasAuth('project:project_task:get_comments'))
  const canAddComment = computed(() => hasAuth('project:project_task:add_comment'))
  const canViewFollows = computed(() => hasAuth('project:project_task:get_comments'))
  const canAddFollow = computed(() => hasAuth('project:project_task:add_follow'))

  // 测试方法
  const testAddComment = async () => {
    try {
      testResults.value = '正在测试添加评论...\n'

      const response = await TaskApi.addComment({
        task_id: testTaskId.value,
        content: '这是一个测试评论',
        attachments: []
      })

      testResults.value += `添加评论成功: ${JSON.stringify(response, null, 2)}\n`
      ElMessage.success('添加评论测试成功')
    } catch (error: any) {
      testResults.value += `添加评论失败: ${error.message}\n`
      ElMessage.error(`添加评论测试失败: ${error.message}`)
    }
  }

  const testAddFollow = async () => {
    try {
      testResults.value = '正在测试添加跟进...\n'

      const response = await TaskApi.addFollow({
        task_id: testTaskId.value,
        content: '这是一个测试跟进',
        follow_type: 'phone',
        follow_date: new Date().toISOString().split('T')[0],
        next_plan: '下次计划',
        next_date: new Date().toISOString().split('T')[0],
        attachments: []
      })

      testResults.value += `添加跟进成功: ${JSON.stringify(response, null, 2)}\n`
      ElMessage.success('添加跟进测试成功')
    } catch (error: any) {
      testResults.value += `添加跟进失败: ${error.message}\n`
      ElMessage.error(`添加跟进测试失败: ${error.message}`)
    }
  }

  const testGetComments = async () => {
    try {
      testResults.value = '正在测试获取评论...\n'

      const response = await TaskApi.getComments(testTaskId.value, {
        page: 1,
        limit: 10
      })

      testResults.value += `获取评论成功: ${JSON.stringify(response, null, 2)}\n`
      ElMessage.success('获取评论测试成功')
    } catch (error: any) {
      testResults.value += `获取评论失败: ${error.message}\n`
      ElMessage.error(`获取评论测试失败: ${error.message}`)
    }
  }

  const testGetFollows = async () => {
    try {
      testResults.value = '正在测试获取跟进...\n'

      const response = await TaskApi.getFollows(testTaskId.value, {
        page: 1,
        limit: 10
      })

      testResults.value += `获取跟进成功: ${JSON.stringify(response, null, 2)}\n`
      ElMessage.success('获取跟进测试成功')
    } catch (error: any) {
      testResults.value += `获取跟进失败: ${error.message}\n`
      ElMessage.error(`获取跟进测试失败: ${error.message}`)
    }
  }
</script>

<style scoped>
  .task-api-test {
    padding: 20px;
  }

  .test-section,
  .permission-section {
    margin-bottom: 30px;
  }

  .test-buttons {
    margin: 20px 0;
  }

  .test-buttons .el-button {
    margin-right: 10px;
    margin-bottom: 10px;
  }

  .test-results {
    margin-top: 20px;
  }

  .test-results pre {
    background: #f5f5f5;
    padding: 15px;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  .permission-status p {
    margin: 10px 0;
    font-size: 14px;
  }

  .success {
    color: #67c23a;
    font-weight: bold;
  }

  .error {
    color: #f56c6c;
    font-weight: bold;
  }
</style>
