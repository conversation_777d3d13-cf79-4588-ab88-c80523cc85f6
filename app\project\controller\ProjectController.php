<?php
declare(strict_types=1);

namespace app\project\controller;

use app\common\core\base\BaseController;
use app\common\core\crud\traits\CrudControllerTrait;
use app\project\service\ProjectProjectService;
use app\project\service\ProjectTaskService;
use think\response\Json;

/**
 * 项目表控制器
 */
class ProjectController extends BaseController
{
	use CrudControllerTrait;
	
	/**
	 * @var ProjectProjectService
	 */
	protected ProjectProjectService $service;
	
	/**
	 * 初始化
	 */
	public function initialize(): void
	{
		parent::initialize();
		
		// 使用单例模式获取Service实例
		$this->service = ProjectProjectService::getInstance();
	}
	
	/**
	 * 获取项目列表（重写以包含统计信息）
	 */
	public function index(): Json
	{
		try {
			$params = $this->request->param();
			$result = $this->service->search($params, [], ['owner']);

			// 为每个项目添加统计信息
			if (!empty($result['list'])) {
				foreach ($result['list'] as &$project) {
					$projectModel = \app\project\model\ProjectProject::find($project['id']);
					if ($projectModel) {
						$stats = $projectModel->getProjectStats();
						$project['task_count'] = $stats['total_tasks'];
						$project['member_count'] = $stats['member_count'];
						$project['progress'] = $stats['progress'];
						$project['completed_tasks'] = $stats['completed_tasks'];
						$project['in_progress_tasks'] = $stats['in_progress_tasks'];
						$project['todo_tasks'] = $stats['todo_tasks'];
					}
				}
			}

			return $this->success('获取成功', $result);
		} catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}

	/**
	 * 状态切换
	 */
	public function status($id)
	{
		$status = $this->request->post('status');
		$result = $this->service->updateField($id, 'status', $status);
		return $this->success('状态更新成功', $result);
	}
	
	/**
	 * 获取我的项目列表（包含统计信息）
	 */
	public function myProjects()
	{
		try {
			$userId = get_user_id();
			$params = $this->request->param();
			$result = $this->service->getMyProjects($userId, $params);

			// 为每个项目添加统计信息
			if (!empty($result['list'])) {
				foreach ($result['list'] as &$project) {
					$projectModel = \app\project\model\ProjectProject::find($project['id']);
					if ($projectModel) {
						$stats = $projectModel->getProjectStats();
						$project['task_count'] = $stats['total_tasks'];
						$project['member_count'] = $stats['member_count'];
						$project['progress'] = $stats['progress'];
						$project['completed_tasks'] = $stats['completed_tasks'];
						$project['in_progress_tasks'] = $stats['in_progress_tasks'];
						$project['todo_tasks'] = $stats['todo_tasks'];
					}
				}
			}

			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取项目详情（包含统计信息）
	 */
	public function projectDetail()
	{
		try {
			$id     = (int)$this->request->param('id');
			$result = $this->service->getProjectDetail($id);
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取项目任务列表数据
	 *
	 * @return Json
	 */
	public function tasks(): Json
	{
		try {
			$projectId = $this->request->param('project_id/d', 0);
			if (!$projectId) {
				return $this->error('项目ID不能为空');
			}

			$result = ProjectTaskService::getInstance()
			                            ->search([
				                            'project_id' => $projectId
			                            ], [], ['assignee']);
			return $this->success('获取成功', $result);
		} catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取项目看板数据
	 */
	public function kanban()
	{
		try {
			$projectId = (int)$this->request->param('project_id');
			$result    = $this->service->getKanbanData($projectId);
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 添加项目成员
	 */
	public function addMember()
	{
		try {
			$projectId = $this->request->param('project_id', 0, 'int');
			$userId    = $this->request->param('user_id', 0, 'int');
			$role      = $this->request->param('role', 'member', 'string');
			
			$result = $this->service->addProjectMember($projectId, $userId, $role);
			return $this->success('添加成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 移除项目成员
	 */
	public function removeMember()
	{
		try {
			$projectId = $this->request->param('project_id', 0, 'int');
			$userId    = $this->request->param('user_id', 0, 'int');
			
			$result = $this->service->removeProjectMember($projectId, $userId);
			return $this->success('移除成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 任务状态统计
	 */
	public function taskStatusStats()
	{
		try {
			$projectId = $this->request->param('project_id');
			$result    = $this->service->getTaskStatusStats($projectId);
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 任务优先级统计
	 */
	public function taskPriorityStats()
	{
		try {
			$projectId = $this->request->param('project_id');
			$result    = $this->service->getTaskPriorityStats($projectId);
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 项目进度趋势
	 */
	public function progressTrend()
	{
		try {
			$projectId = $this->request->param('project_id');
			$result    = $this->service->getProgressTrend($projectId);
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 成员统计
	 */
	public function memberStats()
	{
		try {
			$projectId = $this->request->param('project_id');
			$result    = $this->service->getMemberStats($projectId);
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 最近活动
	 */
	public function recentActivities()
	{
		try {
			$projectId = $this->request->param('project_id');
			$result    = $this->service->getRecentActivities($projectId);
			return $this->success('获取成功', $result);
		}
		catch (\Exception $e) {
			return $this->error($e->getMessage());
		}
	}
	
	/**
	 * 获取项目成员选项（用于任务执行人选择）
	 *
	 * @param int $id
	 * @return Json
	 */
	public function memberOptions(int $id): Json
	{
		$userId = get_user_id();
		$result = $this->service->getMemberOptions($id, $userId);
		return $this->success('获取成功', $result);
	}
	
	/**
	 * 检查用户是否为项目负责人
	 *
	 * @param int $id
	 * @return Json
	 */
	public function checkProjectOwner(int $id): Json
	{
		$userId  = get_user_id();
		$isOwner = $this->service->isProjectOwner($id, $userId);
		return $this->success('检查成功', ['is_owner' => $isOwner]);
	}
	
	/**
	 * 获取项目负责人选项列表
	 *
	 * @return Json
	 */
	public function ownerOptions(): Json
	{
		$result = $this->service->getOwnerOptions();
		return $this->success('获取成功', $result);
	}
	
}