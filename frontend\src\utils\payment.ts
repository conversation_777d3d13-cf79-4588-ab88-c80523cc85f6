/**
 * 支付方式处理工具
 * 提供统一的支付方式常量、选项和转换方法
 */

/**
 * 支付方式常量定义
 */
export const PAYMENT_METHODS = {
  BANK_TRANSFER: 1, // 银行转账
  CASH: 2, // 现金支付
  CHECK: 3, // 支票
  ALIPAY: 4, // 支付宝
  WECHAT: 5, // 微信
  OTHER: 6 // 其他
} as const

/**
 * 支付方式类型定义
 */
export type PaymentMethodType = (typeof PAYMENT_METHODS)[keyof typeof PAYMENT_METHODS]

/**
 * 支付方式选项接口
 */
export interface PaymentMethodOption {
  label: string
  value: number
  icon?: string
  color?: string
}

/**
 * 支付方式选项列表
 * 用于下拉选择器、单选框等组件
 */
export const PAYMENT_METHOD_OPTIONS: PaymentMethodOption[] = [
  {
    label: '银行转账',
    value: PAYMENT_METHODS.BANK_TRANSFER,
    icon: 'Bank',
    color: '#409EFF'
  },
  {
    label: '现金支付',
    value: PAYMENT_METHODS.CASH,
    icon: 'Money',
    color: '#67C23A'
  },
  {
    label: '支票',
    value: PAYMENT_METHODS.CHECK,
    icon: 'Document',
    color: '#E6A23C'
  },
  {
    label: '支付宝',
    value: PAYMENT_METHODS.ALIPAY,
    icon: 'Wallet',
    color: '#1677FF'
  },
  {
    label: '微信',
    value: PAYMENT_METHODS.WECHAT,
    icon: 'ChatDotRound',
    color: '#07C160'
  },
  {
    label: '其他',
    value: PAYMENT_METHODS.OTHER,
    icon: 'More',
    color: '#909399'
  }
]

/**
 * 根据支付方式值获取对应的标签文本
 *
 * @param value 支付方式值
 * @returns 支付方式标签文本
 *
 * @example
 * getPaymentMethodLabel(1) // 返回 "银行转账"
 * getPaymentMethodLabel(4) // 返回 "支付宝"
 * getPaymentMethodLabel(999) // 返回 "未知"
 */
export function getPaymentMethodLabel(value: number | null | undefined): string {
  if (value == null) return '-'

  const option = PAYMENT_METHOD_OPTIONS.find((item) => item.value === value)
  return option ? option.label : '未知'
}

/**
 * 根据支付方式值获取对应的选项对象
 *
 * @param value 支付方式值
 * @returns 支付方式选项对象或null
 *
 * @example
 * getPaymentMethodOption(1) // 返回 { label: "银行转账", value: 1, icon: "Bank", color: "#409EFF" }
 * getPaymentMethodOption(999) // 返回 null
 */
export function getPaymentMethodOption(
  value: number | null | undefined
): PaymentMethodOption | null {
  if (value == null) return null

  return PAYMENT_METHOD_OPTIONS.find((item) => item.value === value) || null
}

/**
 * 根据支付方式值获取对应的图标
 *
 * @param value 支付方式值
 * @returns 图标名称
 *
 * @example
 * getPaymentMethodIcon(1) // 返回 "Bank"
 * getPaymentMethodIcon(4) // 返回 "Wallet"
 */
export function getPaymentMethodIcon(value: number | null | undefined): string {
  const option = getPaymentMethodOption(value)
  return option?.icon || 'QuestionFilled'
}

/**
 * 根据支付方式值获取对应的颜色
 *
 * @param value 支付方式值
 * @returns 颜色值
 *
 * @example
 * getPaymentMethodColor(1) // 返回 "#409EFF"
 * getPaymentMethodColor(5) // 返回 "#07C160"
 */
export function getPaymentMethodColor(value: number | null | undefined): string {
  const option = getPaymentMethodOption(value)
  return option?.color || '#909399'
}

/**
 * 验证支付方式值是否有效
 *
 * @param value 支付方式值
 * @returns 是否为有效的支付方式
 *
 * @example
 * isValidPaymentMethod(1) // 返回 true
 * isValidPaymentMethod(999) // 返回 false
 */
export function isValidPaymentMethod(value: any): boolean {
  return Object.values(PAYMENT_METHODS).includes(value)
}

/**
 * 获取默认支付方式（银行转账）
 *
 * @returns 默认支付方式值
 */
export function getDefaultPaymentMethod(): number {
  return PAYMENT_METHODS.BANK_TRANSFER
}

/**
 * 根据支付方式生成标签组件的属性
 * 用于 ElTag 等标签组件
 *
 * @param value 支付方式值
 * @returns 标签属性对象
 *
 * @example
 * getPaymentMethodTagProps(1) // 返回 { type: "primary", effect: "light" }
 * getPaymentMethodTagProps(2) // 返回 { type: "success", effect: "light" }
 */
export function getPaymentMethodTagProps(value: number | null | undefined): {
  type: string
  effect: string
} {
  const typeMap: Record<number, string> = {
    [PAYMENT_METHODS.BANK_TRANSFER]: 'primary',
    [PAYMENT_METHODS.CASH]: 'success',
    [PAYMENT_METHODS.CHECK]: 'warning',
    [PAYMENT_METHODS.ALIPAY]: 'primary',
    [PAYMENT_METHODS.WECHAT]: 'success',
    [PAYMENT_METHODS.OTHER]: 'info'
  }

  return {
    type: typeMap[value as number] || 'info',
    effect: 'light'
  }
}

/**
 * 支付方式分组（用于复杂的选择场景）
 */
export const PAYMENT_METHOD_GROUPS = [
  {
    label: '电子支付',
    options: [
      PAYMENT_METHOD_OPTIONS.find((item) => item.value === PAYMENT_METHODS.BANK_TRANSFER)!,
      PAYMENT_METHOD_OPTIONS.find((item) => item.value === PAYMENT_METHODS.ALIPAY)!,
      PAYMENT_METHOD_OPTIONS.find((item) => item.value === PAYMENT_METHODS.WECHAT)!
    ]
  },
  {
    label: '传统支付',
    options: [
      PAYMENT_METHOD_OPTIONS.find((item) => item.value === PAYMENT_METHODS.CASH)!,
      PAYMENT_METHOD_OPTIONS.find((item) => item.value === PAYMENT_METHODS.CHECK)!
    ]
  },
  {
    label: '其他方式',
    options: [PAYMENT_METHOD_OPTIONS.find((item) => item.value === PAYMENT_METHODS.OTHER)!]
  }
]

/**
 * 获取支付方式的简化选项（仅包含 label 和 value）
 * 用于简单的选择器组件
 *
 * @returns 简化的选项数组
 */
export function getSimplePaymentMethodOptions(): Array<{ label: string; value: number }> {
  return PAYMENT_METHOD_OPTIONS.map((item) => ({
    label: item.label,
    value: item.value
  }))
}

/**
 * 将字符串类型的支付方式转换为数字类型
 * 用于兼容旧数据格式
 *
 * @param value 支付方式值（可能是字符串或数字）
 * @returns 数字类型的支付方式值
 *
 * @example
 * convertPaymentMethodToNumber("银行转账") // 返回 1
 * convertPaymentMethodToNumber("现金支付") // 返回 2
 * convertPaymentMethodToNumber(1) // 返回 1
 * convertPaymentMethodToNumber("未知方式") // 返回 1 (默认值)
 */
export function convertPaymentMethodToNumber(value: any): number {
  // 如果已经是数字且有效，直接返回
  if (typeof value === 'number' && isValidPaymentMethod(value)) {
    return value
  }

  // 如果是字符串，尝试转换
  if (typeof value === 'string') {
    // 先尝试解析为数字
    const numValue = parseInt(value)
    if (!isNaN(numValue) && isValidPaymentMethod(numValue)) {
      return numValue
    }

    // 根据字符串内容匹配
    const stringMap: Record<string, number> = {
      '银行转账': PAYMENT_METHODS.BANK_TRANSFER,
      '现金支付': PAYMENT_METHODS.CASH,
      '现金': PAYMENT_METHODS.CASH,
      '支票': PAYMENT_METHODS.CHECK,
      '支票支付': PAYMENT_METHODS.CHECK,
      '支付宝': PAYMENT_METHODS.ALIPAY,
      '微信': PAYMENT_METHODS.WECHAT,
      '微信支付': PAYMENT_METHODS.WECHAT,
      '其他': PAYMENT_METHODS.OTHER,
      '其他方式': PAYMENT_METHODS.OTHER,
      // 英文别名
      'bank_transfer': PAYMENT_METHODS.BANK_TRANSFER,
      'transfer': PAYMENT_METHODS.BANK_TRANSFER,
      'cash': PAYMENT_METHODS.CASH,
      'check': PAYMENT_METHODS.CHECK,
      'alipay': PAYMENT_METHODS.ALIPAY,
      'wechat': PAYMENT_METHODS.WECHAT,
      'other': PAYMENT_METHODS.OTHER
    }

    const lowerValue = value.toLowerCase().trim()
    if (stringMap[value] !== undefined) {
      return stringMap[value]
    }
    if (stringMap[lowerValue] !== undefined) {
      return stringMap[lowerValue]
    }
  }

  // 默认返回银行转账
  return PAYMENT_METHODS.BANK_TRANSFER
}

/**
 * 兼容版本的获取支付方式标签函数
 * 支持字符串和数字类型的输入
 *
 * @param value 支付方式值（字符串或数字）
 * @returns 支付方式标签文本
 */
export function getPaymentMethodLabelCompat(value: any): string {
  const numValue = convertPaymentMethodToNumber(value)
  return getPaymentMethodLabel(numValue)
}

/**
 * 兼容版本的获取支付方式标签属性函数
 * 支持字符串和数字类型的输入
 *
 * @param value 支付方式值（字符串或数字）
 * @returns 标签属性对象
 */
export function getPaymentMethodTagPropsCompat(value: any): {
  type: string
  effect: string
} {
  const numValue = convertPaymentMethodToNumber(value)
  return getPaymentMethodTagProps(numValue)
}
