<template>
  <div class="task-management-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon>
            <Tickets />
          </el-icon>
          任务管理
        </h1>
        <span class="page-subtitle">管理和跟踪您的任务进度</span>
      </div>
      <div class="header-right">
        <el-button v-if="hasAuth('project:project_task:add')" type="primary" @click="handleAddTask">
          <el-icon>
            <Plus />
          </el-icon>
          新建任务
        </el-button>
      </div>
    </div>

    <!-- 任务分组标签 -->
    <div class="task-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="我的任务" name="my">
          <template #label>
            <span class="tab-label">
              <el-icon><User /></el-icon>
              我的任务
              <!--              <span class="count">({{ myTasks.length }})</span>-->
            </span>
          </template>
        </el-tab-pane>
        <el-tab-pane label="全部任务" name="all">
          <template #label>
            <span class="tab-label">
              <el-icon><List /></el-icon>
              全部任务
              <!--              <span class="count">({{ allTasks.length }})</span>-->
            </span>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="search-filter-bar">
      <div class="search-section">
        <el-input
          v-model="searchForm.title"
          placeholder="搜索任务标题..."
          clearable
          @change="handleSearch"
          class="search-input"
        >
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>

      <div class="filter-section">
        <el-select
          v-model="searchForm.status"
          placeholder="任务状态"
          clearable
          @change="handleSearch"
          class="filter-select"
        >
          <el-option label="待办" value="1" />
          <el-option label="进行中" value="2" />
          <el-option label="已完成" value="3" />
          <el-option label="已关闭" value="4" />
        </el-select>

        <el-select
          v-model="searchForm.priority"
          placeholder="优先级"
          clearable
          @change="handleSearch"
          class="filter-select"
        >
          <el-option label="低" value="1" />
          <el-option label="中" value="2" />
          <el-option label="高" value="3" />
          <el-option label="紧急" value="4" />
        </el-select>

        <el-select
          v-model="searchForm.project_id"
          placeholder="所属项目"
          clearable
          @change="handleSearch"
          class="filter-select"
        >
          <el-option
            v-for="project in projectList"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          />
        </el-select>
      </div>
    </div>

    <!-- 高优先级任务提醒 -->
    <div v-if="highPriorityTasks.length > 0" class="priority-tasks">
      <div class="priority-header">
        <el-icon>
          <Warning />
        </el-icon>
        <span>高优先级任务 ({{ highPriorityTasks.length }})</span>
      </div>
      <div class="priority-list">
        <div
          v-for="task in highPriorityTasks"
          :key="task.id"
          class="priority-task-item"
          @click="handleTaskClick(task.id)"
        >
          <div class="task-info">
            <span class="task-title">{{ task.name || task.title }}</span>
            <span class="task-project">{{ task.project_name || '未分配项目' }}</span>
          </div>
          <div class="task-meta">
            <el-tag :type="getPriorityType(task.priority)" size="small">
              {{ getPriorityText(task.priority) }}
            </el-tag>
            <span class="task-deadline">{{ formatDate(task.due_date) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-section">
      <div class="task-table">
        <el-table :data="currentTasks" stripe>
          <el-table-column prop="name" label="任务名称" min-width="200">
            <template #default="{ row }">
              <div class="task-name-cell">
                <el-tag :type="getPriorityType(row.priority)" size="small" class="priority-tag">
                  {{ getPriorityText(row.priority) }}
                </el-tag>
                <span class="task-title">{{ row.name || row.title }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="project_name" label="所属项目" width="150">
            <template #default="{ row }">
              <span>{{ row.project_name || '未分配' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="owner_name" label="执行人" width="120">
            <template #default="{ row }">
              <span>{{ row.assignee_name || '未分配' }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="due_date" label="截止时间" width="120">
            <template #default="{ row }">
              <span :class="{ overdue: isOverdue(row.due_date) }">
                {{ formatDate(row.due_date) }}
              </span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="240" fixed="right" align="center">
            <template #default="{ row }">
              <div class="operation-buttons">
                <ArtButtonTable
                  v-if="hasAuth('project:project_task:detail')"
                  text="详情"
                  @click="handleTaskClick(row.id)"
                />
                <ArtButtonTable
                  v-if="hasAuth('project:project_task:edit')"
                  text="编辑"
                  type="edit"
                  @click="handleEditTask(row.id)"
                />
                <ArtButtonTable
                  v-if="hasAuth('project:project_task:delete')"
                  text="删除"
                  type="delete"
                  @click="handleDeleteTask(row)"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 任务详情弹窗 -->
    <TaskDetail
      v-model:visible="taskDetailVisible"
      :task-id="currentTaskId"
      @task-updated="handleTaskUpdated"
    />

    <!-- 新建/编辑任务弹窗 -->
    <TaskForm
      v-model:visible="taskFormVisible"
      :task-id="editTaskId"
      @success="handleTaskFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Tickets, Plus, User, List, Search, Warning } from '@element-plus/icons-vue'
  import { TaskApi, ProjectApi } from '@/api/project/projectApi'
  import { formatDate, isOverdue } from '@/utils/date'
  import TaskDetail from './components/TaskDetail.vue'
  import TaskForm from './components/TaskForm.vue'
  import ArtButtonTable from '@/components/core/forms/ArtButtonTable.vue'
  import { useAuth } from '@/composables/useAuth'
  import { ApiStatus } from '@/utils/http/status'

  // 权限验证
  const { hasAuth } = useAuth()

  // 响应式数据
  const activeTab = ref('my')
  const myTasks = ref<any[]>([])
  const allTasks = ref<any[]>([])
  const projectList = ref<any[]>([])
  const taskDetailVisible = ref(false)
  const taskFormVisible = ref(false)
  const currentTaskId = ref<number | null>(null)
  const editTaskId = ref<number | null>(null)

  // 搜索表单
  const searchForm = reactive({
    title: '',
    status: '',
    priority: '',
    project_id: ''
  })

  // 分页数据
  const pagination = reactive({
    page: 1,
    size: 20,
    total: 0
  })

  // 计算属性
  const currentTasks = computed(() => {
    return activeTab.value === 'my' ? myTasks.value : allTasks.value
  })

  const highPriorityTasks = computed(() => {
    return currentTasks.value.filter(
      (task) => task.priority >= 3 && task.status !== 3 && task.status !== 4
    )
  })

  // 方法
  const handleTabChange = (tabName: string) => {
    console.log('TaskManagement: 切换tab', tabName)
    activeTab.value = tabName
    // 重置分页和搜索条件
    pagination.page = 1
    searchForm.project_id = '' // 清空项目筛选

    // 重新加载项目列表和任务列表
    loadProjectList().then(() => {
      if (tabName === 'my') {
        loadMyTasks()
      } else {
        loadAllTasks()
      }
    })
  }

  const handleSearch = () => {
    console.log('TaskManagement: 执行搜索', searchForm)
    // 重置分页
    pagination.page = 1
    if (activeTab.value === 'my') {
      loadMyTasks()
    } else {
      loadAllTasks()
    }
  }

  const handleAddTask = () => {
    editTaskId.value = null
    taskFormVisible.value = true
  }

  const handleTaskClick = (taskId: number) => {
    currentTaskId.value = taskId
    taskDetailVisible.value = true
  }

  const handleEditTask = async (taskId: number) => {
    try {
      console.log('TaskManagement: 开始编辑任务', taskId)
      // 先请求详情数据
      const response = await TaskApi.detail(taskId)

      if (response.code === ApiStatus.success) {
        console.log('TaskManagement: 获取详情成功', response.data)

        // 数据加载完成后，设置ID并打开弹窗
        editTaskId.value = taskId
        taskFormVisible.value = true
      }
    } catch (error) {
      console.error('TaskManagement: 获取任务详情失败', error)
    }
  }

  const handleDeleteTask = async (task: any) => {
    try {
      await ElMessageBox.confirm(`确定要删除任务"${task.name || task.title}"吗？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      const res = await TaskApi.delete(task.id)
      if (res.code === ApiStatus.success) {
        ElMessage.success('删除成功')

        if (activeTab.value === 'my') {
          await loadMyTasks()
        } else {
          await loadAllTasks()
        }
      }
    } catch (error) {}
  }

  const handleTaskUpdated = () => {
    if (activeTab.value === 'my') {
      loadMyTasks()
    } else {
      loadAllTasks()
    }
  }

  const handleTaskFormSuccess = () => {
    taskFormVisible.value = false
    if (activeTab.value === 'my') {
      loadMyTasks()
    } else {
      loadAllTasks()
    }
  }

  const handleSizeChange = (size: number) => {
    pagination.size = size
    loadAllTasks()
  }

  const handleCurrentChange = (page: number) => {
    pagination.page = page
    loadAllTasks()
  }

  // 状态和优先级相关方法
  const getStatusType = (status: number): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
    const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
      1: 'info', // 待办
      2: 'primary', // 进行中
      3: 'success', // 已完成
      4: 'danger' // 已关闭
    }
    return typeMap[status] || 'info'
  }

  const getStatusText = (status: number) => {
    const textMap: Record<number, string> = {
      1: '待办',
      2: '进行中',
      3: '已完成',
      4: '已关闭'
    }
    return textMap[status] || '未知'
  }

  const getPriorityType = (
    priority: number
  ): 'primary' | 'success' | 'warning' | 'info' | 'danger' => {
    const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'info' | 'danger'> = {
      1: 'info', // 低
      2: 'primary', // 中
      3: 'warning', // 高
      4: 'danger' // 紧急
    }
    return typeMap[priority] || 'info'
  }

  const getPriorityText = (priority: number) => {
    const textMap: Record<number, string> = {
      1: '低',
      2: '中',
      3: '高',
      4: '紧急'
    }
    return textMap[priority] || '未知'
  }

  // 数据加载
  const loadMyTasks = async () => {
    try {
      console.log('TaskManagement: 加载我的任务', searchForm)
      const params: any = {
        ...searchForm,
        // 清理空值参数
        title: searchForm.title || undefined,
        status: searchForm.status || undefined,
        priority: searchForm.priority || undefined,
        project_id: searchForm.project_id || undefined
      }
      // 移除undefined值
      Object.keys(params).forEach((key) => {
        if (params[key] === undefined || params[key] === '') {
          delete params[key]
        }
      })

      const response = await TaskApi.myTasks(params)
      if (response.code !== ApiStatus.success) {
        return
      }
      console.log('TaskManagement: 我的任务响应', response)

      // 根据后端返回的数据结构处理
      if (response.data && response.data.list) {
        myTasks.value = response.data.list
      } else if (Array.isArray(response.data)) {
        myTasks.value = response.data
      } else {
        myTasks.value = []
      }

      console.log('TaskManagement: 我的任务数据', myTasks.value)
    } catch (error) {
      console.error('加载我的任务失败:', error)
      myTasks.value = []
    }
  }

  const loadAllTasks = async () => {
    try {
      console.log('TaskManagement: 加载全部任务', searchForm, pagination)
      const params: any = {
        ...searchForm,
        page: pagination.page,
        limit: pagination.size, // 使用limit而不是size
        // 清理空值参数
        title: searchForm.title || undefined,
        status: searchForm.status || undefined,
        priority: searchForm.priority || undefined,
        project_id: searchForm.project_id || undefined
      }
      // 移除undefined值
      Object.keys(params).forEach((key) => {
        if (params[key] === undefined || params[key] === '') {
          delete params[key]
        }
      })

      const response = await TaskApi.list(params)
      if (response.code !== ApiStatus.success) {
        return
      }
      console.log('TaskManagement: 全部任务响应', response)

      // 根据后端返回的数据结构处理
      if (response.data && response.data.list) {
        allTasks.value = response.data.list
        pagination.total = response.data.total || 0
      } else if (Array.isArray(response.data)) {
        allTasks.value = response.data
        pagination.total = response.data.length
      } else {
        allTasks.value = []
        pagination.total = 0
      }

      console.log('TaskManagement: 全部任务数据', allTasks.value)
    } catch (error) {
      console.error('加载任务列表失败:', error)
      allTasks.value = []
      pagination.total = 0
    }
  }

  const loadProjectList = async () => {
    try {
      // 根据当前tab加载不同的项目列表
      console.log('TaskManagement: 加载项目列表', activeTab.value)

      let response
      if (activeTab.value === 'my') {
        // 我的任务：只加载我参与的项目
        response = await ProjectApi.myProjects({ page: 1, size: 100 })
        projectList.value = Array.isArray(response.data) ? response.data : response.data?.list || []
      } else {
        // 全部任务：加载所有有权限查看的项目
        response = await ProjectApi.list({ page: 1, size: 100 })
        projectList.value = response.data?.list || []
      }

      console.log('TaskManagement: 项目列表加载完成', projectList.value.length)
    } catch (error) {
      console.error('加载项目列表失败:', error)
      projectList.value = []
    }
  }

  // 初始化
  onMounted(() => {
    console.log('TaskManagement: 组件初始化')
    // 使用立即执行的异步函数避免生命周期钩子警告
    const initData = async () => {
      try {
        // 并行加载数据
        await Promise.all([loadMyTasks(), loadProjectList()])
        console.log('TaskManagement: 初始化完成', {
          myTasks: myTasks.value.length,
          allTasks: allTasks.value.length,
          projects: projectList.value.length
        })
      } catch (error) {
        console.error('TaskManagement: 初始化失败', error)
      }
    }
    initData()
  })
</script>

<style scoped lang="scss">
  .task-management-container {
    padding: 24px;
    background-color: #f5f7fa;
    min-height: 100vh;

    // 黑暗模式适配
    html.dark & {
      background-color: var(--art-bg-color);
    }
  }

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .header-left {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 24px;
        font-weight: 600;
        color: #1f2329;
        margin: 0 0 4px 0;

        // 黑暗模式适配
        html.dark & {
          color: var(--art-text-gray-900);
        }
      }

      .page-subtitle {
        color: #86909c;
        font-size: 14px;

        // 黑暗模式适配
        html.dark & {
          color: var(--art-text-gray-600);
        }
      }
    }
  }

  .task-tabs {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;

    // 黑暗模式适配
    html.dark & {
      background: var(--art-main-bg-color);
      box-shadow: var(--art-root-card-box-shadow);
      border: 1px solid var(--art-root-card-border-color);
    }

    :deep(.el-tabs__header) {
      margin: 0;
      padding: 0 20px;
      margin-bottom: 16px; /* 增加tabs头部与内容的间距 */
    }

    :deep(.el-tabs__nav-wrap::after) {
      display: none;
    }

    :deep(.el-tabs__content) {
      padding: 0 20px 20px; /* 增加内容区域的内边距 */
    }

    :deep(.el-tab-pane) {
      padding-top: 8px; /* 增加标签页内容的顶部间距 */
    }

    .tab-label {
      display: flex;
      align-items: center;
      gap: 6px;

      .count {
        color: #86909c;
        font-weight: normal;

        // 黑暗模式适配
        html.dark & {
          color: var(--art-text-gray-600);
        }
      }
    }
  }

  .search-filter-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    // 黑暗模式适配
    html.dark & {
      background: var(--art-main-bg-color);
      box-shadow: var(--art-root-card-box-shadow);
      border: 1px solid var(--art-root-card-border-color);
    }

    .search-section {
      .search-input {
        width: 300px;
      }
    }

    .filter-section {
      display: flex;
      align-items: center;
      gap: 12px;

      .filter-select {
        width: 150px;
      }
    }
  }

  .priority-tasks {
    background: #fff7e6;
    border: 1px solid #ffd591;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;

    // 黑暗模式适配
    html.dark & {
      background: rgba(var(--art-warning), 0.1);
      border-color: rgba(var(--art-warning), 0.3);
    }

    .priority-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #fa8c16;
      margin-bottom: 12px;

      // 黑暗模式适配
      html.dark & {
        color: rgb(var(--art-warning));
      }
    }

    .priority-list {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .priority-task-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: white;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s;

        // 黑暗模式适配
        html.dark & {
          background: var(--art-main-bg-color);
          border: 1px solid var(--art-border-color);
        }

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .task-info {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .task-title {
            font-weight: 500;
            color: #1f2329;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-900);
            }
          }

          .task-project {
            font-size: 12px;
            color: #86909c;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-600);
            }
          }
        }

        .task-meta {
          display: flex;
          align-items: center;
          gap: 12px;

          .task-deadline {
            font-size: 12px;
            color: #86909c;

            // 黑暗模式适配
            html.dark & {
              color: var(--art-text-gray-600);
            }
          }
        }
      }
    }
  }

  .task-list-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 24px;
    margin-bottom: 24px;

    // 黑暗模式适配
    html.dark & {
      background: var(--art-main-bg-color);
      box-shadow: var(--art-root-card-box-shadow);
      border: 1px solid var(--art-root-card-border-color);
    }

    .task-name-cell {
      display: flex;
      align-items: center;
      gap: 8px;

      .priority-tag {
        flex-shrink: 0;
      }

      .task-title {
        font-weight: 500;
      }
    }

    .overdue {
      color: #ff4d4f;

      // 黑暗模式适配
      html.dark & {
        color: rgb(var(--art-danger));
      }
    }

    .operation-buttons {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 8px;
      flex-wrap: wrap;
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: center;
  }

  :deep(.el-table) {
    .el-table__row {
      cursor: pointer;

      &:hover {
        background-color: #f5f7fa;

        // 黑暗模式适配
        html.dark & {
          background-color: var(--art-hoverColor);
        }
      }
    }
  }
</style>
