-- 企业审批系统数据表设计（CRUD生成器兼容版本 - 第二部分）
-- 报销、出差、外出申请表
-- 设计时间：2025-07-25

-- 报销申请表
-- 生成器命令: php think generator:crud finance_expense_reimbursement --module=finance --frontend --overwrite
DROP TABLE IF EXISTS `finance_expense_reimbursement`;
CREATE TABLE `finance_expense_reimbursement`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
    `reimbursement_no`     varchar(50)         NOT NULL DEFAULT '' COMMENT '报销单号 | @required @max:50 @s=like @e @exp @imp',
    `expense_type`         tinyint(1)          NOT NULL DEFAULT 1 COMMENT '报销类型:1=差旅费,2=交通费,3=餐费,4=住宿费,5=办公费,6=通讯费,7=其他 | @required @s=eq @e @exp @imp @fmt=tag',
    `total_amount`         decimal(10, 2)      NOT NULL DEFAULT 0.00 COMMENT '报销总金额 | @required @number @component:currency @min:0.01 @s=between @e @exp @imp',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件（发票、收据等） | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_reimbursement_no` (`reimbursement_no`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报销申请表 @module:finance @exp:true @imp:true';

-- 报销明细表
-- 生成器命令: php think generator:crud hr_finance_expense_item --module=finance --frontend --overwrite
DROP TABLE IF EXISTS `finance_expense_item`;
CREATE TABLE `finance_expense_item`
(
    `id`               bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '报销明细ID | @h',
    `tenant_id`        bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',
    `reimbursement_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '报销申请ID | @required @s=eq @e @exp @imp',
    `description`      text COMMENT '费用说明 | @max:255 @s=like @e @exp @imp @form:textarea',

    -- 系统字段
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_reimbursement_id` (`reimbursement_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报销明细表 @module:finance @exp:true @imp:true';

-- 出差申请表（优化版本，支持多行程明细）
-- 生成器命令: php think generator:crud hr_business_trip --module=hr --frontend --overwrite
DROP TABLE IF EXISTS `hr_business_trip`;
CREATE TABLE `hr_business_trip`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '出差申请ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
#     `trip_type`            tinyint(1)          NOT NULL DEFAULT 1 COMMENT '出差类型:1=国内,2=国际 | @required @s=eq @e @exp @imp @fmt=tag',
#     `trip_mode`            tinyint(1)          NOT NULL DEFAULT 1 COMMENT '单程往返:1=往返,2=单程 | @required @s=eq @e @exp @imp @fmt=tag',
    `duration`             decimal(10, 1)      NOT NULL DEFAULT 0.0 COMMENT '出差总时长(天) | @number @s=between @e @exp @imp',
    `purpose`              text COMMENT '出差事由 | @required @max:500 @s=like @e @exp @imp @form:textarea',
    -- 其他信息
    `companions`           text COMMENT '同行人 | @e @exp @imp @form:textarea',
#     `emergency_contact`    varchar(50)                  DEFAULT '' COMMENT '紧急联系人 | @max:50 @s=like @e @exp @imp',
#     `emergency_phone`      varchar(20)                  DEFAULT '' COMMENT '紧急联系电话 | @max:20 @s=like @e @exp @imp',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='出差申请表 @module:hr @exp:true @imp:true';

-- 出差行程明细表
-- 生成器命令: php think generator:crud hr_business_trip_itinerary --module=hr --frontend --overwrite
DROP TABLE IF EXISTS `hr_business_trip_itinerary`;
CREATE TABLE `hr_business_trip_itinerary`
(
    `id`                    bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '行程明细ID | @h',
    `tenant_id`             bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',
    `business_trip_id`      bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '出差申请ID | @required @s=eq @e @exp @imp',

    -- 行程核心信息
    `departure_city`        varchar(50)         NOT NULL DEFAULT '' COMMENT '出发城市 | @required @max:50 @s=like @e @exp @imp',
    `departure_city_code`   varchar(50)         NOT NULL DEFAULT '' COMMENT '出发城市CODE',
    `destination_city`      varchar(50)         NOT NULL DEFAULT '' COMMENT '目的地城市 | @required @max:50 @s=like @e @exp @imp',
    `destination_city_code` varchar(50)         NOT NULL DEFAULT '' COMMENT '出发城市CODE',
    `trip_mode`             tinyint(1)          NOT NULL DEFAULT 1 COMMENT '单程往返:1=往返,2=单程 | @required @s=eq @e @exp @imp @fmt=tag',
    `start_time`            datetime            NOT NULL COMMENT '开始时间 | @required @s=datetime @e @exp @imp @fmt=datetime',
    `end_time`              datetime            NOT NULL COMMENT '结束时间 | @required @s=datetime @e @exp @imp @fmt=datetime',
    `duration`              decimal(5, 1)       NOT NULL DEFAULT 0.0 COMMENT '时长(天) | @number @s=between @e @exp @imp',

    -- 交通信息
    `transport_type`        tinyint(1)          NOT NULL DEFAULT 1 COMMENT '交通工具:1=飞机,2=高铁,3=火车,4=汽车,5=其他 | @required @s=eq @e @exp @imp @fmt=tag',
#     `transport_no`     varchar(50)                  DEFAULT '' COMMENT '交通工具班次号 | @max:50 @s=like @e @exp @imp',
#     `departure_time`   datetime                     DEFAULT NULL COMMENT '出发时间 | @s=datetime @e @exp @imp @fmt=datetime',
#     `arrival_time`     datetime                     DEFAULT NULL COMMENT '到达时间 | @s=datetime @e @exp @imp @fmt=datetime',

    -- 住宿信息
    /*`hotel_name`       varchar(100)                 DEFAULT '' COMMENT '酒店名称 | @max:100 @s=like @e @exp @imp',
    `hotel_address`    varchar(200)                 DEFAULT '' COMMENT '酒店地址 | @max:200 @s=like @e @exp @imp',
    `check_in_date`    date                         DEFAULT NULL COMMENT '入住日期 | @s=date @e @exp @imp @fmt=date',
    `check_out_date`   date                         DEFAULT NULL COMMENT '退房日期 | @s=date @e @exp @imp @fmt=date',*/

    -- 业务信息

    /*`meeting_info`     text COMMENT '会议安排 | @e @exp @imp @form:textarea',
    `contact_person`   varchar(50)                  DEFAULT '' COMMENT '联系人 | @max:50 @s=like @e @exp @imp',
    `contact_phone`    varchar(20)                  DEFAULT '' COMMENT '联系电话 | @max:20 @s=like @e @exp @imp',*/

    -- 备注信息
    `remark`                text COMMENT '行程备注 | @e @exp @imp @form:textarea',
    `attachment`            text COMMENT '行程相关附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `created_at`            datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`            datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`            datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_business_trip_id` (`business_trip_id`),
    KEY `idx_sequence_no` (`business_trip_id`),
    KEY `idx_itinerary_time` (`start_time`, `end_time`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='出差行程明细表 @module:hr @exp:true @imp:true';

-- 外出申请表
-- 生成器命令: php think generator:crud hr_outing --module=hr --frontend --overwrite
DROP TABLE IF EXISTS `hr_outing`;
CREATE TABLE `hr_outing`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '外出申请ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
    `start_time`           datetime            NOT NULL COMMENT '开始时间 | @required @s=datetime @e @exp @imp @fmt=datetime',
    `end_time`             datetime            NOT NULL COMMENT '结束时间 | @required @s=datetime @e @exp @imp @fmt=datetime',
    `duration`             decimal(5, 1)       NOT NULL DEFAULT 0.0 COMMENT '外出时长(小时) | @number @s=between @e @exp @imp',
    `purpose`              text COMMENT '外出事由 | @required @max:500 @s=like @e @exp @imp @form:textarea',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_outing_time` (`start_time`, `end_time`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='外出申请表 @module:hr @exp:true @imp:true';

-- 办公采购申请表
-- 生成器命令: php think generator:crud office_procurement --module=office --frontend --overwrite
DROP TABLE IF EXISTS `office_procurement`;
CREATE TABLE `office_procurement`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '办公采购ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
    `procurement_type`     tinyint(1)                   DEFAULT NULL COMMENT '采购类型:1=生产原料,2=办公用品,3=其他 | @s=eq @e @exp @imp @fmt=tag',
    `delivery_date`        date                NOT NULL COMMENT '交付日期 | @required @s=date @e @exp @imp @fmt=date',
    `item_name`            varchar(200)        NOT NULL DEFAULT '' COMMENT '物品名称 | @required @max:200 @s=like @e @exp @imp',
    `supplier_name`        varchar(200)        NOT NULL DEFAULT '' COMMENT '采购来源单位名称 | @required @max:200 @s=like @e @exp @imp',
    `unit_price`           decimal(10, 2)      NOT NULL DEFAULT 0.00 COMMENT '单价(元) | @required @number @component:currency @min:0.01 @s=between @e @exp @imp',
    `quantity`             decimal(10, 2)      NOT NULL DEFAULT 0.00 COMMENT '数量 | @required @number @min:1 @s=between @e @exp @imp',
    `payment_amount`       decimal(10, 2)      NOT NULL DEFAULT 0.00 COMMENT '付款金额 | @required @number @component:currency @min:0.01 @s=between @e @exp @imp',
    `payment_amount_words` varchar(500)        NOT NULL DEFAULT '' COMMENT '付款金额大写 | @required @max:500 @readonly @e @exp @imp',
    `payee_name`           varchar(100)        NOT NULL DEFAULT '' COMMENT '收款人 | @required @max:100 @s=like @e @exp @imp',
    `bank_name`            varchar(200)        NOT NULL DEFAULT '' COMMENT '开户行 | @required @max:200 @s=like @e @exp @imp',
    `bank_account`         varchar(50)         NOT NULL DEFAULT '' COMMENT '收款账号 | @required @max:50 @s=like @e @exp @imp',
    `payment_method`       tinyint(1)          NOT NULL DEFAULT 1 COMMENT '支付方式:1=银行转账,2=现金支付,3=支票,4=支付宝,5=微信,6=其他 | @required @s=eq @e @exp @imp @fmt=tag',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '图片附件 | @e @exp @imp @form:upload @component:image',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_delivery_date` (`delivery_date`),
    KEY `idx_item_name` (`item_name`),
    KEY `idx_supplier_name` (`supplier_name`),
    KEY `idx_payment_amount` (`payment_amount`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='办公采购申请表 @module:office @exp:true @imp:true';


