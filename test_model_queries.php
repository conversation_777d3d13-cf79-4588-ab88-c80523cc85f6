<?php
/**
 * 测试基于模型查询的统计方法
 * 验证真实数据查询效果
 */

$pdo = new PDO('mysql:host=*************;port=3306;dbname=www_bs_com;charset=utf8mb4', 'www_bs_com', 'PdadjMXmNy8Pn9tj');
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

echo "=== 测试基于模型查询的统计方法 ===\n\n";

/**
 * 模拟任务状态统计查询
 */
function testTaskStatusStats($pdo, $projectId) {
    echo "1. 任务状态分布统计:\n";
    
    $statusConfig = [
        1 => ['name' => '待办', 'color' => '#8C8C8C'],
        2 => ['name' => '进行中', 'color' => '#1664FF'], 
        3 => ['name' => '已完成', 'color' => '#00BC70'],
        4 => ['name' => '已关闭', 'color' => '#F54A45']
    ];
    
    $result = [];
    foreach ($statusConfig as $status => $config) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM project_task 
            WHERE project_id = ? AND status = ? AND deleted_at IS NULL
        ");
        $stmt->execute([$projectId, $status]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($count > 0) {
            $result[] = [
                'name' => $config['name'],
                'value' => (int)$count,
                'color' => $config['color']
            ];
        }
    }
    
    echo "   结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n\n";
    return $result;
}

/**
 * 模拟任务优先级统计查询
 */
function testTaskPriorityStats($pdo, $projectId) {
    echo "2. 任务优先级分布统计:\n";
    
    $priorityConfig = [1 => '低', 2 => '中', 3 => '高'];
    
    $result = [];
    foreach ($priorityConfig as $priority => $name) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM project_task 
            WHERE project_id = ? AND priority = ? AND deleted_at IS NULL
        ");
        $stmt->execute([$projectId, $priority]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($count > 0) {
            $result[] = [
                'name' => $name,
                'value' => (int)$count
            ];
        }
    }
    
    echo "   结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n\n";
    return $result;
}

/**
 * 模拟成员工作量统计查询
 */
function testMemberStats($pdo, $projectId) {
    echo "3. 成员工作量统计:\n";
    
    $stmt = $pdo->prepare("
        SELECT 
            sa.id,
            sa.real_name as name,
            sa.avatar,
            COUNT(pt.id) as task_count,
            COUNT(CASE WHEN pt.status = 3 THEN 1 END) as completed_tasks,
            CASE 
                WHEN COUNT(pt.id) > 0 THEN 
                    ROUND((COUNT(CASE WHEN pt.status = 3 THEN 1 END) * 100.0 / COUNT(pt.id)), 2)
                ELSE 0 
            END as completion_rate
        FROM project_member pm
        LEFT JOIN system_admin sa ON pm.user_id = sa.id
        LEFT JOIN project_task pt ON pm.user_id = pt.assignee_id 
            AND pt.project_id = pm.project_id 
            AND pt.deleted_at IS NULL
        WHERE pm.project_id = ? 
            AND pm.deleted_at IS NULL 
            AND sa.deleted_at IS NULL
        GROUP BY sa.id, sa.real_name, sa.avatar
        ORDER BY task_count DESC
    ");
    
    $stmt->execute([$projectId]);
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result = [];
    foreach ($members as $member) {
        $result[] = [
            'id' => (int)$member['id'],
            'name' => $member['name'],
            'avatar' => $member['avatar'],
            'task_count' => (int)$member['task_count'],
            'completion_rate' => (float)$member['completion_rate']
        ];
    }
    
    echo "   结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n\n";
    return $result;
}

/**
 * 模拟项目进度趋势查询
 */
function testProgressTrend($pdo, $projectId) {
    echo "4. 项目进度趋势:\n";
    
    $stmt = $pdo->prepare("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as total_tasks,
            COUNT(CASE WHEN status = 3 THEN 1 END) as completed_tasks,
            CASE 
                WHEN COUNT(*) > 0 THEN 
                    ROUND((COUNT(CASE WHEN status = 3 THEN 1 END) * 100.0 / COUNT(*)), 2)
                ELSE 0 
            END as progress
        FROM project_task 
        WHERE project_id = ? AND deleted_at IS NULL
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month
    ");
    
    $stmt->execute([$projectId]);
    $trends = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result = [];
    foreach ($trends as $trend) {
        $result[] = [
            'date' => $trend['month'],
            'progress' => (float)$trend['progress']
        ];
    }
    
    // 如果数据太少，补充最近6个月
    if (count($result) < 3) {
        $filledResult = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = date('Y-m', strtotime("-{$i} months"));
            
            $found = false;
            foreach ($result as $data) {
                if ($data['date'] == $month) {
                    $filledResult[] = $data;
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $filledResult[] = [
                    'date' => $month,
                    'progress' => 0.0
                ];
            }
        }
        $result = $filledResult;
    }
    
    echo "   结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n\n";
    return $result;
}

/**
 * 模拟最近活动查询
 */
function testRecentActivities($pdo, $projectId) {
    echo "5. 最近活动:\n";
    
    $stmt = $pdo->prepare("
        SELECT 
            sa.real_name as user_name,
            ptr.record_type,
            pt.title as task_title,
            ptr.created_at
        FROM project_task_record ptr
        LEFT JOIN project_task pt ON ptr.task_id = pt.id
        LEFT JOIN system_admin sa ON ptr.creator_id = sa.id
        WHERE pt.project_id = ? AND ptr.deleted_at IS NULL
        ORDER BY ptr.created_at DESC
        LIMIT 10
    ");
    
    $stmt->execute([$projectId]);
    $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $result = [];
    foreach ($activities as $activity) {
        $action = $activity['record_type'] == 'comment' ? '评论了任务' : '跟进了任务';
        $time = formatRelativeTime($activity['created_at']);
        
        $result[] = [
            'user' => $activity['user_name'],
            'action' => $action,
            'target' => $activity['task_title'],
            'time' => $time
        ];
    }
    
    echo "   结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n\n";
    return $result;
}

/**
 * 格式化相对时间
 */
function formatRelativeTime($datetime) {
    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;
    
    if ($diff < 60) {
        return '刚刚';
    } elseif ($diff < 3600) {
        return floor($diff / 60) . '分钟前';
    } elseif ($diff < 86400) {
        return floor($diff / 3600) . '小时前';
    } elseif ($diff < 2592000) {
        return floor($diff / 86400) . '天前';
    } else {
        return date('Y-m-d', $time);
    }
}

// 执行测试
$projectId = 1;

echo "测试项目ID: $projectId\n";
echo "=" . str_repeat("-", 50) . "\n\n";

try {
    $statusStats = testTaskStatusStats($pdo, $projectId);
    $priorityStats = testTaskPriorityStats($pdo, $projectId);
    $memberStats = testMemberStats($pdo, $projectId);
    $progressTrend = testProgressTrend($pdo, $projectId);
    $recentActivities = testRecentActivities($pdo, $projectId);
    
    echo "=== 测试完成 ===\n";
    echo "所有统计查询都基于真实数据，可以直接用于前端展示。\n";
    
    // 生成前端可用的完整数据格式
    $frontendData = [
        'taskStatus' => $statusStats,
        'taskPriority' => $priorityStats,
        'memberStats' => $memberStats,
        'progressTrend' => $progressTrend,
        'recentActivities' => $recentActivities
    ];
    
    echo "\n=== 前端完整数据格式 ===\n";
    echo json_encode($frontendData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    echo "测试失败: " . $e->getMessage() . "\n";
}
?>
