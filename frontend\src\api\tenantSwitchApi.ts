import request from '@/utils/http'
import { BaseResult } from '@/types/axios'

export interface TenantSwitchStatus {
  work_mode: string
  switched_tenant_id: number
  current_tenant_info: any
  permission_context: any
  switch_status: {
    is_switched: boolean
    original_tenant_id: number
    current_tenant_id: number
    switch_mode: string
    switch_time: number | null
  }
  request_info: {
    tenant_id: number
    is_tenant_switched: boolean
    original_tenant_id: number
    switch_mode: string
  }
}

export interface TenantInfo {
  id: number
  name: string
  code: string
  status: number
  created_time: string
}

export class TenantSwitchApi {
  /**
   * 获取当前切换状态
   */
  static getCurrentStatus() {
    return request.get<BaseResult<TenantSwitchStatus>>({
      url: '/system/tenant-switch/status'
    })
  }

  /**
   * 获取可切换的租户列表
   */
  static getAvailableTenants() {
    return request.get<BaseResult<TenantInfo[]>>({
      url: '/system/tenant-switch/tenants'
    })
  }

  /**
   * 切换到系统管理模式
   */
  static switchToSystemMode() {
    return request.post<BaseResult>({
      url: '/system/tenant-switch/system-mode'
    })
  }

  /**
   * 切换到租户管理模式
   */
  static switchToTenantMode(tenantId: number) {
    return request.post<BaseResult>({
      url: '/system/tenant-switch/tenant-mode',
      data: { tenant_id: tenantId }
    })
  }

  /**
   * 恢复到原始租户
   */
  static restoreOriginalTenant() {
    return request.post<BaseResult>({
      url: '/system/tenant-switch/restore'
    })
  }

  /**
   * 刷新Token有效期
   */
  static refreshToken() {
    return request.post<BaseResult>({
      url: '/system/tenant-switch/refresh-token'
    })
  }

  /**
   * 清除切换缓存
   */
  static clearCache(tenantId?: number) {
    return request.post<BaseResult>({
      url: '/system/tenant-switch/clear-cache',
      data: { tenant_id: tenantId || 0 }
    })
  }

  /**
   * 获取切换历史记录
   */
  static getSwitchHistory() {
    return request.get<BaseResult<any[]>>({
      url: '/system/tenant-switch/history'
    })
  }
}
