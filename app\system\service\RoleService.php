<?php
declare(strict_types=1);

namespace app\system\service;

use app\common\core\base\BaseService;
use app\common\core\constants\SystemConstant;
use app\common\exception\AuthException;
use app\common\exception\BusinessException;
use app\common\exception\ValidateFailedException;
use app\common\utils\DataPermissionCacheUtil;
use app\system\model\AdminModel;
use app\system\model\AdminRoleModel;
use app\system\model\RoleMenuModel;
use app\system\model\RoleModel;
use app\system\validate\RolesValidate;
use think\exception\ValidateException;
use think\facade\Cache;
use think\facade\Db;

/**
 * 角色服务类
 *
 */
class RoleService extends BaseService
{
	
	public function __construct()
	{
		$this->model = new RoleModel();
		parent::__construct();
	}
	
	/**
	 * 获取角色列表
	 *
	 * @param array $params 查询参数
	 * @return array
	 */
	public function getList(array $params): array
	{
		$where = [];
		
		// 角色名称搜索
		if (!empty($params['name'])) {
			$where[] = [
				'name',
				'like',
				'%' . $params['name'] . '%'
			];
		}
		
		// 状态搜索
		if (isset($params['status']) && $params['status'] !== '') {
			$where[] = [
				'status',
				'=',
				(int)$params['status']
			];
		}
		
		// 角色类型搜索
		if (isset($params['data_scope']) && $params['data_scope'] !== '') {
			$where[] = [
				'data_scope',
				'=',
				(int)$params['data_scope']
			];
		}
		
		// 排序规则
		$order = 'sort desc';
		
		$page  = $params['page'] ?? 1;
		$limit = $params['limit'] ?? 10;
		
		$total = $this->crudService->getCount($where);
		$list  = $this->crudService->getPageList($where, $order, (int)$page, (int)$limit);
		
		
		return [
			'list'  => $list,
			'total' => $total,
			'page'  => $page,
			'limit' => $limit,
		];
	}
	
	
	/**
	 * 获取角色详情
	 *
	 * @param int $id 角色ID
	 * @return array
	 */
	public function getDetail(int $id): array
	{
		$role = $this->crudService->getOne([
			[
				'id',
				'=',
				$id
			],
		], [
			'menu'
		]);
		
		if ($role->isEmpty()) {
			throw new BusinessException('角色不存在');
		}
		
		// 获取角色关联的菜单ID（用户实际选择的节点）
		$menuList = $role->menu;
		if (!$menuList->isEmpty()) {
			$role['menu_ids'] = $menuList->column('menu_id');
		}
		
		$role->hidden(['menu']);
		
		return $role->toArray();
	}
	
	/**
	 * 验证用户是否有权限分配指定的菜单
	 *
	 * @param int   $adminId 当前用户ID
	 * @param array $menuIds 要分配的菜单ID数组
	 * @throws BusinessException
	 * @throws AuthException
	 */
	private function validateMenuPermissions(int $adminId, array $menuIds): void
	{
		
		// 超级管理员跳过验证
		if (is_super_admin()) {
			return;
		}
		
		$permissionService = new PermissionService();
		$userMenuList      = $permissionService->getAdminPermissionByAdminId($adminId, true);
		
		if (empty($userMenuList)) {
			throw new BusinessException('您没有权限分配菜单');
		}
		
		$userMenuIds = $userMenuList->column('id');
		
		// 检查是否有超出权限范围的菜单
		$invalidMenuIds = array_diff($menuIds, $userMenuIds);
		if (!empty($invalidMenuIds)) {
			// 获取无权限菜单的名称，便于用户理解
			$invalidMenuNames = MenuService::getInstance()
			                               ->getModel()
			                               ->whereIn('id', $invalidMenuIds)
			                               ->column('title');
			
			$menuNamesStr = implode('、', $invalidMenuNames);
			throw new BusinessException("您没有权限分配以下菜单：{$menuNamesStr}，请联系管理员");
		}
	}
	
	/**
	 * 创建角色
	 *
	 * @param array $data 角色数据
	 * @return bool
	 */
	public function create(array $data): bool
	{
		try {
			validate(RolesValidate::class)->check($data);
		}
		catch (ValidateException $e) {
			throw new ValidateFailedException($e->getMessage());
		}
		
		Db::startTrans();
		try {
			
			$data_scope = $data['data_scope'];
			
			if ($data_scope == 5 && !empty($data['data_scope_dept_ids'])) {
				$data['data_scope_dept_ids'] = implode(',', $data['data_scope_dept_ids']);
			}
			else {
				$data['data_scope_dept_ids'] = '';
			}
			
			$res = $this->model->saveByUpdate($data);
			if (!$res) {
				throw new BusinessException('角色更新失败');
			}
			
			$id = $this->model->id;
			
			if (!empty($data['menu_ids'])) {
				
				$menuIds = $data['menu_ids'];
				
				$count = MenuService::getInstance()
				                    ->getCount([
					                    [
						                    'id',
						                    'in',
						                    $menuIds
					                    ]
				                    ]);
				if ($count != count($menuIds)) {
					throw new BusinessException('菜单权限有误');
				}
				
				// 新增：验证当前用户是否有权限分配这些菜单
				$currentAdminId = request()->adminId ?? 0;
				$this->validateMenuPermissions($currentAdminId, $menuIds);
				
				$roleMenuModel = new RoleMenuModel();
				
				foreach ($menuIds as $menuId) {
					$res = $roleMenuModel->saveByUpdate([
						'role_id' => $id,
						'menu_id' => $menuId,
					]);
					if (!$res) {
						throw new BusinessException('角色权限更新失败');
					}
				}
			}
			Db::commit();
		}
		catch (BusinessException $e) {
			Db::rollback();
			throw new BusinessException($e->getMessage());
		}
		
		// 清理所有相关缓存
		DataPermissionCacheUtil::clearAllDataPermissions();
		
		// 清理菜单权限缓存
		Cache::tag('menu')
		     ->clear();
		
		return true;
	}
	
	/**
	 * 更新角色
	 *
	 * @param int   $id       角色ID
	 * @param int   $tenantId 租户ID
	 * @param array $data     角色数据
	 * @return bool
	 * @throws \Exception
	 */
	public function update(int $id, int $tenantId, array $data): bool
	{
		try {
			validate(RolesValidate::class)->check($data);
		}
		catch (ValidateException $e) {
			throw new ValidateFailedException($e->getMessage());
		}
		
		$role = $this->crudService->getOne([
			[
				'id',
				'=',
				$id
			],
		], ['menu']);
		
		if ($role->isEmpty()) {
			throw new BusinessException('角色不存在');
		}
		Db::startTrans();
		try {
			
			// 处理数据范围部门ID
			if (!empty($data['data_scope_dept_ids'])) {
				$data['data_scope_dept_ids'] = implode(',', $data['data_scope_dept_ids']);
			}
			
			if (isset($data['menu_ids'])) {

				$menuIds = $data['menu_ids'];

				// 先删除现有的角色菜单关联
				$roleMenuModel = new RoleMenuModel();

				$list = $roleMenuModel::field('id,menu_id')
				                      ->where('role_id', $id)
				                      ->select();

				$res = $list->delete();
				if (!$res) {
					throw new BusinessException('角色权限更新失败');
				}

				// 如果有新的菜单权限，则添加
				if (!empty($menuIds)) {
					$count = MenuService::getInstance()->crudService->getCount([
						[
							'id',
							'in',
							$menuIds
						]
					]);
					/*if ($count != count($menuIds)) {
						throw new BusinessException('菜单权限有误');
					}*/

					// 新增：验证当前用户是否有权限分配这些菜单
					$currentAdminId = request()->adminId ?? 0;
					$this->validateMenuPermissions($currentAdminId, $menuIds);

					foreach ($menuIds as $menuId) {
						(new RoleMenuModel())->saveByCreate([
							'role_id' => $id,
							'menu_id' => $menuId,
						]);
					}
				}

			}
			
			$res = $role->saveByUpdate($data);
			if (!$res) {
				throw new BusinessException('角色更新失败');
			}
			Db::commit();
		}
		catch (BusinessException $e) {
			Db::rollback();
			throw new BusinessException($e->getMessage());
		}
		
		// 清理所有相关缓存
		DataPermissionCacheUtil::clearAllDataPermissions();
		
		// 清理菜单权限缓存
		Cache::tag('menu')
		     ->clear();
		
		return true;
	}
	
	/**
	 * 删除角色
	 *
	 * @param int $id 角色ID
	 * @return bool
	 */
	public function delete(int $id): bool
	{
		$roleInfo = $this->crudService->getOne([
			'id' => $id,
		]);
		if ($roleInfo->isEmpty()) {
			throw new BusinessException('角色不存在');
		}
		
		DataPermissionCacheUtil::clearAllDataPermissions();
		
		return $roleInfo->delete();
	}
	
	/**
	 * 获取用户拥有的角色ID列表
	 *
	 * @param int $adminId 用户ID
	 * @return array 角色ID数组
	 */
	public function getUserRoleIds(int $adminId): array
	{
		return AdminRoleModel::where('admin_id', $adminId)
		                     ->column('role_id');
	}
	
	/**
	 * 获取用户拥有的角色列表
	 *
	 * @param int $adminId 用户ID
	 * @return array 角色数组
	 */
	public function getUserRoles(int $adminId): array
	{
		$roleIds = $this->getUserRoleIds($adminId);
		
		if (empty($roleIds)) {
			return [];
		}
		
		return $this->model->whereIn('id', $roleIds)
		                   ->where('status', 1)
		                   ->select()
		                   ->toArray();
	}
	
	
	/**
	 * 分配角色给用户
	 *
	 * @param int|string $adminId
	 * @param array      $roleIds    角色ID数组
	 * @param int        $tenantId   租户ID
	 * @param int|string $operatorId 操作者ID
	 * @return bool
	 * @throws \Exception
	 */
	public function assignRoles(int|string $adminId, array $roleIds, int $tenantId, int|string $operatorId): bool
	{
		
		$adminId = (int)$adminId;
		try {
			$adminRoleModel = new AdminRoleModel();
			$adminRoleList  = $adminRoleModel->field('id')
			                                 ->where('admin_id', $adminId)
			                                 ->where('tenant_id', $tenantId)
			                                 ->select();
			$delRes         = $adminRoleList->delete();
			
			if (!$delRes) {
				throw new BusinessException('操作失败');
			}
			
			$roleArrIds = $this->model->whereIn('id', $roleIds)
			                          ->where('status', 1)
			                          ->where('tenant_id', $tenantId)
			                          ->order('id desc')
			                          ->column('id');
			
			$roleArrIds = array_unique($roleArrIds);
			
			if (count($roleArrIds) != count($roleIds)) {
				throw new BusinessException('角色已禁用或不存在');
			}
			
			if (!empty($roleArrIds)) {
				
				foreach ($roleArrIds as $roleId) {
					$adminRoleModel->saveByCreate([
						'admin_id' => $adminId,
						'role_id'  => $roleId,
					]);
				}
			}
		}
		catch (BusinessException $e) {
			throw new BusinessException($e->getMessage());
		}
		catch (\Exception $e) {
			throw new \Exception($e->getMessage());
		}
		return true;
	}
	
	
	/**
	 * 获取下拉（带数据权限过滤）
	 *
	 * @param array $where 基础查询条件
	 * @return array
	 */
	public function getOptions(array $where = []): array
	{
		$query = $this->model->field('id,name')
		                     ->where($where)
		                     ->order('sort', 'desc');
		
		// 应用数据权限过滤
		$query = $this->applyDataPermission($query);
		
		return $query->select()
		             ->toArray();
	}
	
	/**
	 * 应用数据权限过滤
	 *
	 * @param $query
	 * @return mixed
	 * @throws AuthException
	 */
	private function applyDataPermission($query)
	{
		$adminId = request()->adminId ?? 0;
		
		// 安全检查：必须提供有效的用户ID
		if ($adminId <= 0) {
			throw new AuthException('用户未登录或登录已过期');
		}
		
		// 超级管理员跳过数据权限过滤
		if (is_super_admin()) {
			return $query;
		}
		
		// 租户超级管理员在租户范围内有全部权限
		if (is_tenant_super_admin()) {
			return $query;
		}
		
		// 普通用户：根据数据权限范围过滤
		$dataPermissionUserIds = $this->getDataScopeUserIds($adminId);
		
		// 如果返回空数组，表示有全部数据权限
		if (empty($dataPermissionUserIds)) {
			return $query;
		}
		
		// 只能看到指定用户创建的角色
		return $query->whereIn('creator_id', $dataPermissionUserIds);
	}
	
	
	/**
	 * 获取数据权限范围内的用户ID列表
	 *
	 * @param int $adminId 管理员ID
	 * @return array 可访问的用户ID列表，空数组表示不受限制（全部数据权限）
	 */
	public function getDataScopeUserIds(int $adminId): array
	{
		// 超级管理员拥有所有权限
		if (is_super_admin()) {
			return [];
		}
		
		// 查询用户自身的数据权限设置
		$admin = AdminService::getInstance()
		                     ->getModel()
		                     ->where([
			                     'id'     => $adminId,
			                     'status' => 1,
		                     ])
		                     ->findOrEmpty();
		
		// 检查用户是否有自定义数据权限设置（优先使用用户个人设置）
		if ($admin->data_scope !== null) {
			return $this->getUserScopeIds($adminId, $admin->data_scope, $admin->data_scope_dept_ids, $admin);
		}
		
		// 获取用户所有角色
		$roles = $this->getAdminRoles($adminId);
		if (empty($roles)) {
			// 没有角色，只能看自己的数据
			return [$adminId];
		}
		
		// 数据权限范围，优先级：全部 > 自定义 > 本部门及以下 > 本部门 > 仅本人
		$allData         = false;         // 是否有全部数据权限
		$customDeptIds   = [];            // 自定义部门ID列表
		$hasCustom       = false;         // 是否有自定义数据权限
		$hasDeptAndChild = false;         // 是否有本部门及以下数据权限
		$hasDept         = false;         // 是否有本部门数据权限
		
		// 分析角色的数据权限
		foreach ($roles as $role) {
			switch ($role['data_scope']) {
				case 1: // 全部数据
					$allData = true;
					break 2; // 跳出整个循环
				case 5: // 自定义数据
					$hasCustom = true;
					// 获取角色自定义数据权限
					if (!empty($role['data_scope_dept_ids'])) {
						$deptIds       = explode(',', $role['data_scope_dept_ids']);
						$customDeptIds = array_merge($customDeptIds, $deptIds);
					}
					break;
				case 3: // 本部门及以下
					$hasDeptAndChild = true;
					break;
				case 2: // 本部门
					$hasDept = true;
					break;
				case 4: // 仅本人
				default:
					// 不做处理，默认就是仅本人
					break;
			}
		}
		
		// 如果有全部数据权限
		if ($allData) {
			return [];
		}
		
		// 获取用户所在部门
		$deptId   = $admin->dept_id;
		$adminIds = [];
		// 处理不同的数据权限类型
		if ($hasCustom) {
			// 自定义数据权限
			$adminIds = $this->getDeptUserIds($customDeptIds);
		}
		elseif ($hasDeptAndChild) {
			// 本部门及以下
			$adminIds = $this->getDeptAndChildUserIds($deptId);
		}
		elseif ($hasDept) {
			// 本部门
			$adminIds = $this->getDeptUserIds([$deptId]);
		}
		
		// 添加自己的ID
		$adminIds[] = $adminId;
		
		return array_unique($adminIds);
	}
	
	
	/**
	 * 获取管理员的角色列表
	 *
	 * @param int $adminId 管理员ID
	 * @return array
	 */
	public function getAdminRoles(int $adminId): array
	{
		// 从数据库获取用户角色
		$adminRoleModel = new AdminRoleModel();
		return $adminRoleModel->where('admin_id', $adminId)
		                      ->with(['role'])
		                      ->select()
		                      ->column('role');
	}
	
	/**
	 * 获取指定部门下的所有用户ID
	 *
	 * @param array $deptIds  部门ID列表
	 * @param int   $tenantId 租户ID
	 * @return array
	 */
	public function getDeptUserIds(array $deptIds): array
	{
		if (empty($deptIds)) {
			return [];
		}
		
		// 从数据库获取部门下的用户ID
		return AdminService::getInstance()
		                   ->getModel()
		                   ->where('dept_id', 'in', $deptIds)
		                   ->column('id');
	}
	
	/**
	 * 获取指定部门及其下级部门的所有用户ID
	 *
	 * @param int $deptId 部门ID
	 * @return array
	 */
	public function getDeptAndChildUserIds(int $deptId): array
	{
		// 获取部门及其子部门ID列表
		$deptIds   = $this->getChildDeptIds($deptId);
		$deptIds[] = $deptId;
		
		// 获取这些部门下的所有用户ID
		return $this->getDeptUserIds($deptIds);
	}
	
	/**
	 * 获取指定部门的所有子部门ID
	 *
	 * @param int $deptId 部门ID
	 * @return array
	 */
	public function getChildDeptIds(int $deptId): array
	{
		// 从数据库获取子部门ID
		return DeptService::getInstance()
		                  ->getModel()
		                  ->where('parent_id', $deptId)
		                  ->column('id');
	}
	
	/**
	 * 根据用户个人数据权限设置获取用户ID列表
	 *
	 * @param int                               $adminId          管理员ID
	 * @param int                               $dataScope        数据权限类型
	 * @param string|null                       $dataScopeDeptIds 自定义部门ID字符串
	 * @param \app\system\model\AdminModel|null $admin            用户对象（可选，避免重复查询）
	 * @return array 空数组表示不受限制（全部数据权限），非空数组表示指定用户ID列表
	 */
	private function getUserScopeIds(
		int $adminId, int $dataScope, ?string $dataScopeDeptIds, ?AdminModel $admin = null
	): array
	{
		// 超级管理员拥有所有权限
		if (is_super_admin()) {
			return [];
		}
		
		// 如果没有传入用户对象，则查询
		if ($admin->isEmpty()) {
			$admin = AdminService::getInstance()
			                     ->getModel()
			                     ->where([
				                     'id'     => $adminId,
				                     'status' => 1,
			                     ])
			                     ->findOrEmpty();
			
			if (!$admin) {
				return [$adminId];
			}
		}
		
		$deptId   = $admin->dept_id;
		$adminIds = [];
		
		switch ($dataScope) {
			case 1: // 全部数据
				return [];
			
			case 5: // 自定义数据
				if (!empty($dataScopeDeptIds)) {
					$deptIds  = explode(',', $dataScopeDeptIds);
					$adminIds = $this->getDeptUserIds($deptIds);
				}
				break;
			
			case 3: // 本部门及以下
				$adminIds = $this->getDeptAndChildUserIds($deptId);
				break;
			
			case 2: // 本部门
				$adminIds = $this->getDeptUserIds([$deptId]);
				break;
			
			case 4: // 仅本人
			default:
				// 默认只能看自己的数据
				break;
		}
		
		// 添加自己的ID
		$adminIds[] = $adminId;
		
		return array_unique($adminIds);
	}
} 