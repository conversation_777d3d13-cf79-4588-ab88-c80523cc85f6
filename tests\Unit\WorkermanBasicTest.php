<?php
declare(strict_types=1);

namespace tests\Unit;

use PHPUnit\Framework\TestCase;

// 定义测试环境常量
if (!defined('PHPUNIT_RUNNING')) {
    define('PHPUNIT_RUNNING', true);
}

/**
 * Workerman基础功能测试
 */
class WorkermanBasicTest extends TestCase
{
    /**
     * 测试Workerman目录结构
     */
    public function testWorkermanDirectoryStructure(): void
    {
        $baseDir = __DIR__ . '/../../workerman';
        
        // 检查主目录
        $this->assertDirectoryExists($baseDir);
        
        // 检查子目录
        $this->assertDirectoryExists($baseDir . '/workers');
        $this->assertDirectoryExists($baseDir . '/common');
        
        // 检查核心文件
        $this->assertFileExists($baseDir . '/start.php');
        $this->assertFileExists($baseDir . '/common/WorkerBase.php');
        $this->assertFileExists($baseDir . '/common/ConfigManager.php');
        $this->assertFileExists($baseDir . '/workers/UnifiedWorker.php');
    }
    
    /**
     * 测试WorkerBase类的基本功能
     */
    public function testWorkerBaseClass(): void
    {
        require_once __DIR__ . '/../../workerman/common/WorkerBase.php';
        
        // 创建匿名子类进行测试
        $worker = new class('TestWorker') extends \workerman\common\WorkerBase {
            protected function onWorkerStart($worker): void
            {
                // 测试实现
            }

            public function testFormatBytes($bytes): string
            {
                return $this->formatBytes($bytes);
            }

            public function testSafeExecute(callable $callback): bool
            {
                return $this->safeExecute($callback, 'test');
            }

            public function testIsEnabled(): bool
            {
                return $this->isEnabled();
            }
        };

        // 测试基本属性
        $this->assertEquals('TestWorker', $worker->getWorkerName());
        $this->assertTrue($worker->testIsEnabled());
        
        // 测试字节格式化
        $this->assertEquals('1 KB', $worker->testFormatBytes(1024));
        $this->assertEquals('1 MB', $worker->testFormatBytes(1024 * 1024));
        
        // 测试安全执行
        $result = $worker->testSafeExecute(function() {
            return true;
        });
        $this->assertTrue($result);
        
        // 测试异常处理
        $result = $worker->testSafeExecute(function() {
            throw new \Exception('Test exception');
        });
        $this->assertFalse($result);
    }
    
    /**
     * 测试配置管理器的默认值
     */
    public function testConfigManagerDefaults(): void
    {
        // 模拟get_tenant_id函数
        if (!function_exists('get_tenant_id')) {
            function get_tenant_id() {
                return 1;
            }
        }
        
        require_once __DIR__ . '/../../workerman/common/ConfigManager.php';
        
        // 由于ConfigManager依赖ThinkPHP的组件，这里只测试类是否能正确加载
        $this->assertTrue(class_exists('\workerman\common\ConfigManager'));
        
        // 测试方法是否存在
        $reflection = new \ReflectionClass('\workerman\common\ConfigManager');
        $this->assertTrue($reflection->hasMethod('getSeaRuleConfig'));
        $this->assertTrue($reflection->hasMethod('getAdvanceNoticeConfig'));
        $this->assertTrue($reflection->hasMethod('getTaskReminderConfig'));
        $this->assertTrue($reflection->hasMethod('validateConfig'));
        $this->assertTrue($reflection->hasMethod('clearConfigCache'));
    }
    
    /**
     * 测试启动脚本的基本结构
     */
    public function testStartScript(): void
    {
        $startScript = __DIR__ . '/../../workerman/start.php';
        $this->assertFileExists($startScript);
        
        $content = file_get_contents($startScript);
        
        // 检查关键内容
        $this->assertStringContainsString('declare(strict_types=1)', $content);
        $this->assertStringContainsString('PHP版本必须 >= 7.4.0', $content);
        $this->assertStringContainsString('UnifiedWorker', $content);
        $this->assertStringContainsString('Worker::runAll()', $content);
    }
    
    /**
     * 测试UnifiedWorker类的基本结构
     */
    public function testUnifiedWorkerClass(): void
    {
        // 由于UnifiedWorker依赖Workerman和ThinkPHP组件，这里只测试文件结构
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $this->assertFileExists($workerFile);
        
        $content = file_get_contents($workerFile);
        
        // 检查关键内容
        $this->assertStringContainsString('class UnifiedWorker extends WorkerBase', $content);
        $this->assertStringContainsString('executeSeaRecycle', $content);
        $this->assertStringContainsString('processMessageQueue', $content);
        $this->assertStringContainsString('processDelayedMessages', $content);
        $this->assertStringContainsString('onWorkerStart', $content);
        $this->assertStringContainsString('onConnect', $content);
        $this->assertStringContainsString('onMessage', $content);
        $this->assertStringContainsString('onClose', $content);
    }
    
    /**
     * 测试PHP环境要求
     */
    public function testPhpEnvironment(): void
    {
        // 检查PHP版本
        $this->assertGreaterThanOrEqual('7.4.0', PHP_VERSION);
        
        // 检查必要的类是否存在
        $this->assertTrue(class_exists('DateTime'));
        $this->assertTrue(class_exists('Exception'));
        $this->assertTrue(class_exists('ReflectionClass'));
        
        // 检查JSON支持
        $this->assertTrue(function_exists('json_encode'));
        $this->assertTrue(function_exists('json_decode'));
    }
    
    /**
     * 测试文件权限和可读性
     */
    public function testFilePermissions(): void
    {
        $files = [
            __DIR__ . '/../../workerman/start.php',
            __DIR__ . '/../../workerman/common/WorkerBase.php',
            __DIR__ . '/../../workerman/common/ConfigManager.php',
            __DIR__ . '/../../workerman/workers/UnifiedWorker.php'
        ];
        
        foreach ($files as $file) {
            $this->assertFileExists($file);
            $this->assertFileIsReadable($file);
            
            // 检查文件不为空
            $this->assertGreaterThan(0, filesize($file));
        }
    }
    
    /**
     * 测试命名空间和类名
     */
    public function testNamespacesAndClasses(): void
    {
        $files = [
            __DIR__ . '/../../workerman/common/WorkerBase.php' => 'workerman\common\WorkerBase',
            __DIR__ . '/../../workerman/common/ConfigManager.php' => 'workerman\common\ConfigManager',
            __DIR__ . '/../../workerman/workers/UnifiedWorker.php' => 'workerman\workers\UnifiedWorker'
        ];
        
        foreach ($files as $file => $expectedClass) {
            $content = file_get_contents($file);
            
            // 检查命名空间声明
            $namespace = str_replace('/', '\\', dirname(str_replace('\\', '/', $expectedClass)));
            $this->assertStringContainsString("namespace {$namespace};", $content);
            
            // 检查类声明
            $className = basename($expectedClass);
            $this->assertStringContainsString("class {$className}", $content);
        }
    }
}
