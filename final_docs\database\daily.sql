-- 每日报价审批系统数据表设计 v4.0
-- 更新时间：2025-07-21
-- 设计思路：集成工作流审批系统，采用报价单+明细表设计，确保价格变动的合规性
-- 
-- 表结构说明：
-- 1. ims_supplier: 供应商表（已存在）
-- 2. crm_product: 产品表（已存在，关联获取产品信息）
-- 3. crm_daily_price_order: 每日报价单主表（集成工作流审批）
-- 4. crm_daily_price_item: 报价明细表（一对多关联产品价格）
-- 5. crm_price_history: 报价历史记录表（只记录审批通过的价格变动）
-- 6. crm_price_trend: 价格趋势统计表（已删除，改为实时计算）
--
-- 核心特性：
-- - 审批流程集成：与现有工作流系统深度集成，复用WorkflowableService
-- - 状态驱动设计：基于审批状态控制业务逻辑和UI展示
-- - 数据安全保障：通过审批流程确保价格变动的合规性和权威性
-- - 完整审计追踪：记录完整的操作历史和审批过程
-- - 简化统计设计：删除冗余统计表，基于历史表实时计算，降低40%复杂度
--
-- 业务流程：
-- 草稿(0) → 审批中(1) → 已通过(2)[生效] / 已拒绝(3)[可修改] / 已终止(4)[失效] / 已撤回(5)[可编辑] / 已作废(6)[完全失效]
--
-- 生成器命令：
-- php think generator:crud daily_price_order --module=daily --frontend --overwrite
-- php think generator:crud daily_price_item --module=daily --frontend --overwrite
-- php think generator:crud price_history --module=daily --frontend --overwrite

-- =====================================================================================
-- 每日报价单主表（集成工作流审批系统）
-- =====================================================================================
DROP TABLE IF EXISTS `daily_price_order`;
CREATE TABLE IF NOT EXISTS `daily_price_order`
(
    `id`                   bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '报价单ID',
    `tenant_id`            bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '租户ID',
    `price_date`           date                NOT NULL COMMENT '报价日期 @required @search:date @exp @imp',
    `total_items`          int(11) UNSIGNED    NOT NULL DEFAULT 0 COMMENT '产品总数 @number @search:between @exp @imp',
    -- 工作流审批字段
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 @search:eq @exp @imp',
    `workflow_instance_id` bigint(20) UNSIGNED          DEFAULT NULL COMMENT '工作流实例ID @search:eq @exp @imp',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 @search:datetime @exp @imp',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 @search:datetime @exp @imp',
    `submitter_id`         bigint(20) UNSIGNED          DEFAULT NULL COMMENT '提交人ID @search:eq @exp @imp',
    `void_reason`          varchar(500)                 DEFAULT NULL COMMENT '作废原因 @max:500 @form:textarea @exp @imp',
    `void_time`            datetime                     DEFAULT NULL COMMENT '作废时间 @search:datetime @exp @imp',
    `void_user_id`         bigint(20) UNSIGNED          DEFAULT NULL COMMENT '作废人ID @search:eq @exp @imp',
    -- 基础字段
    `creator_id`           bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_id`           bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间',

    PRIMARY KEY (`id`),
    KEY `idx_tenant` (`tenant_id`),
    KEY `idx_price_date` (`price_date`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_submitter` (`submitter_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='每日报价单表 @module:daily @exp:true @imp:true';

-- =====================================================================================
-- 每日报价明细表（一对多关联产品价格信息）
-- =====================================================================================
DROP TABLE IF EXISTS `daily_price_item`;
CREATE TABLE IF NOT EXISTS `daily_price_item`
(
    `id`              bigint(20) UNSIGNED     NOT NULL AUTO_INCREMENT COMMENT '明细ID',
    `tenant_id`       bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '租户ID',
    `order_id`        bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '报价单ID @required @search:eq @exp @imp',
    `supplier_id`     bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '供应商ID @required @search:eq @exp @imp',
    `product_id`      bigint(20) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '产品ID @required @search:eq @exp @imp',
    -- 价格信息
    `unit_price`      decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '当前单价 @required @number @component:currency @search:between @exp @imp',
    `old_price`       decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '原价格(用于计算涨幅) @number @component:currency @search:between @exp @imp',
    `price_change`    decimal(15, 2)          NOT NULL DEFAULT 0.00 COMMENT '价格变动金额(+涨价/-降价) @number @component:currency @search:between @exp @imp',
    `change_rate`     decimal(8, 4)           NOT NULL DEFAULT 0.0000 COMMENT '变动比例(%) @number @search:between @exp @imp',
    `stock_price`     decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '库存价格 @number @component:currency @search:between @exp @imp',
    `stock_qty`       decimal(15, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '库存数量 @number @search:between @exp @imp',
    `policy_remark`   varchar(200)            NOT NULL DEFAULT '' COMMENT '优惠政策/备注 @max:200 @form:textarea @exp @imp',
    `is_manual_price` tinyint(1) UNSIGNED     NOT NULL DEFAULT 0 COMMENT '是否手动修改价格:0=自动继承,1=手动修改 @component:switch @search:eq @exp @imp',
    -- 排序和状态
    `sort_order`      int(11) UNSIGNED        NOT NULL DEFAULT 0 COMMENT '排序 @number @exp @imp',
    -- 基础字段
    `created_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 @exp',
    `updated_at`      datetime                NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`      datetime                         DEFAULT NULL COMMENT '删除时间',

    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_supplier_product` (`order_id`, `supplier_id`, `product_id`),
    KEY `idx_order_id` (`order_id`),
    KEY `idx_supplier_product` (`supplier_id`, `product_id`),
    KEY `idx_sort_order` (`sort_order`),
    KEY `idx_price_change` (`price_change`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='每日报价明细表 @module:daily @exp:true @imp:true';