<?php
declare(strict_types=1);

namespace workerman\common;

use Workerman\Worker;
use think\facade\Log;

/**
 * Workerman统一基类
 * 提供通用的Worker功能和日志记录
 */
abstract class WorkerBase
{
    /**
     * Worker实例
     */
    protected Worker $worker;
    
    /**
     * Worker名称
     */
    protected string $workerName;
    
    /**
     * 是否启用
     */
    protected bool $enabled = true;
    
    /**
     * 租户ID（用于多租户支持）
     */
    protected int $tenantId = 0;
    
    /**
     * 构造函数
     */
    public function __construct(string $workerName = '')
    {
        $this->workerName = $workerName ?: static::class;
        $this->initialize();
    }
    
    /**
     * 初始化方法，子类可重写
     */
    protected function initialize(): void
    {
        // 子类可重写此方法进行初始化
    }
    
    /**
     * Worker启动时的回调，子类必须实现
     */
    abstract protected function onWorkerStart($worker): void;
    
    /**
     * 记录日志
     */
    protected function log(string $message, string $level = 'info', array $context = []): void
    {
        $logMessage = "[{$this->workerName}] {$message}";

        // 在Workerman环境中使用简单的文件日志，避免ThinkPHP的Log facade
        if (defined('WORKERMAN_RUNNING')) {
            // 输出到控制台
            echo "[" . date('Y-m-d H:i:s') . "] [{$level}] {$logMessage}\n";

            // 写入日志文件
            $logFile = __DIR__ . '/../../runtime/log/workerman_' . date('Y-m-d') . '.log';
            $logDir = dirname($logFile);
            if (!is_dir($logDir)) {
                @mkdir($logDir, 0755, true);
            }

            $logContent = "[" . date('Y-m-d H:i:s') . "] [{$level}] {$logMessage}";
            if (!empty($context)) {
                $logContent .= " " . json_encode($context, JSON_UNESCAPED_UNICODE);
            }
            $logContent .= "\n";

            @file_put_contents($logFile, $logContent, FILE_APPEND | LOCK_EX);
        } else {
            // 在其他环境中使用ThinkPHP的Log facade
            if (class_exists('think\facade\Log') && !defined('PHPUNIT_RUNNING')) {
                switch ($level) {
                    case 'error':
                        Log::error($logMessage, $context);
                        break;
                    case 'warning':
                        Log::warning($logMessage, $context);
                        break;
                    case 'debug':
                        Log::debug($logMessage, $context);
                        break;
                    default:
                        Log::info($logMessage, $context);
                }
            }

            // 同时输出到控制台
            echo "[" . date('Y-m-d H:i:s') . "] {$logMessage}\n";
        }
    }
    
    /**
     * 检查是否启用
     */
    protected function isEnabled(): bool
    {
        return $this->enabled;
    }
    
    /**
     * 设置启用状态
     */
    public function setEnabled(bool $enabled): void
    {
        $this->enabled = $enabled;
    }
    
    /**
     * 获取Worker名称
     */
    public function getWorkerName(): string
    {
        return $this->workerName;
    }
    
    /**
     * 安全执行方法，捕获异常并记录
     */
    protected function safeExecute(callable $callback, string $operation = 'operation'): bool
    {
        try {
            $callback();
            return true;
        } catch (\Throwable $e) {
            $this->log("执行{$operation}时发生异常: " . $e->getMessage(), 'error', [
                'exception' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return false;
        }
    }
    
    /**
     * 格式化时间
     */
    protected function formatTime(string $format = 'Y-m-d H:i:s'): string
    {
        return date($format);
    }
    
    /**
     * 获取内存使用情况
     */
    protected function getMemoryUsage(): array
    {
        return [
            'current' => memory_get_usage(true),
            'peak' => memory_get_peak_usage(true),
            'current_formatted' => $this->formatBytes(memory_get_usage(true)),
            'peak_formatted' => $this->formatBytes(memory_get_peak_usage(true))
        ];
    }
    
    /**
     * 格式化字节数
     */
    protected function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes >= 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
