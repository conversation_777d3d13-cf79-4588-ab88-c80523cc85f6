<?php

namespace app\system\controller;

use app\common\core\base\BaseAdminController;
use app\system\service\ConfigService;
use think\response\Json;

class ConfigController extends BaseAdminController
{
	/**
	 * @var ConfigService
	 */
	private ConfigService $service;
	
	public function initialize(): void
	{
		parent::initialize();
		
		$this->service = ConfigService::getInstance();
	}
	
	public function index(): Json
	{
		$websiteInfo = $this->service->getInfo('website');
		$common      = $this->service->getInfo('common');
		$data        = [
			'systemInfo' => [
				'name'        => $websiteInfo['site_name'] ?? '',
				'title'       => $websiteInfo['system_title'] ?? '',
				'logo'        => $websiteInfo['system_logo'] ?? '',
				'background'  => $websiteInfo['login_bg'] ?? '',
				'version'     => $websiteInfo['version'] ?? '',
				'captcha'     => !empty($common['captcha']),
				'record_info' => $websiteInfo['record_info'] ?? '',
				'copyright'   => $websiteInfo['copyright'] ?? '',
				'remember'    => !empty($common['remember']),
			]
		];
		return $this->success('获取成功', $data);
	}
	
	/**
	 * 根据group获取配置
	 */
	public function detail(): Json
	{
		$data      = [];
		$configArr = [
			'website',
			'upload',
			'common'
		];
		foreach ($configArr as $item) {
			$data[$item] = $this->service->getInfo($item);
		}
		return $this->success('获取成功', $data);
	}
	
	/**
	 * 创建
	 */
	public function save(): Json
	{
		return $this->service->create(input('group/s', ''), input('config/a'))
			? $this->success('创建成功')
			: $this->error('创建失败');
	}
	
	
}