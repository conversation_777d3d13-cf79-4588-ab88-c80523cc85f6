<?php

namespace app\common\middleware;

use app\common\exception\AuthException;
use app\common\utils\TokenUtil;
use think\Response;

class TokenAuthMiddleware
{
	/**
	 * 处理请求
	 *
	 * @param \think\Request $request
	 * @param \Closure       $next
	 * @return Response
	 */
	public function handle($request, \Closure $next)
	{
		// 获取请求头中的token
		$token = $request->header('Authorization');
		
		if (empty($token)) {
			throw new AuthException();
		}
		
		// 验证token
		$adminInfo = TokenUtil::getTokenInfo($token);
		if (empty($adminInfo) || empty($adminInfo['data']) || !isset($adminInfo['data']['id']) || $adminInfo['data']['id'] < 0 || empty($adminInfo['data']['status'])) {
			throw new AuthException();
		}
		// 将用户信息附加到请求中
		$request->adminInfo = $adminInfo;
		$request->adminId   = $adminInfo['data']['id'] ?? -1;

		// 关键修改：处理租户切换逻辑
		if (isset($adminInfo['tenant_switch']) && $adminInfo['tenant_switch']['is_switched']) {
			// 使用切换后的租户ID
			$request->tenantId = $adminInfo['tenant_switch']['current_tenant_id'];
			$request->isTenantSwitched = true;
			$request->originalTenantId = $adminInfo['tenant_switch']['original_tenant_id'];
			$request->switchMode = $adminInfo['tenant_switch']['switch_mode'];
			$request->switchTime = $adminInfo['tenant_switch']['switch_time'];
		} else {
			// 使用原始租户ID
			$request->tenantId = $adminInfo['data']['tenant_id'] ?? -1;
			$request->isTenantSwitched = false;
			$request->originalTenantId = $adminInfo['data']['tenant_id'] ?? -1;
			$request->switchMode = 'original';
		}

		return $next($request);
	}
}