<?php
declare(strict_types=1);

namespace tests\Unit;

use PHPUnit\Framework\TestCase;

// 定义测试环境常量
if (!defined('PHPUNIT_RUNNING')) {
    define('PHPUNIT_RUNNING', true);
}

/**
 * 任务提醒服务单元测试
 */
class TaskReminderServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // 模拟租户环境
        if (!function_exists('get_tenant_id')) {
            function get_tenant_id() {
                return 1;
            }
        }
    }
    
    /**
     * 测试TaskReminderService文件是否存在
     */
    public function testTaskReminderServiceFileExists(): void
    {
        $serviceFile = __DIR__ . '/../../workerman/services/TaskReminderService.php';
        $this->assertFileExists($serviceFile);
        $this->assertFileIsReadable($serviceFile);
        $this->assertGreaterThan(0, filesize($serviceFile));
    }
    
    /**
     * 测试TaskReminderService文件语法
     */
    public function testTaskReminderServiceSyntax(): void
    {
        $serviceFile = __DIR__ . '/../../workerman/services/TaskReminderService.php';
        $content = file_get_contents($serviceFile);
        
        // 检查基本语法结构
        $this->assertStringContainsString('<?php', $content);
        $this->assertStringContainsString('declare(strict_types=1);', $content);
        $this->assertStringContainsString('namespace workerman\services;', $content);
        $this->assertStringContainsString('class TaskReminderService extends WorkerBase', $content);
        
        // 检查关键方法
        $this->assertStringContainsString('public function executeTaskReminder', $content);
        $this->assertStringContainsString('private function processOverdueReminders', $content);
        $this->assertStringContainsString('private function processDueSoonReminders', $content);
        $this->assertStringContainsString('private function processFollowUpReminders', $content);
        $this->assertStringContainsString('private function getOverdueTasks', $content);
        $this->assertStringContainsString('private function getDueSoonTasks', $content);
        $this->assertStringContainsString('private function sendTaskReminder', $content);
        $this->assertStringContainsString('private function recordTaskReminderLog', $content);
    }
    
    /**
     * 测试UnifiedWorker是否包含任务提醒功能
     */
    public function testUnifiedWorkerTaskReminderIntegration(): void
    {
        $workerFile = __DIR__ . '/../../workerman/workers/UnifiedWorker.php';
        $content = file_get_contents($workerFile);
        
        // 检查是否引入了TaskReminderService
        $this->assertStringContainsString('use workerman\services\TaskReminderService;', $content);
        
        // 检查是否添加了任务提醒定时器
        $this->assertStringContainsString('task_reminder', $content);
        $this->assertStringContainsString('executeTaskReminder', $content);
        
        // 检查定时器配置
        $this->assertStringContainsString('任务提醒检查', $content);
    }
    
    /**
     * 测试启动脚本是否包含TaskReminderService
     */
    public function testStartScriptIncludesTaskReminderService(): void
    {
        $startFile = __DIR__ . '/../../workerman/start.php';
        $content = file_get_contents($startFile);
        
        // 检查是否包含TaskReminderService文件
        $this->assertStringContainsString('services/TaskReminderService.php', $content);
    }
    
    /**
     * 测试任务提醒配置结构
     */
    public function testTaskReminderConfigStructure(): void
    {
        // 测试默认配置结构
        $defaultConfig = [
            'reminder_enabled' => false,
            'overdue_enabled' => true,
            'overdue_frequency' => 'daily',
            'due_soon_enabled' => true,
            'due_soon_days' => [1, 3, 7],
            'follow_up_enabled' => true,
            'follow_up_advance_hours' => 2
        ];
        
        // 验证配置键
        $this->assertArrayHasKey('reminder_enabled', $defaultConfig);
        $this->assertArrayHasKey('overdue_enabled', $defaultConfig);
        $this->assertArrayHasKey('overdue_frequency', $defaultConfig);
        $this->assertArrayHasKey('due_soon_enabled', $defaultConfig);
        $this->assertArrayHasKey('due_soon_days', $defaultConfig);
        $this->assertArrayHasKey('follow_up_enabled', $defaultConfig);
        $this->assertArrayHasKey('follow_up_advance_hours', $defaultConfig);
        
        // 验证数据类型
        $this->assertIsBool($defaultConfig['reminder_enabled']);
        $this->assertIsBool($defaultConfig['overdue_enabled']);
        $this->assertIsString($defaultConfig['overdue_frequency']);
        $this->assertIsBool($defaultConfig['due_soon_enabled']);
        $this->assertIsArray($defaultConfig['due_soon_days']);
        $this->assertIsBool($defaultConfig['follow_up_enabled']);
        $this->assertIsInt($defaultConfig['follow_up_advance_hours']);
    }
    
    /**
     * 测试提醒类型
     */
    public function testReminderTypes(): void
    {
        $reminderTypes = [
            'overdue',           // 逾期任务
            'due_soon_1',        // 1天后到期
            'due_soon_3',        // 3天后到期
            'due_soon_7',        // 7天后到期
            'follow_up',         // 任务跟进计划
            'crm_follow_up'      // CRM跟进计划
        ];
        
        foreach ($reminderTypes as $type) {
            $this->assertIsString($type);
            $this->assertNotEmpty($type);
        }
        
        // 验证类型含义
        $this->assertEquals('overdue', $reminderTypes[0]);
        $this->assertStringContainsString('due_soon', $reminderTypes[1]);
        $this->assertStringContainsString('follow_up', $reminderTypes[4]);
    }
    
    /**
     * 测试提醒频率选项
     */
    public function testReminderFrequencyOptions(): void
    {
        $validFrequencies = ['once', 'daily', 'weekly'];
        
        foreach ($validFrequencies as $frequency) {
            $this->assertIsString($frequency);
            $this->assertNotEmpty($frequency);
        }
        
        // 验证频率含义
        $this->assertEquals('once', $validFrequencies[0]);     // 仅提醒一次
        $this->assertEquals('daily', $validFrequencies[1]);    // 每天提醒
        $this->assertEquals('weekly', $validFrequencies[2]);   // 每周提醒
    }
    
    /**
     * 测试任务状态
     */
    public function testTaskStatuses(): void
    {
        // 需要提醒的任务状态
        $activeStatuses = [1, 2]; // 进行中、待处理
        
        foreach ($activeStatuses as $status) {
            $this->assertIsInt($status);
            $this->assertGreaterThan(0, $status);
        }
        
        $this->assertEquals(1, $activeStatuses[0]); // 进行中
        $this->assertEquals(2, $activeStatuses[1]); // 待处理
    }
    
    /**
     * 测试日期时间计算
     */
    public function testDateTimeCalculations(): void
    {
        // 测试即将到期日期计算
        $days = 3;
        $targetDate = date('Y-m-d', strtotime("+{$days} days"));
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $targetDate);
        
        // 测试跟进提前时间计算
        $hours = 2;
        $targetTime = date('Y-m-d H:i:s', strtotime("+{$hours} hours"));
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $targetTime);
        
        // 验证时间逻辑
        $now = time();
        $futureTime = strtotime($targetTime);
        $this->assertGreaterThan($now, $futureTime);
        
        // 验证小时差
        $hoursDiff = ($futureTime - $now) / 3600;
        $this->assertGreaterThanOrEqual(1.5, $hoursDiff); // 允许一些误差
        $this->assertLessThanOrEqual(2.5, $hoursDiff);
    }
    
    /**
     * 测试任务提醒记录表结构
     */
    public function testTaskReminderLogStructure(): void
    {
        // 模拟project_task_reminder_log表结构
        $logRecord = [
            'id' => 1,
            'task_id' => 123,
            'reminder_type' => 'overdue',
            'reminder_date' => '2025-08-04',
            'sent_at' => '2025-08-04 10:30:00',
            'tenant_id' => 1
        ];
        
        // 验证字段存在
        $requiredFields = ['id', 'task_id', 'reminder_type', 'reminder_date', 'sent_at', 'tenant_id'];
        foreach ($requiredFields as $field) {
            $this->assertArrayHasKey($field, $logRecord);
        }
        
        // 验证数据格式
        $this->assertIsInt($logRecord['id']);
        $this->assertIsInt($logRecord['task_id']);
        $this->assertIsString($logRecord['reminder_type']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}$/', $logRecord['reminder_date']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $logRecord['sent_at']);
        $this->assertIsInt($logRecord['tenant_id']);
    }
    
    /**
     * 测试SQL文件是否存在
     */
    public function testSqlFileExists(): void
    {
        $sqlFile = __DIR__ . '/../../database/migrations/create_project_task_reminder_log_table.sql';
        $this->assertFileExists($sqlFile);
        $this->assertFileIsReadable($sqlFile);
        
        $content = file_get_contents($sqlFile);
        
        // 检查SQL语法
        $this->assertStringContainsString('CREATE TABLE `project_task_reminder_log`', $content);
        $this->assertStringContainsString('PRIMARY KEY (`id`)', $content);
        $this->assertStringContainsString('UNIQUE KEY `uk_task_type_date`', $content);
        $this->assertStringContainsString('KEY `idx_tenant_date`', $content);
        $this->assertStringContainsString('ENGINE=InnoDB', $content);
    }
    
    /**
     * 测试模板消息
     */
    public function testReminderTemplates(): void
    {
        $templates = [
            'overdue' => '您的任务"{{task_name}}"已逾期，截止时间：{{due_date}}，请尽快处理。',
            'due_soon' => '您的任务"{{task_name}}"将在{{days}}天后到期（{{due_date}}），请注意安排时间。'
        ];
        
        // 检查模板变量
        foreach ($templates as $type => $template) {
            $this->assertStringContainsString('{{task_name}}', $template);
            
            if ($type === 'overdue') {
                $this->assertStringContainsString('已逾期', $template);
            } elseif ($type === 'due_soon') {
                $this->assertStringContainsString('{{days}}', $template);
                $this->assertStringContainsString('天后到期', $template);
            }
        }
        
        // 测试变量替换
        $content = str_replace([
            '{{task_name}}',
            '{{due_date}}',
            '{{days}}'
        ], [
            '测试任务',
            '2025-08-07 18:00:00',
            '3'
        ], $templates['due_soon']);
        
        $expected = '您的任务"测试任务"将在3天后到期（2025-08-07 18:00:00），请注意安排时间。';
        $this->assertEquals($expected, $content);
    }
}
