<?php
/**
 * 增强的图表显示数据格式
 * 包含百分比和更详细的信息展示
 */

/**
 * 增强版任务状态统计 - 包含百分比
 */
function getEnhancedTaskStatusStats($projectId)
{
    $statusConfig = [
        1 => ['name' => '待办', 'color' => '#8C8C8C'],
        2 => ['name' => '进行中', 'color' => '#1664FF'], 
        3 => ['name' => '已完成', 'color' => '#00BC70'],
        4 => ['name' => '已关闭', 'color' => '#F54A45']
    ];
    
    $project = ProjectProject::find($projectId);
    if (!$project) {
        return [];
    }
    
    // 先获取总数
    $totalTasks = $project->tasks()->count();
    if ($totalTasks == 0) {
        return [];
    }
    
    $result = [];
    foreach ($statusConfig as $status => $config) {
        $count = $project->tasks()->where('status', $status)->count();
        
        if ($count > 0) {
            $percentage = round(($count / $totalTasks) * 100, 1);
            
            $result[] = [
                'name' => $config['name'],
                'value' => $count,
                'color' => $config['color'],
                'percentage' => $percentage,
                'label' => "{$config['name']}: {$count}个 ({$percentage}%)", // 用于图表显示
                'description' => "{$config['name']}任务共{$count}个，占总任务的{$percentage}%"
            ];
        }
    }
    
    return $result;
}

/**
 * 增强版任务优先级统计 - 包含百分比
 */
function getEnhancedTaskPriorityStats($projectId)
{
    $priorityConfig = [
        1 => ['name' => '低', 'color' => '#52C41A'],
        2 => ['name' => '中', 'color' => '#1890FF'], 
        3 => ['name' => '高', 'color' => '#F5222D']
    ];
    
    $project = ProjectProject::find($projectId);
    if (!$project) {
        return [];
    }
    
    $totalTasks = $project->tasks()->count();
    if ($totalTasks == 0) {
        return [];
    }
    
    $result = [];
    foreach ($priorityConfig as $priority => $config) {
        $count = $project->tasks()->where('priority', $priority)->count();
        
        if ($count > 0) {
            $percentage = round(($count / $totalTasks) * 100, 1);
            
            $result[] = [
                'name' => $config['name'],
                'value' => $count,
                'color' => $config['color'],
                'percentage' => $percentage,
                'label' => "{$config['name']}: {$count}个 ({$percentage}%)",
                'description' => "{$config['name']}优先级任务共{$count}个，占总任务的{$percentage}%"
            ];
        }
    }
    
    return $result;
}

/**
 * 增强版成员统计 - 包含更多详细信息
 */
function getEnhancedMemberStats($projectId)
{
    $project = ProjectProject::with(['members.user'])->find($projectId);
    if (!$project) {
        return [];
    }
    
    $result = [];
    foreach ($project->members as $member) {
        if (!$member->user) continue;
        
        $totalTasks = $project->tasks()
                             ->where('assignee_id', $member->user_id)
                             ->count();
        
        $completedTasks = $project->tasks()
                                 ->where('assignee_id', $member->user_id)
                                 ->where('status', 3)
                                 ->count();
        
        $inProgressTasks = $project->tasks()
                                  ->where('assignee_id', $member->user_id)
                                  ->where('status', 2)
                                  ->count();
        
        $pendingTasks = $project->tasks()
                               ->where('assignee_id', $member->user_id)
                               ->where('status', 1)
                               ->count();
        
        $completionRate = $totalTasks > 0 
            ? round(($completedTasks / $totalTasks) * 100, 1) 
            : 0;
        
        $result[] = [
            'id' => $member->user->id,
            'name' => $member->user->real_name,
            'avatar' => $member->user->avatar,
            'role' => $member->getRoleText(),
            'task_count' => $totalTasks,
            'completed_tasks' => $completedTasks,
            'in_progress_tasks' => $inProgressTasks,
            'pending_tasks' => $pendingTasks,
            'completion_rate' => $completionRate,
            'status_text' => "已完成 {$completedTasks}/{$totalTasks} 个任务",
            'progress_text' => "完成率 {$completionRate}%"
        ];
    }
    
    // 按任务数量排序
    usort($result, function($a, $b) {
        return $b['task_count'] - $a['task_count'];
    });
    
    return $result;
}

/**
 * 增强版项目进度趋势 - 包含更多信息
 */
function getEnhancedProgressTrend($projectId)
{
    $project = ProjectProject::find($projectId);
    if (!$project) {
        return [];
    }
    
    $tasks = $project->tasks()
                    ->field('created_at, status, completed_at')
                    ->order('created_at asc')
                    ->select();
    
    if ($tasks->isEmpty()) {
        return [];
    }
    
    // 按月份统计
    $monthlyData = [];
    foreach ($tasks as $task) {
        $month = date('Y-m', strtotime($task->created_at));
        if (!isset($monthlyData[$month])) {
            $monthlyData[$month] = [
                'total' => 0, 
                'completed' => 0,
                'in_progress' => 0,
                'pending' => 0
            ];
        }
        
        $monthlyData[$month]['total']++;
        
        switch ($task->status) {
            case 3: $monthlyData[$month]['completed']++; break;
            case 2: $monthlyData[$month]['in_progress']++; break;
            case 1: $monthlyData[$month]['pending']++; break;
        }
    }
    
    $result = [];
    foreach ($monthlyData as $month => $data) {
        $progress = $data['total'] > 0 
            ? round(($data['completed'] / $data['total']) * 100, 1) 
            : 0;
            
        $result[] = [
            'date' => $month,
            'progress' => $progress,
            'total_tasks' => $data['total'],
            'completed_tasks' => $data['completed'],
            'in_progress_tasks' => $data['in_progress'],
            'pending_tasks' => $data['pending'],
            'label' => date('Y年m月', strtotime($month . '-01')),
            'tooltip' => "完成率: {$progress}%\n总任务: {$data['total']}个\n已完成: {$data['completed']}个"
        ];
    }
    
    // 补充最近6个月数据
    if (count($result) < 6) {
        $result = fillProgressTrendDataEnhanced($result);
    }
    
    return $result;
}

/**
 * 增强版最近活动 - 包含更多上下文信息
 */
function getEnhancedRecentActivities($projectId)
{
    $project = ProjectProject::find($projectId);
    if (!$project) {
        return [];
    }
    
    $taskIds = $project->tasks()->column('id');
    if (empty($taskIds)) {
        return [];
    }
    
    $records = ProjectTaskRecord::with(['task', 'creator'])
                               ->whereIn('task_id', $taskIds)
                               ->order('created_at desc')
                               ->limit(15)
                               ->select();
    
    $result = [];
    foreach ($records as $record) {
        if (!$record->task || !$record->creator) continue;
        
        $actionMap = [
            'comment' => '评论了任务',
            'follow' => '跟进了任务'
        ];
        
        $action = $actionMap[$record->record_type] ?? '更新了任务';
        $time = formatRelativeTime($record->created_at);
        $taskStatus = getTaskStatusText($record->task->status);
        
        $result[] = [
            'id' => $record->id,
            'user' => $record->creator->real_name,
            'user_avatar' => $record->creator->avatar,
            'action' => $action,
            'target' => $record->task->title,
            'task_status' => $taskStatus,
            'time' => $time,
            'datetime' => $record->created_at,
            'content_preview' => mb_substr($record->content, 0, 50) . '...',
            'record_type' => $record->record_type
        ];
    }
    
    return $result;
}

/**
 * 获取任务状态文本
 */
function getTaskStatusText($status)
{
    $statusMap = [
        1 => '待办',
        2 => '进行中',
        3 => '已完成',
        4 => '已关闭'
    ];
    
    return $statusMap[$status] ?? '未知';
}

/**
 * 补充进度趋势数据 - 增强版
 */
function fillProgressTrendDataEnhanced($existingData)
{
    $result = [];
    for ($i = 5; $i >= 0; $i--) {
        $month = date('Y-m', strtotime("-{$i} months"));
        
        $found = false;
        foreach ($existingData as $data) {
            if ($data['date'] == $month) {
                $result[] = $data;
                $found = true;
                break;
            }
        }
        
        if (!$found) {
            $result[] = [
                'date' => $month,
                'progress' => 0,
                'total_tasks' => 0,
                'completed_tasks' => 0,
                'in_progress_tasks' => 0,
                'pending_tasks' => 0,
                'label' => date('Y年m月', strtotime($month . '-01')),
                'tooltip' => "暂无任务数据"
            ];
        }
    }
    
    return $result;
}

// 测试增强版数据格式
echo "=== 增强版统计数据格式示例 ===\n";

// 模拟数据
$enhancedStatusStats = [
    [
        'name' => '待办',
        'value' => 4,
        'color' => '#8C8C8C',
        'percentage' => 23.5,
        'label' => '待办: 4个 (23.5%)',
        'description' => '待办任务共4个，占总任务的23.5%'
    ],
    [
        'name' => '进行中',
        'value' => 5,
        'color' => '#1664FF',
        'percentage' => 29.4,
        'label' => '进行中: 5个 (29.4%)',
        'description' => '进行中任务共5个，占总任务的29.4%'
    ],
    [
        'name' => '已完成',
        'value' => 6,
        'color' => '#00BC70',
        'percentage' => 35.3,
        'label' => '已完成: 6个 (35.3%)',
        'description' => '已完成任务共6个，占总任务的35.3%'
    ],
    [
        'name' => '已关闭',
        'value' => 2,
        'color' => '#F54A45',
        'percentage' => 11.8,
        'label' => '已关闭: 2个 (11.8%)',
        'description' => '已关闭任务共2个，占总任务的11.8%'
    ]
];

echo "任务状态分布 (增强版):\n";
echo json_encode($enhancedStatusStats, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
?>
