<?php
declare(strict_types=1);

/**
 * Workerman自动化系统启动脚本
 * 
 * 使用方法：
 * Windows: php start.php start
 * Linux: php start.php start -d (守护进程模式)
 */

// 检查PHP版本
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    die("PHP版本必须 >= 7.4.0，当前版本: " . PHP_VERSION . "\n");
}

// 检查必要的扩展
$requiredExtensions = ['pcntl', 'posix'];
$missingExtensions = [];

foreach ($requiredExtensions as $ext) {
    if (!extension_loaded($ext)) {
        $missingExtensions[] = $ext;
    }
}

// Windows系统下pcntl和posix扩展不可用，给出提示但不阻止运行
if (!empty($missingExtensions) && PHP_OS_FAMILY !== 'Windows') {
    die("缺少必要的PHP扩展: " . implode(', ', $missingExtensions) . "\n");
}

if (PHP_OS_FAMILY === 'Windows') {
    echo "检测到Windows系统，部分功能可能受限（守护进程、信号处理等）\n";
}

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 定义Workerman运行环境常量
if (!defined('WORKERMAN_RUNNING')) {
    define('WORKERMAN_RUNNING', true);
}

// 引入必要文件
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/common.php';
require_once __DIR__ . '/common/WorkerBase.php';
require_once __DIR__ . '/common/ConfigManager.php';
require_once __DIR__ . '/services/AdvanceNoticeService.php';
require_once __DIR__ . '/services/TaskReminderService.php';
require_once __DIR__ . '/workers/UnifiedWorker.php';

use Workerman\Worker;
use workerman\workers\UnifiedWorker;
use think\App;
use think\Container;

// 显示启动信息
echo "=================================\n";
echo "Workerman自动化系统\n";
echo "版本: 1.0.0\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "系统: " . PHP_OS_FAMILY . "\n";
echo "启动时间: " . date('Y-m-d H:i:s') . "\n";
echo "=================================\n";

// 初始化ThinkPHP应用
try {
    echo "正在初始化ThinkPHP应用...\n";

    // 创建应用实例
    $app = new App();

    // 绑定请求类
    $app->bind('think\Request', \app\Request::class);

    // 初始化应用
    $app->initialize();

    // 设置应用实例到容器
    Container::setInstance($app);

    echo "ThinkPHP应用初始化成功\n";

} catch (\Throwable $e) {
    echo "ThinkPHP应用初始化失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 创建统一Worker实例
try {
    $unifiedWorker = new UnifiedWorker();
    echo "统一Worker创建成功\n";

    // 设置进程标题（Linux下有效）
    if (function_exists('cli_set_process_title')) {
        cli_set_process_title('workerman-automation');
    }

    // 启动所有Worker
    Worker::runAll();
    
} catch (\Throwable $e) {
    echo "启动失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "错误堆栈:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
