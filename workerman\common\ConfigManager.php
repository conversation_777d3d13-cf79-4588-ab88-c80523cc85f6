<?php
declare(strict_types=1);

namespace workerman\common;

use app\system\service\TenantConfigService;
use think\facade\Cache;

/**
 * Workerman配置管理器
 * 负责读取和管理各种配置信息
 */
class ConfigManager
{
    /**
     * 配置缓存前缀
     */
    private const CACHE_PREFIX = 'workerman:config:';
    
    /**
     * 缓存时间（秒）
     */
    private const CACHE_TTL = 300; // 5分钟
    
    /**
     * 租户配置服务实例
     */
    private static ?TenantConfigService $tenantConfigService = null;
    
    /**
     * 获取租户配置服务实例
     */
    private static function getTenantConfigService(): TenantConfigService
    {
        if (self::$tenantConfigService === null) {
            self::$tenantConfigService = TenantConfigService::getInstance();
        }
        return self::$tenantConfigService;
    }
    
    /**
     * 获取公海规则配置
     */
    public static function getSeaRuleConfig(int $tenantId = 0): array
    {
        $cacheKey = self::CACHE_PREFIX . "sea_rule:{$tenantId}";
        
        $config = Cache::get($cacheKey);
        if ($config === null) {
            // 临时设置租户ID（如果需要）
            $originalTenantId = get_tenant_id();
            if ($tenantId > 0 && $tenantId !== $originalTenantId) {
                // 这里需要根据实际情况处理租户切换
                // 暂时使用当前租户配置
            }
            
            $config = self::getTenantConfigService()->getInfo('sea_rule');
            
            // 设置默认值
            $config = array_merge([
                'sea_status' => 0,           // 公海功能开关
                'follow_days' => 15,         // 未跟进天数阈值
                'deal_days' => 30,           // 未成交天数阈值
                'cron_expression' => '0 * * * *', // 每小时执行
                'max_process_count' => 100   // 单次最大处理数量
            ], $config ?: []);
            
            Cache::set($cacheKey, $config, self::CACHE_TTL);
        }
        
        return $config;
    }
    
    /**
     * 获取提前通知配置
     */
    public static function getAdvanceNoticeConfig(int $tenantId = 0): array
    {
        $cacheKey = self::CACHE_PREFIX . "advance_notice:{$tenantId}";
        
        $config = Cache::get($cacheKey);
        if ($config === null) {
            $config = self::getTenantConfigService()->getInfo('advance_notice');
            
            // 设置默认值
            $config = array_merge([
                'notice_enabled' => false,                    // 启用提前通知
                'notify_days' => 3,                          // 提前通知天数
                'notice_channels' => ['site'],               // 通知方式
                'notice_target' => 'owner',                  // 通知对象
                'notice_frequency' => 'once',                // 通知频率
                'notice_template' => '您负责的客户 {{customer_name}} 将在 {{days}} 天后被回收到公海，请及时跟进。'
            ], $config ?: []);
            
            Cache::set($cacheKey, $config, self::CACHE_TTL);
        }
        
        return $config;
    }
    
    /**
     * 获取任务提醒配置
     */
    public static function getTaskReminderConfig(int $tenantId = 0): array
    {
        $cacheKey = self::CACHE_PREFIX . "task_reminder:{$tenantId}";
        
        $config = Cache::get($cacheKey);
        if ($config === null) {
            $config = self::getTenantConfigService()->getInfo('task_reminder');
            
            // 设置默认值
            $config = array_merge([
                'reminder_enabled' => false,         // 启用任务提醒
                'overdue_enabled' => true,           // 启用逾期提醒
                'overdue_frequency' => 'daily',      // 逾期提醒频率
                'due_soon_enabled' => true,          // 启用即将到期提醒
                'due_soon_days' => [1, 3, 7],       // 提前提醒天数
                'follow_up_enabled' => true,         // 启用跟进提醒
                'follow_up_advance_hours' => 2       // 跟进提前小时数
            ], $config ?: []);
            
            Cache::set($cacheKey, $config, self::CACHE_TTL);
        }
        
        return $config;
    }
    
    /**
     * 清除配置缓存
     */
    public static function clearConfigCache(string $configType = '', int $tenantId = 0): void
    {
        if ($configType) {
            $cacheKey = self::CACHE_PREFIX . "{$configType}:{$tenantId}";
            Cache::delete($cacheKey);
        } else {
            // 清除所有配置缓存
            $patterns = ['sea_rule', 'advance_notice', 'task_reminder'];
            foreach ($patterns as $pattern) {
                $cacheKey = self::CACHE_PREFIX . "{$pattern}:{$tenantId}";
                Cache::delete($cacheKey);
            }
        }
    }
    
    /**
     * 获取所有租户的配置（用于多租户处理）
     */
    public static function getAllTenantsConfig(string $configType): array
    {
        // 这里需要根据实际情况实现获取所有租户的逻辑
        // 暂时返回当前租户的配置
        $method = 'get' . ucfirst(str_replace('_', '', $configType)) . 'Config';
        if (method_exists(self::class, $method)) {
            return [get_tenant_id() => self::$method()];
        }
        
        return [];
    }
    
    /**
     * 验证配置的有效性
     */
    public static function validateConfig(string $configType, array $config): array
    {
        $errors = [];
        
        switch ($configType) {
            case 'sea_rule':
                if (!isset($config['follow_days']) || $config['follow_days'] < 1) {
                    $errors[] = '未跟进天数必须大于0';
                }
                if (!isset($config['deal_days']) || $config['deal_days'] < 1) {
                    $errors[] = '未成交天数必须大于0';
                }
                break;
                
            case 'advance_notice':
                if ($config['notice_enabled'] && (!isset($config['notify_days']) || $config['notify_days'] < 1)) {
                    $errors[] = '提前通知天数必须大于0';
                }
                break;
                
            case 'task_reminder':
                if ($config['reminder_enabled'] && $config['follow_up_enabled']) {
                    if (!isset($config['follow_up_advance_hours']) || $config['follow_up_advance_hours'] < 1) {
                        $errors[] = '跟进提前小时数必须大于0';
                    }
                }
                break;
        }
        
        return $errors;
    }
}
