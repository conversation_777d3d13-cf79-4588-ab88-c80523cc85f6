# 租户切换功能测试指南

## 功能概述

本系统为 system_admin 表中 id=1 的超级管理员提供了租户切换功能，允许其切换到任意租户进行管理操作。

## 核心问题分析

通过深入分析代码，发现了一个关键问题：

### 问题描述
- `should_apply_tenant_isolation()` 函数（位于 `app/common.php` 第1230-1235行）硬编码返回 `true`
- `TenantSwitchService::shouldApplyTenantIsolation()` 方法（位于 `app/common/service/TenantSwitchService.php` 第303-308行）也硬编码返回 `true`
- 这导致所有用户（包括超级管理员）都必须应用租户隔离，无法实现真正的租户切换功能

### 当前实现逻辑
1. 租户切换服务 `TenantSwitchService` 可以正常切换模式和租户ID
2. `get_effective_tenant_id()` 函数能正确返回有效的租户ID
3. 但是 `should_apply_tenant_isolation()` 始终返回 `true`，导致数据查询仍然被租户隔离限制

## 测试API接口

已在 `TenantSwitchTestController` 中添加了以下测试接口：

### 1. 基础状态测试
```
GET /api/tenant-switch-test/status
```
获取当前用户的租户切换状态信息。

### 2. 租户数据访问测试
```
GET /api/tenant-switch-test/tenant-data-access
```
测试在不同模式下的租户数据访问权限。

### 3. 租户管理页面数据测试
```
GET /api/tenant-switch-test/tenant-list-management
```
模拟租户管理页面的数据获取逻辑。

### 4. 切换并验证访问权限
```
POST /api/tenant-switch-test/switch-and-verify
参数: tenant_id (要切换到的租户ID)
```
执行租户切换并验证数据访问权限的变化。

### 5. 配置诊断
```
GET /api/tenant-switch-test/diagnose-config
```
诊断当前配置问题，分析租户切换功能的实现问题。

### 6. 租户管理页面集成测试
```
GET /api/tenant-switch-test/management-integration
```
测试租户切换功能与租户管理页面的集成可能性。

## 测试步骤

### 前提条件
1. 确保数据库中存在 system_admin 表的 id=1 记录（超级管理员）
2. 确保数据库中存在多个租户记录（system_tenant 表）
3. 使用 id=1 的超级管理员账号登录系统

### 测试流程

#### 步骤1：检查当前状态
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -X GET "http://your-domain/api/tenant-switch-test/status"
```

#### 步骤2：诊断配置问题
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -X GET "http://your-domain/api/tenant-switch-test/diagnose-config"
```

#### 步骤3：测试租户数据访问
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -X GET "http://your-domain/api/tenant-switch-test/tenant-data-access"
```

#### 步骤4：测试切换功能
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -X POST "http://your-domain/api/tenant-switch-test/switch-and-verify" \
     -d '{"tenant_id": 2}'
```

#### 步骤5：测试租户管理页面集成
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     -X GET "http://your-domain/api/tenant-switch-test/management-integration"
```

## 预期测试结果

### 当前状态（问题状态）
- `should_apply_tenant_isolation` 始终为 `true`
- 即使切换到系统模式，仍然应用租户隔离
- 数据访问仍然受到租户限制

### 修复后的预期状态
- 系统模式下：`should_apply_tenant_isolation` 为 `false`，可访问所有租户数据
- 租户模式下：`should_apply_tenant_isolation` 为 `true`，只能访问指定租户数据

## 修复建议

### 方案1：修改 should_apply_tenant_isolation 函数
修改 `app/common.php` 中的 `should_apply_tenant_isolation()` 函数：

```php
function should_apply_tenant_isolation(): bool
{
    // 检查是否为超级管理员且处于系统模式
    if (is_super_admin()) {
        $tenantSwitchService = app(TenantSwitchService::class);
        if ($tenantSwitchService->getCurrentMode() === 'system') {
            return false; // 系统模式下跳过租户隔离
        }
    }
    return true; // 其他情况应用租户隔离
}
```

### 方案2：修改 TenantSwitchService 的方法
同时修改 `app/common/service/TenantSwitchService.php` 中的 `shouldApplyTenantIsolation()` 方法。

## 租户管理页面集成方案

### UI改进建议
1. **模式指示器**：在页面顶部显示当前工作模式
2. **切换按钮**：为每个租户添加"切换到此租户"按钮
3. **返回系统模式**：提供快捷返回系统模式的按钮
4. **当前租户信息**：在租户模式下显示当前操作的租户信息

### 前端实现建议
在租户列表页面（`frontend/src/views/tenant/list.vue`）的操作列中添加切换按钮：

```vue
// 在操作列中添加
hasAuth('system:tenant:switch') &&
  h(ArtButtonTable, {
    text: '切换到此租户',
    type: 'primary',
    onClick: () => switchToTenant(row.id)
  })
```

## 注意事项

1. **安全性**：租户切换功能仅限于 system_admin 表 id=1 的超级管理员
2. **数据隔离**：修复后需要确保数据隔离逻辑正确工作
3. **缓存一致性**：切换后需要验证缓存状态的一致性
4. **日志记录**：所有切换操作都会被记录到日志中

## 测试数据准备

确保数据库中有以下测试数据：
1. system_admin 表中 id=1 的超级管理员记录
2. 至少2-3个 system_tenant 表的租户记录
3. 每个租户下有一些测试数据用于验证隔离效果
