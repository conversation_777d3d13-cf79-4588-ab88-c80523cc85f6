# Workerman自动化系统完整设计方案

## 📋 项目概述

基于现有ThinkPHP 8.0多租户企业管理系统，利用已安装的Workerman v4.2.1组件，实现客户自动回收公海、任务跟进提醒、消息队列自动化处理和WebSocket实时通知的完整解决方案。

**核心目标**：
- 🤖 客户自动回收公海，减少手动操作
- ⏰ 智能任务跟进提醒，避免遗漏
- 🚀 消息队列自动化处理，提升系统性能
- 📡 WebSocket实时通知，替代轮询机制
- 🏗️ 统一的Workerman架构，便于管理维护

## 🔍 现状深度分析

### ✅ 已有基础设施

#### 1. CRM客户管理系统
- **完整功能**：客户、线索、商机、合同管理
- **公海管理**：`CustomerSeaManagementService`完整实现
- **配置存储**：租户配置表`system_tenant_config`，group='sea_rule'
- **规则执行**：`executeSeaRules()`方法已实现，需要自动化调度

#### 2. 项目任务管理系统
- **任务管理**：`ProjectTask`和`ProjectTaskRecord`完整实现
- **跟进记录**：支持评论和跟进两种记录类型
- **状态管理**：完整的任务状态流转和进度跟踪
- **提醒需求**：缺少基于时间的自动提醒机制

#### 3. 消息通知中心
- **双重发送机制**：
  - **队列机制（异步）**：`NoticeMessageService::send()` → `notice_queue`表 → `NoticeQueueService::processQueue()`
  - **直接发送机制（同步）**：各Channel服务直接发送，即使不启动队列也能正常工作
- **完整的队列处理**：支持重试、批量处理、失败处理、状态管理
- **命令行工具**：`notice:process-queue`、`notice:process-delayed`
- **缺少自动化**：需要手动执行命令，不是持续处理

#### 4. 前端轮询机制
- **当前实现**：每10秒轮询`/api/notice/unread-count`接口
- **缓存优化**：后端1小时缓存，但仍有频繁查询
- **存在问题**：资源浪费、延迟性、服务器压力

#### 5. Workerman组件
- **版本**：v4.2.1，包含workerman/workerman、workerman/redis-queue、workerman/crontab
- **功能支持**：WebSocket、Timer、多进程管理
- **跨平台限制**：Windows下守护进程、信号处理等功能受限

## 🎯 核心功能设计

### 1. 客户自动回收公海系统

#### 1.1 配置管理（用户可视化配置）

**配置界面设计**：在现有公海配置页面添加Tab页，用户可自主配置所有参数

**存储位置**：`system_tenant_config`表，支持多组配置
```json
{
  // 基础公海规则配置
  "sea_rule": {
    "sea_status": 1,        // 公海功能开关 0关闭/1开启
    "follow_days": 15,      // 未跟进天数阈值
    "deal_days": 30,        // 未成交天数阈值
    "cron_expression": "0 * * * *",  // 执行频率
    "max_process_count": 100         // 单次最大处理数量
  },

  // 提前通知配置（用户可配置）
  "advance_notice": {
    "notice_enabled": true,           // 启用提前通知
    "notify_days": 3,                 // 提前通知天数
    "notice_channels": ["site", "email"], // 通知方式
    "notice_target": "owner",         // 通知对象：owner/manager/both
    "notice_frequency": "once",       // 通知频率：once/daily/every_2_days
    "notice_template": "您负责的客户 {{customer_name}} 将在 {{days}} 天后被回收到公海，请及时跟进。"
  },

  // 任务提醒配置（用户可配置）
  "task_reminder": {
    "reminder_enabled": true,         // 启用任务提醒
    "overdue_enabled": true,          // 启用逾期提醒
    "overdue_frequency": "daily",     // 逾期提醒频率
    "due_soon_enabled": true,         // 启用即将到期提醒
    "due_soon_days": [1, 3, 7],      // 提前提醒天数
    "follow_up_enabled": true,        // 启用跟进提醒
    "follow_up_advance_hours": 2      // 跟进提前小时数
  }
}
```

#### 1.2 用户配置界面设计

**前端Tab页结构**：
```vue
<!-- 在现有公海配置页面扩展 -->
<el-tabs v-model="activeTab" type="card">
  <!-- 原有公海规则配置 -->
  <el-tab-pane label="公海规则" name="sea_rule">
    <el-form :model="seaRuleForm">
      <el-form-item label="公海功能">
        <el-switch v-model="seaRuleForm.sea_status" />
      </el-form-item>
      <el-form-item label="未跟进天数">
        <el-input-number v-model="seaRuleForm.follow_days" :min="1" />
      </el-form-item>
      <el-form-item label="未成交天数">
        <el-input-number v-model="seaRuleForm.deal_days" :min="1" />
      </el-form-item>
    </el-form>
  </el-tab-pane>

  <!-- 新增：提前通知配置 -->
  <el-tab-pane label="提前通知" name="advance_notice">
    <el-form :model="noticeForm">
      <el-form-item label="启用提前通知">
        <el-switch v-model="noticeForm.notice_enabled" />
      </el-form-item>
      <el-form-item label="提前通知天数" v-if="noticeForm.notice_enabled">
        <el-input-number v-model="noticeForm.notify_days" :min="1" :max="30" />
        <span class="form-tip">客户即将被回收到公海前提前通知的天数</span>
      </el-form-item>
      <el-form-item label="通知方式" v-if="noticeForm.notice_enabled">
        <el-checkbox-group v-model="noticeForm.notice_channels">
          <el-checkbox label="site">站内信</el-checkbox>
          <el-checkbox label="email">邮件</el-checkbox>
          <el-checkbox label="sms">短信</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="通知对象" v-if="noticeForm.notice_enabled">
        <el-radio-group v-model="noticeForm.notice_target">
          <el-radio label="owner">客户负责人</el-radio>
          <el-radio label="manager">部门经理</el-radio>
          <el-radio label="both">负责人和经理</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="通知频率" v-if="noticeForm.notice_enabled">
        <el-select v-model="noticeForm.notice_frequency">
          <el-option label="仅通知一次" value="once" />
          <el-option label="每天通知" value="daily" />
          <el-option label="每2天通知" value="every_2_days" />
        </el-select>
      </el-form-item>
      <el-form-item label="通知模板" v-if="noticeForm.notice_enabled">
        <el-input
          v-model="noticeForm.notice_template"
          type="textarea"
          :rows="3"
          placeholder="您负责的客户 {{customer_name}} 将在 {{days}} 天后被回收到公海，请及时跟进。"
        />
        <div class="template-vars">
          <span>可用变量：</span>
          <el-tag size="small">{{customer_name}}</el-tag>
          <el-tag size="small">{{days}}</el-tag>
          <el-tag size="small">{{owner_name}}</el-tag>
          <el-tag size="small">{{last_follow_date}}</el-tag>
        </div>
      </el-form-item>
    </el-form>
  </el-tab-pane>

  <!-- 新增：任务提醒配置 -->
  <el-tab-pane label="任务提醒" name="task_reminder">
    <el-form :model="taskReminderForm">
      <el-form-item label="启用任务提醒">
        <el-switch v-model="taskReminderForm.reminder_enabled" />
      </el-form-item>

      <template v-if="taskReminderForm.reminder_enabled">
        <el-card class="reminder-type-card">
          <template #header>逾期任务提醒</template>
          <el-form-item label="启用逾期提醒">
            <el-switch v-model="taskReminderForm.overdue_enabled" />
          </el-form-item>
          <el-form-item label="提醒频率" v-if="taskReminderForm.overdue_enabled">
            <el-select v-model="taskReminderForm.overdue_frequency">
              <el-option label="每天提醒" value="daily" />
              <el-option label="每2天提醒" value="every_2_days" />
              <el-option label="每周提醒" value="weekly" />
            </el-select>
          </el-form-item>
        </el-card>

        <el-card class="reminder-type-card">
          <template #header>即将到期提醒</template>
          <el-form-item label="启用到期提醒">
            <el-switch v-model="taskReminderForm.due_soon_enabled" />
          </el-form-item>
          <el-form-item label="提前提醒天数" v-if="taskReminderForm.due_soon_enabled">
            <el-checkbox-group v-model="taskReminderForm.due_soon_days">
              <el-checkbox :label="1">1天前</el-checkbox>
              <el-checkbox :label="3">3天前</el-checkbox>
              <el-checkbox :label="7">7天前</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-card>

        <el-card class="reminder-type-card">
          <template #header>跟进计划提醒</template>
          <el-form-item label="启用跟进提醒">
            <el-switch v-model="taskReminderForm.follow_up_enabled" />
          </el-form-item>
          <el-form-item label="提前提醒时间" v-if="taskReminderForm.follow_up_enabled">
            <el-input-number
              v-model="taskReminderForm.follow_up_advance_hours"
              :min="1"
              :max="48"
            />
            <span>小时前</span>
          </el-form-item>
        </el-card>
      </template>
    </el-form>
  </el-tab-pane>
</el-tabs>
```

#### 1.3 后端API接口扩展

**扩展租户配置API**：
```php
// app/system/controller/TenantConfigController.php
class TenantConfigController extends BaseController
{
    /**
     * 获取CRM配置（包含公海规则、提前通知、任务提醒）
     */
    public function getCrmConfig(): Json
    {
        $tenantConfigService = TenantConfigService::getInstance();

        $config = [
            'sea_rule' => $tenantConfigService->getInfo('sea_rule'),
            'advance_notice' => $tenantConfigService->getInfo('advance_notice'),
            'task_reminder' => $tenantConfigService->getInfo('task_reminder')
        ];

        return $this->success('获取成功', $config);
    }

    /**
     * 保存CRM配置
     */
    public function saveCrmConfig(): Json
    {
        $params = $this->request->param();
        $tenantConfigService = TenantConfigService::getInstance();

        // 保存各个配置组
        if (isset($params['sea_rule'])) {
            $tenantConfigService->setInfo('sea_rule', $params['sea_rule']);
        }

        if (isset($params['advance_notice'])) {
            $tenantConfigService->setInfo('advance_notice', $params['advance_notice']);
        }

        if (isset($params['task_reminder'])) {
            $tenantConfigService->setInfo('task_reminder', $params['task_reminder']);
        }

        return $this->success('保存成功');
    }
}
```

#### 1.4 Workerman中的配置读取和自动化实现

```php
// 在UnifiedWorker中读取用户配置并执行
private function executeSeaRecycle(): void
{
    echo "[" . date('Y-m-d H:i:s') . "] 开始执行公海回收\n";

    $tenantConfigService = TenantConfigService::getInstance();

    // 读取公海规则配置
    $seaRuleConfig = $tenantConfigService->getInfo('sea_rule');
    if (empty($seaRuleConfig['sea_status'])) {
        echo "公海功能未启用，跳过执行\n";
        return;
    }

    // 读取提前通知配置
    $noticeConfig = $tenantConfigService->getInfo('advance_notice');

    // 如果启用了提前通知，先检查即将被回收的客户
    if (!empty($noticeConfig['notice_enabled'])) {
        $this->checkAdvanceNotice($noticeConfig);
    }

    // 执行公海回收
    $service = CustomerSeaManagementService::getInstance();
    $result = $service->executeSeaRules();

    echo "公海回收完成: " . json_encode($result) . "\n";

    // 如果有回收操作，通过WebSocket通知相关用户
    if ($result['success'] && !empty($result['data']['affected_users'])) {
        foreach ($result['data']['affected_users'] as $userId) {
            $this->pushToUser($userId, [
                'type' => 'sea_recycle_notification',
                'data' => [
                    'message' => '您的客户已被回收到公海',
                    'count' => $result['data']['recycled_count']
                ]
            ]);
        }
    }
}

/**
 * 检查并发送提前通知
 */
private function checkAdvanceNotice(array $config): void
{
    $notifyDays = $config['notify_days'] ?? 3;

    // 查询即将被回收的客户（在通知天数范围内）
    $customers = $this->getCustomersNearRecycle($notifyDays);

    foreach ($customers as $customer) {
        // 根据用户配置发送通知
        $this->sendAdvanceNotice($customer, $config);
    }
}

/**
 * 发送提前通知（使用系统通知模板）
 */
private function sendAdvanceNotice(array $customer, array $config): void
{
    $template = $config['notice_template'] ?? '您负责的客户 {{customer_name}} 将在 {{days}} 天后被回收到公海，请及时跟进。';

    // 替换用户自定义模板变量
    $content = str_replace([
        '{{customer_name}}',
        '{{days}}',
        '{{owner_name}}',
        '{{last_follow_date}}'
    ], [
        $customer['customer_name'],
        $customer['days_until_recycle'],
        $customer['owner_name'],
        $customer['last_follow_date']
    ], $template);

    // 根据配置确定通知对象
    $recipients = $this->getNoticeRecipients($customer, $config['notice_target']);

    // 使用系统通知模板发送
    $noticeService = NoticeDispatcherService::getInstance();
    $noticeService->send('system_notice', [
        'title' => '客户即将回收提醒',
        'content' => $content
    ], $recipients, [
        'channels' => $config['notice_channels']
    ]);

    // 实时推送WebSocket通知
    foreach ($recipients as $userId) {
        $this->pushToUser($userId, [
            'type' => 'advance_notice',
            'data' => [
                'customer_name' => $customer['customer_name'],
                'days' => $customer['days_until_recycle'],
                'message' => $content
            ]
        ]);
    }
}
```

### 2. 任务跟进提醒系统

#### 2.1 提醒类型设计
- **逾期提醒**：`due_date < NOW() AND status IN (1,2)`
- **即将到期**：`due_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 1 DAY)`
- **跟进计划**：基于`project_task_record.next_date`字段

#### 2.2 数据库扩展
```sql
CREATE TABLE `project_task_reminder` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `task_id` bigint(20) UNSIGNED NOT NULL COMMENT '任务ID',
    `reminder_type` varchar(20) NOT NULL COMMENT '提醒类型：overdue,due_soon,follow_up',
    `reminder_time` datetime NOT NULL COMMENT '提醒时间',
    `recipient_id` bigint(20) UNSIGNED NOT NULL COMMENT '接收人ID',
    `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态：0待发送，1已发送',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `idx_tenant_task` (`tenant_id`, `task_id`),
    KEY `idx_reminder_time` (`reminder_time`)
);
```

### 3. 消息队列自动化处理

#### 3.1 基于现有队列机制
- **利用现有**：`NoticeQueueService::processQueue()`方法
- **Timer调度**：每10秒处理一次，每次50条
- **延迟消息**：每5分钟处理一次延迟消息

```php
// 每10秒处理队列
Timer::add(10, function() {
    $queueService = NoticeQueueService::getInstance();
    $result = $queueService->processQueue(50);
    
    if ($result['success'] > 0 || $result['failed'] > 0) {
        echo "队列处理: 成功{$result['success']}, 失败{$result['failed']}\n";
    }
});
```

### 4. 消息通知模板统一方案

#### 4.1 使用现有系统通知模板

**模板信息**：
- **模板代码**：`system_notice`
- **模板名称**：系统通知
- **模板标题**：系统通知：${标题}
- **模板内容**：标题：${标题}\n内容：${内容}
- **支持变量**：`title`（标题）、`content`（内容）

#### 4.2 统一通知发送方案

**所有自动化通知统一使用`system_notice`模板**：

```php
// 统一的通知发送方法
private function sendSystemNotice(string $title, string $content, array $recipients, array $channels = ['site']): void
{
    $noticeService = NoticeDispatcherService::getInstance();
    $noticeService->send('system_notice', [
        'title' => $title,
        'content' => $content
    ], $recipients, [
        'channels' => $channels
    ]);
}

// 各种通知的具体实现
private function sendSeaRecycleNotice(int $userId, int $count): void
{
    $this->sendSystemNotice(
        '客户回收通知',
        "您负责的 {$count} 个客户已被系统自动回收到公海，请及时关注公海客户池。",
        [$userId]
    );
}

private function sendAdvanceNotice(array $customer, string $customContent): void
{
    $this->sendSystemNotice(
        '客户即将回收提醒',
        $customContent, // 用户自定义的模板内容
        $this->getNoticeRecipients($customer)
    );
}

private function sendTaskReminderNotice(string $type, string $message, int $userId): void
{
    $titles = [
        'overdue' => '任务逾期提醒',
        'due_soon' => '任务即将到期提醒',
        'follow_up' => '任务跟进提醒'
    ];

    $this->sendSystemNotice(
        $titles[$type] ?? '任务提醒',
        $message,
        [$userId]
    );
}
```

#### 4.3 方案优势

**技术优势**：
- ✅ **复用现有模板**：无需创建新的消息模板
- ✅ **统一风格**：所有系统通知保持一致的格式
- ✅ **简化维护**：模板修改时所有通知同步更新
- ✅ **灵活扩展**：支持多渠道发送（站内信、邮件、短信）

**用户体验**：
- ✅ **风格统一**：用户接收到的所有系统通知格式一致
- ✅ **易于识别**：明确的标题和内容结构
- ✅ **个性化内容**：支持用户自定义提前通知模板

### 5. WebSocket实时通知系统

#### 5.1 替代轮询机制
**当前问题**：
- 前端每10秒轮询`/api/notice/unread-count`
- 资源浪费、延迟性、服务器压力

**WebSocket方案**：
- 实时推送，0延迟
- 长连接，减少HTTP请求
- 支持桌面通知
- 与消息模板系统完美集成

#### 5.2 WebSocket与消息模板集成

**实现原理**：
1. **消息发送**：使用`system_notice`模板发送到消息队列
2. **队列处理**：Workerman Timer自动处理队列中的消息
3. **实时推送**：同时通过WebSocket推送实时通知
4. **双重保障**：确保用户既能收到持久化消息，又能获得实时提醒

#### 5.3 技术实现
```php
// WebSocket服务器集成到统一Worker中
$ws_worker = new Worker("websocket://0.0.0.0:8282");
$ws_worker->onMessage = function($connection, $data) {
    $message = json_decode($data, true);
    if ($message['type'] === 'auth') {
        $userId = $message['user_id'];
        $this->userConnections[$userId] = $connection;
    }
};

// 在消息处理完成后推送WebSocket通知
private function updateUnreadCounts(): void {
    foreach ($this->userConnections as $userId => $connection) {
        $unreadCount = NoticeMessageService::getInstance()->getUnreadCount($userId);
        $connection->send(json_encode([
            'type' => 'unread_count_update',
            'data' => ['count' => $unreadCount]
        ]));
    }
}
```

## 🏗️ 统一架构设计

### 核心设计理念
**统一的Workerman架构**：所有功能基于相同的技术栈，便于管理和维护。

### 架构组成
```
workerman/
├── start.php                    # 统一启动入口
├── workers/
│   ├── UnifiedWorker.php       # 统一Worker（推荐方案）
│   ├── SeaRecycleWorker.php    # 公海回收Worker（分离方案）
│   ├── TaskReminderWorker.php  # 任务提醒Worker（分离方案）
│   ├── NoticeQueueWorker.php   # 消息队列Worker（分离方案）
│   └── WebSocketWorker.php     # WebSocket服务Worker（分离方案）
├── common/
│   ├── WorkerBase.php          # 统一Worker基类
│   ├── TimerManager.php        # 定时器管理
│   └── ConfigManager.php       # 配置管理
└── logs/                       # 日志目录
```

### 统一Worker基类
```php
abstract class WorkerBase {
    protected Worker $worker;
    protected string $workerName;
    protected bool $enabled = true;

    abstract protected function onWorkerStart($worker);

    protected function log(string $message): void {
        echo "[" . date('Y-m-d H:i:s') . "] [{$this->workerName}] {$message}\n";
    }

    protected function isEnabled(): bool {
        // 从租户配置读取是否启用
        return $this->enabled;
    }
}
```

## 🚀 推荐实施方案：统一Worker架构

### 方案选择：单进程混合方案

**核心理念**：将WebSocket服务器和Timer定时任务集成在同一个Worker进程中，实现最佳的性能和简洁性。

### 统一Worker实现
```php
// workerman/workers/UnifiedWorker.php
<?php
use Workerman\Worker;
use Workerman\Lib\Timer;

class UnifiedWorker {
    private Worker $wsWorker;
    private array $userConnections = [];

    public function __construct() {
        // 创建WebSocket服务器
        $this->wsWorker = new Worker("websocket://0.0.0.0:8282");
        $this->wsWorker->name = 'UnifiedWorker';
        $this->wsWorker->count = 1;

        $this->wsWorker->onWorkerStart = [$this, 'onWorkerStart'];
        $this->wsWorker->onConnect = [$this, 'onConnect'];
        $this->wsWorker->onMessage = [$this, 'onMessage'];
        $this->wsWorker->onClose = [$this, 'onClose'];
    }

    public function onWorkerStart($worker) {
        echo "统一Worker启动，集成所有功能\n";

        // 1. 公海回收定时任务（每小时）
        Timer::add(3600, function() {
            $this->executeSeaRecycle();
        });

        // 2. 任务提醒定时任务（每30分钟）
        Timer::add(1800, function() {
            $this->processTaskReminders();
        });

        // 3. 消息队列处理（每10秒）
        Timer::add(10, function() {
            $this->processMessageQueue();
        });

        // 4. 延迟消息处理（每5分钟）
        Timer::add(300, function() {
            $this->processDelayedMessages();
        });

        echo "所有定时任务已启动\n";
    }

    // WebSocket连接处理
    public function onConnect($connection) {
        echo "WebSocket新连接: {$connection->id}\n";
    }

    public function onMessage($connection, $data) {
        $message = json_decode($data, true);

        if ($message['type'] === 'auth') {
            // 用户认证，建立连接映射
            $userId = $message['user_id'];
            $this->userConnections[$userId] = $connection;
            $connection->user_id = $userId;

            $connection->send(json_encode([
                'type' => 'auth_success',
                'message' => '认证成功'
            ]));

            // 立即发送当前未读数
            $unreadCount = NoticeMessageService::getInstance()->getUnreadCount($userId);
            $connection->send(json_encode([
                'type' => 'unread_count_update',
                'data' => ['count' => $unreadCount]
            ]));
        }
    }

    public function onClose($connection) {
        if (isset($connection->user_id)) {
            unset($this->userConnections[$connection->user_id]);
            echo "用户 {$connection->user_id} 断开连接\n";
        }
    }

    // 公海回收执行
    private function executeSeaRecycle(): void {
        echo "[" . date('Y-m-d H:i:s') . "] 开始执行公海回收\n";

        $tenantConfigService = TenantConfigService::getInstance();
        $seaRuleConfig = $tenantConfigService->getInfo('sea_rule');

        if (empty($seaRuleConfig['sea_status'])) {
            echo "公海功能未启用，跳过执行\n";
            return;
        }

        $service = CustomerSeaManagementService::getInstance();
        $result = $service->executeSeaRules();

        echo "公海回收完成: " . json_encode($result) . "\n";

        // 如果有回收操作，发送通知并推送WebSocket
        if ($result['success'] && !empty($result['data']['affected_users'])) {
            foreach ($result['data']['affected_users'] as $userId) {
                // 发送系统通知
                $noticeService = NoticeDispatcherService::getInstance();
                $noticeService->send('system_notice', [
                    'title' => '客户回收通知',
                    'content' => "您负责的 {$result['data']['recycled_count']} 个客户已被系统自动回收到公海，请及时关注公海客户池。"
                ], [$userId], [
                    'channels' => ['site']
                ]);

                // 推送WebSocket实时通知
                $this->pushToUser($userId, [
                    'type' => 'sea_recycle_notification',
                    'data' => [
                        'message' => '您的客户已被回收到公海',
                        'count' => $result['data']['recycled_count']
                    ]
                ]);
            }
        }
    }

    // 任务提醒处理（基于用户配置）
    private function processTaskReminders(): void {
        echo "[" . date('Y-m-d H:i:s') . "] 开始处理任务提醒\n";

        $tenantConfigService = TenantConfigService::getInstance();
        $reminderConfig = $tenantConfigService->getInfo('task_reminder');

        if (empty($reminderConfig['reminder_enabled'])) {
            echo "任务提醒功能未启用，跳过执行\n";
            return;
        }

        $service = new TaskReminderService();

        // 根据用户配置处理不同类型的提醒
        if ($reminderConfig['overdue_enabled']) {
            $overdueReminders = $service->processOverdueReminders($reminderConfig);
            $this->pushTaskReminders($overdueReminders, 'overdue');
        }

        if ($reminderConfig['due_soon_enabled']) {
            $dueSoonReminders = $service->processDueSoonReminders($reminderConfig);
            $this->pushTaskReminders($dueSoonReminders, 'due_soon');
        }

        if ($reminderConfig['follow_up_enabled']) {
            $followUpReminders = $service->processFollowUpReminders($reminderConfig);
            $this->pushTaskReminders($followUpReminders, 'follow_up');
        }

        echo "任务提醒处理完成\n";
    }

    /**
     * 推送任务提醒到WebSocket（使用系统通知模板）
     */
    private function pushTaskReminders(array $reminders, string $type): void {
        foreach ($reminders as $reminder) {
            // 发送消息通知
            $this->sendTaskReminderNotice($reminder, $type);

            // 推送WebSocket实时通知
            $this->pushToUser($reminder['user_id'], [
                'type' => 'task_reminder',
                'data' => [
                    'task_id' => $reminder['task_id'],
                    'task_name' => $reminder['task_name'],
                    'reminder_type' => $type,
                    'message' => $reminder['message'],
                    'due_date' => $reminder['due_date'] ?? null
                ]
            ]);
        }
    }

    /**
     * 发送任务提醒通知（使用系统通知模板）
     */
    private function sendTaskReminderNotice(array $reminder, string $type): void {
        $titles = [
            'overdue' => '任务逾期提醒',
            'due_soon' => '任务即将到期提醒',
            'follow_up' => '任务跟进提醒'
        ];

        $title = $titles[$type] ?? '任务提醒';

        // 使用系统通知模板发送
        $noticeService = NoticeDispatcherService::getInstance();
        $noticeService->send('system_notice', [
            'title' => $title,
            'content' => $reminder['message']
        ], [$reminder['user_id']], [
            'channels' => ['site'] // 默认发送站内信，可根据配置调整
        ]);
    }

    // 消息队列处理
    private function processMessageQueue(): void {
        $queueService = NoticeQueueService::getInstance();
        $result = $queueService->processQueue(50);

        // 如果处理了消息，实时更新用户未读数
        if ($result['success'] > 0) {
            $this->updateUnreadCounts();
        }

        if ($result['success'] > 0 || $result['failed'] > 0) {
            echo "[" . date('Y-m-d H:i:s') . "] 队列处理: 成功{$result['success']}, 失败{$result['failed']}\n";
        }
    }

    // 延迟消息处理
    private function processDelayedMessages(): void {
        $messageService = NoticeMessageService::getInstance();
        $count = $messageService->processDelayedMessages(100);

        if ($count > 0) {
            echo "[" . date('Y-m-d H:i:s') . "] 延迟消息处理: {$count}条\n";
            // 延迟消息处理后也需要更新未读数
            $this->updateUnreadCounts();
        }
    }

    // 向指定用户推送消息
    private function pushToUser(int $userId, array $message): bool {
        if (isset($this->userConnections[$userId])) {
            $this->userConnections[$userId]->send(json_encode($message));
            return true;
        }
        return false;
    }

    // 更新所有在线用户的未读数
    private function updateUnreadCounts(): void {
        foreach ($this->userConnections as $userId => $connection) {
            $unreadCount = NoticeMessageService::getInstance()->getUnreadCount($userId);
            $connection->send(json_encode([
                'type' => 'unread_count_update',
                'data' => ['count' => $unreadCount]
            ]));
        }
    }
}

// 启动统一Worker
new UnifiedWorker();
Worker::runAll();
```

### 启动脚本
```php
// workerman/start.php
<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/workers/UnifiedWorker.php';

// 启动统一Worker，包含所有功能
new UnifiedWorker();
Worker::runAll();
```

## 📱 前端WebSocket客户端实现

### Vue3 Composable
```typescript
// composables/useWebSocketNotification.ts
export function useWebSocketNotification() {
  const unreadCount = ref(0)
  const isConnected = ref(false)

  let ws: WebSocket | null = null
  let reconnectTimer: NodeJS.Timeout | null = null
  let heartbeatTimer: NodeJS.Timeout | null = null

  const connect = () => {
    try {
      ws = new WebSocket('ws://localhost:8282')

      ws.onopen = () => {
        console.log('WebSocket连接成功')
        isConnected.value = true

        // 发送认证信息
        const userInfo = getUserInfo()
        ws?.send(JSON.stringify({
          type: 'auth',
          user_id: userInfo.id,
          token: getToken()
        }))

        startHeartbeat()
      }

      ws.onmessage = (event) => {
        const message = JSON.parse(event.data)
        handleMessage(message)
      }

      ws.onclose = () => {
        console.log('WebSocket连接断开')
        isConnected.value = false
        stopHeartbeat()

        // 5秒后自动重连
        reconnectTimer = setTimeout(connect, 5000)
      }

    } catch (error) {
      console.error('WebSocket连接失败:', error)
    }
  }

  const handleMessage = (message: any) => {
    switch (message.type) {
      case 'unread_count_update':
        unreadCount.value = message.data.count
        break

      case 'task_reminder':
        // 任务提醒通知
        ElNotification({
          title: '任务提醒',
          message: message.data.message,
          type: 'warning'
        })
        break

      case 'sea_recycle_notification':
        // 公海回收通知
        ElNotification({
          title: '公海回收通知',
          message: message.data.message,
          type: 'info'
        })
        break
    }
  }

  const startHeartbeat = () => {
    heartbeatTimer = setInterval(() => {
      if (ws?.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'ping' }))
      }
    }, 30000)
  }

  return { unreadCount, isConnected, connect }
}
```

### 组件集成
```typescript
// 替换原有轮询逻辑
// frontend/src/components/core/layouts/art-header-bar/index.vue
import { useWebSocketNotification } from '@/composables/useWebSocketNotification'

const { unreadCount, isConnected, connect } = useWebSocketNotification()

onMounted(() => {
  // 建立WebSocket连接，替代轮询
  connect()
})

// 移除原有的轮询代码
// unreadCountInterval.value = setInterval(() => {
//   getUnreadMessageCount()
// }, 10000)
```

## 🖥️ 跨平台兼容性方案

### Windows与Linux差异分析

**Workerman在Windows系统限制**：
- ❌ 不支持守护进程模式（`-d`参数无效）
- ❌ 不支持进程信号处理（无法优雅重启）
- ❌ 多进程功能受限
- ❌ 定时器精度可能不如Linux

**解决方案**：平台自适应架构
```php
// workerman/common/PlatformAdapter.php
class PlatformAdapter {
    public static function isWindows(): bool {
        return PHP_OS_FAMILY === 'Windows';
    }

    public static function startWorker(): void {
        if (self::isWindows()) {
            echo "Windows环境：启动前台模式\n";
            // Windows下直接启动，不使用守护进程
            Worker::runAll();
        } else {
            echo "Linux环境：支持守护进程模式\n";
            // Linux下支持完整功能
            Worker::runAll();
        }
    }
}
```

### 部署策略
- **开发环境（Windows）**：前台运行，便于调试
- **生产环境（Linux）**：守护进程模式，稳定运行

## 📊 工作量与实施计划

### 开发工作量评估

| 功能模块 | 工作量 | 说明 |
|---------|--------|------|
| **统一Worker架构** | 1-2天 | 基于现有服务，主要是集成工作 |
| **公海自动回收** | 0.5天 | 调用现有`executeSeaRules()`方法 |
| **提前通知功能** | 1天 | 新增提前通知逻辑和模板处理 |
| **用户配置界面** | 1-2天 | 前端Tab页扩展和配置表单 |
| **配置API接口** | 0.5天 | 扩展租户配置API |
| **任务提醒系统** | 1-2天 | 需要新增提醒逻辑和数据表 |
| **消息队列自动化** | 0.5天 | 调用现有`processQueue()`方法 |
| **WebSocket实时通知** | 2-3天 | 服务端+客户端完整实现 |
| **前端改造** | 1天 | 替换轮询为WebSocket |
| **测试调试** | 1-2天 | 功能测试和稳定性测试 |
| **文档和部署** | 0.5天 | 部署文档和操作手册 |
| **总计** | **9-15天** | 根据团队经验可能有所调整 |

### 分阶段实施计划

#### 第一阶段：基础自动化（3-4天）
1. **统一Worker架构搭建**
2. **公海自动回收集成**
3. **消息队列自动化处理**
4. **基础测试验证**

#### 第二阶段：用户配置系统（2-3天）
1. **前端配置界面开发**（Tab页扩展、表单设计）
2. **后端配置API接口**（扩展租户配置）
3. **提前通知功能实现**（通知逻辑、模板处理）
4. **配置功能测试**

#### 第三阶段：任务提醒系统（2-3天）
1. **任务提醒数据表设计**
2. **提醒逻辑开发**（基于用户配置）
3. **集成到统一Worker**
4. **功能测试**

#### 第四阶段：WebSocket实时通知（3-4天）
1. **WebSocket服务端开发**
2. **前端客户端改造**
3. **消息推送集成**
4. **完整测试**

#### 第五阶段：优化完善（1-2天）
1. **性能优化**
2. **错误处理完善**
3. **监控和日志**
4. **部署文档**

## 🎯 核心优势与收益

### 技术优势
- ✅ **统一架构**：所有功能基于Workerman，技术栈一致
- ✅ **高性能**：常驻内存，避免重复启动开销
- ✅ **实时性**：WebSocket替代轮询，0延迟通知
- ✅ **资源效率**：单进程处理多种任务，资源利用率高
- ✅ **易维护**：统一的日志、错误处理和监控

### 业务价值
- 🚀 **效率提升**：自动化处理减少90%手动操作
- 📈 **用户体验**：实时通知，及时响应
- 💰 **成本降低**：减少服务器资源消耗
- 🔒 **稳定性**：7x24小时自动运行
- 📊 **可扩展**：为后续功能扩展奠定基础
- ⚙️ **个性化定制**：用户可自主配置各种提醒参数
- 🎯 **精准通知**：避免过度通知，提升通知效果

### 风险控制
- **降级方案**：WebSocket不可用时自动切换到轮询
- **监控告警**：进程状态、消息处理量、错误率监控
- **日志记录**：完整的操作日志和错误日志
- **配置热更新**：支持运行时配置修改

## 🚀 部署和运维

### 启动命令
```bash
# Linux环境（推荐）
php workerman/start.php start -d    # 守护进程启动
php workerman/start.php status      # 查看状态
php workerman/start.php stop        # 停止服务
php workerman/start.php restart     # 重启服务

# Windows环境
php workerman/start.php start       # 前台启动（不支持-d参数）
```

### 监控脚本
```bash
#!/bin/bash
# monitor.sh - 进程监控脚本
PROCESS_NAME="UnifiedWorker"
SCRIPT_PATH="/path/to/workerman/start.php"

if ! pgrep -f "$PROCESS_NAME" > /dev/null; then
    echo "$(date): $PROCESS_NAME 进程不存在，正在重启..."
    cd $(dirname $SCRIPT_PATH)
    php start.php start -d
    echo "$(date): $PROCESS_NAME 已重启"
fi
```

### 系统要求
- **PHP版本**：7.4+
- **扩展要求**：pcntl、posix（Linux）
- **内存要求**：建议512MB+
- **网络端口**：8282（WebSocket）

## 📋 总结

本方案基于深入的现状分析，充分利用现有的业务逻辑和技术基础，通过Workerman统一架构实现：

1. **客户自动回收公海**：基于现有`CustomerSeaManagementService`，支持用户自定义配置
2. **提前通知系统**：可配置的提前通知机制，避免客户意外流失
3. **任务跟进提醒**：新增智能提醒机制，支持多种提醒类型
4. **用户配置界面**：统一的Tab页配置，用户可自主调整所有参数
5. **消息队列自动化**：基于现有`NoticeQueueService`
6. **WebSocket实时通知**：替代轮询机制，提升用户体验

**核心特点**：
- 🎯 **实用性强**：基于现有代码，改动最小
- 🚀 **技术先进**：统一的Workerman架构
- ⚙️ **用户友好**：可视化配置界面，灵活自定义
- 💡 **易于实施**：9-15天完成全部开发
- 📈 **价值显著**：大幅提升自动化程度和用户体验

这是一个完整、实用、高效的企业级自动化解决方案。

---

**文档版本**：v2.0
**创建时间**：2025-01-03
**最后更新**：2025-01-03
**适用版本**：ThinkPHP 8.0 + Workerman 4.2.1
