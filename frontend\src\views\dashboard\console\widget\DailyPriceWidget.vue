<template>
  <div class="daily-price-widget art-custom-card">
    <div class="widget-header">
      <h3 class="widget-title">每日报价</h3>
      <span class="widget-count">({{ priceData.length }})</span>
    </div>

    <div v-loading="loading" class="widget-content">
      <div v-if="priceData.length === 0" class="empty-state">
        <el-empty description="暂无报价数据" :image-size="80" />
      </div>

      <div v-else class="price-list">
        <div
          v-for="price in priceData"
          :key="`price-${price.id}`"
          class="price-item"
          :data-type="'daily_price'"
          @click="handlePriceClick(price)"
        >
          <div class="price-content">
            <div class="price-header">
              <span class="price-title">{{ price.title }}</span>
            </div>
            <div class="price-meta">
              <span class="price-date">{{ price.price_date }}</span>
              <span class="price-count">{{ price.total_items }}个产品</span>
            </div>
          </div>
          <div class="price-actions">
            <el-icon class="action-icon">
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <DetailDialog
      v-model="detailDialogVisible"
      :detail-data="selectedPriceDetail"
      :loading="detailLoading"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import { ArrowRight } from '@element-plus/icons-vue'
  import { WorkbenchApi, type DailyPrice } from '@/api/dashboard/workbenchApi'
  import { DailyPriceOrderApi } from '@/api/daily/dailyPriceOrder'
  import DetailDialog from '@/views/daily/daily_price_order/detail-dialog.vue'
  import { ApiStatus } from '@/utils/http/status'

  // 响应式数据
  const priceData = ref<DailyPrice[]>([])
  const loading = ref(false)
  const detailDialogVisible = ref(false)
  const detailLoading = ref(false)
  const selectedPriceDetail = ref<any>({})

  /**
   * 加载每日报价数据
   */
  const loadDailyPrices = async () => {
    try {
      loading.value = true
      console.log('开始加载每日报价...')
      const res = await WorkbenchApi.getRecentDailyPrices()
      console.log('每日报价API响应:', res)

      if (res.code === 1) {
        priceData.value = res.data as DailyPrice[]
        console.log('每日报价数据:', priceData.value)
      } else {
        console.error('API返回错误:', res)
      }
    } catch (error) {
      console.error('加载每日报价失败:', error)
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理报价点击事件
   */
  const handlePriceClick = async (price: DailyPrice) => {
    try {
      detailLoading.value = true
      detailDialogVisible.value = true
      
      // 获取报价详情
      const res = await DailyPriceOrderApi.detail(price.id)
      if (res.code === ApiStatus.success) {
        selectedPriceDetail.value = res.data
      } else {
        console.error('获取报价详情失败:', res.message)
      }
    } catch (error) {
      console.error('获取报价详情失败:', error)
    } finally {
      detailLoading.value = false
    }
  }

  // 组件挂载时加载数据
  onMounted(() => {
    loadDailyPrices()
  })

  // 暴露刷新方法
  defineExpose({
    refresh: loadDailyPrices
  })
</script>

<style lang="scss" scoped>
  .daily-price-widget {
    height: 100%;
    display: flex;
    flex-direction: column;

    .widget-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;

      .widget-title {
        font-size: 16px;
        font-weight: 600;
        color: var(--art-text-color-1);
        margin: 0;
      }

      .widget-count {
        font-size: 12px;
        color: var(--art-text-color-3);
      }
    }

    .widget-content {
      flex: 1;
      overflow: hidden;

      .empty-state {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;
      }

      .price-list {
        height: 100%;
        overflow-y: auto;
      }

      .price-item {
        position: relative;
        padding: 12px 12px 12px 20px;
        margin-bottom: 8px;
        border-radius: 6px;
        border: 1px solid var(--el-border-color-light);
        cursor: pointer;
        transition: all 0.3s ease;
        background: var(--art-main-bg-color);
        display: flex;
        align-items: center;
        justify-content: space-between;

        // 左侧彩色条
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          width: 4px;
          height: 100%;
          border-radius: 6px 0 0 6px;
          background: #52C41A; // 绿色表示报价
        }

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          border-color: #52C41A;
          box-shadow: 0 2px 8px rgba(82, 196, 26, 0.2);
        }

        .price-content {
          flex: 1;

          .price-header {
            margin-bottom: 8px;

            .price-title {
              font-size: 14px;
              color: var(--art-text-color-1);
              font-weight: 500;
              line-height: 1.4;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
            }
          }

          .price-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 12px;
            color: var(--art-text-color-3);

            .price-date {
              display: flex;
              align-items: center;
              gap: 4px;
            }

            .price-count {
              display: flex;
              align-items: center;
              gap: 4px;
            }
          }
        }

        .price-actions {
          flex-shrink: 0;
          margin-left: 8px;

          .action-icon {
            font-size: 14px;
            color: var(--art-text-color-3);
            transition: color 0.3s ease;
          }
        }

        &:hover .action-icon {
          color: var(--art-text-color-1);
        }
      }
    }
  }
</style>
