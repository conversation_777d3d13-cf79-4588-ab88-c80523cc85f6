# 工作流任务页面终止功能修改总结

## 修改概述
将审批任务页面的终止功能从审批表单中移除，并在操作列的"更多"下拉菜单中体现。

## ✅ 问题修复
- **修复导入路径错误**：`@/stores/userStore` → `@/store/modules/user`
- **修复用户ID属性名**：`userStore.userInfo?.id` → `userStore.info?.userId`

## 主要修改内容

### 1. 移除审批表单中的终止功能
- ✅ 移除 `actionTypeConfig` 中的 `terminate` 配置
- ✅ 移除审批表单验证规则中的终止逻辑
- ✅ 移除审批操作处理中的终止分支
- ✅ 移除审批表单中的终止按钮和相关UI元素

### 2. 添加独立的终止功能
- ✅ 新增 `checkTerminatePermission()` 权限检查函数
- ✅ 新增 `terminateWorkflow()` 独立终止处理函数
- ✅ 新增 `handleMoreAction()` 更多操作处理函数
- ✅ 添加用户存储导入以获取当前用户ID

### 3. 在操作列添加更多操作菜单
- ✅ 在操作列添加 `ElDropdown` 下拉菜单
- ✅ 添加终止流程选项，带有危险色彩样式
- ✅ 添加必要的图标导入 (`ArrowDown`, `Close`)

## 权限控制逻辑

### 终止权限检查条件：
1. **基础权限**：用户必须有 `workflow:task:terminate` 权限
2. **业务权限**：满足以下任一条件
   - 是流程发起人 (`submitter_id` 匹配当前用户ID)
   - 是管理员 (有 `workflow:admin` 或 `system:admin` 权限)
3. **状态检查**：流程必须处于审批中状态 (`status === 1`)

## 终止流程交互
1. 用户点击"更多" → "终止流程"
2. 弹出输入框要求输入终止原因
3. 验证原因长度（5-500字符）
4. 调用 `WorkflowTaskApi.terminate()` 接口
5. 成功后刷新列表并显示成功消息

## API接口参数
```typescript
WorkflowTaskApi.terminate({
  instance_id: number,  // 工作流实例ID
  reason: string       // 终止原因
})
```

## 用户体验改进
- ✅ 审批表单更简洁，只包含审批相关功能
- ✅ 终止功能独立，操作更直观
- ✅ 权限控制更准确，只对有权限用户显示
- ✅ 终止原因输入验证，确保数据质量

## 后续可扩展功能
在"更多"菜单中可以继续添加其他管理操作：
- 转交任务
- 催办提醒
- 查看流程图
- 导出流程数据

## 注意事项
- 后端API接口保持不变
- 后端权限控制逻辑保持不变
- 只有审批中的流程才显示终止选项
- 终止操作需要输入原因，不能为空
