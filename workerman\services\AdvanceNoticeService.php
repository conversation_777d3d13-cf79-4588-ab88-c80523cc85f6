<?php
declare(strict_types=1);

namespace workerman\services;

use app\crm\model\CrmCustomer;
use app\notice\service\NoticeDispatcherService;
use think\facade\Db;
use think\facade\Log;
use workerman\common\WorkerBase;

/**
 * 提前通知服务
 * 负责检查即将被回收的客户并发送提前通知
 */
class AdvanceNoticeService extends WorkerBase
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct('AdvanceNoticeService');
    }
    
    /**
     * Worker启动回调（基类要求实现）
     */
    protected function onWorkerStart($worker): void
    {
        // 此服务不直接作为Worker运行，所以留空
    }
    
    /**
     * 执行提前通知检查
     */
    public function executeAdvanceNotice(array $config): array
    {
        if (empty($config['notice_enabled'])) {
            $this->log("提前通知功能未启用，跳过执行");
            return ['success' => true, 'message' => '功能未启用', 'data' => []];
        }
        
        $notifyDays = $config['notify_days'] ?? 3;
        $this->log("开始执行提前通知检查，提前天数: {$notifyDays}");
        
        try {
            // 1. 查询即将被回收的客户
            $customers = $this->getCustomersNearRecycle($notifyDays);
            $this->log("找到即将被回收的客户数量: " . count($customers));
            
            $sentCount = 0;
            $skipCount = 0;
            
            // 2. 为每个客户发送通知
            foreach ($customers as $customer) {
                if ($this->shouldSendNotice($customer['id'], $config['notice_frequency'])) {
                    $this->sendAdvanceNotice($customer, $config);
                    $this->recordNoticeLog($customer['id'], $customer['days_until_recycle']);
                    $sentCount++;
                } else {
                    $skipCount++;
                }
            }
            
            $this->log("提前通知执行完成", 'info', [
                'total_customers' => count($customers),
                'sent_count' => $sentCount,
                'skip_count' => $skipCount
            ]);
            
            return [
                'success' => true,
                'message' => "提前通知执行完成，发送{$sentCount}条通知",
                'data' => [
                    'total_customers' => count($customers),
                    'sent_count' => $sentCount,
                    'skip_count' => $skipCount
                ]
            ];
            
        } catch (\Throwable $e) {
            $this->log("提前通知执行异常: " . $e->getMessage(), 'error', [
                'exception' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '提前通知执行失败: ' . $e->getMessage(),
                'data' => []
            ];
        }
    }
    
    /**
     * 查询即将被回收的客户
     */
    private function getCustomersNearRecycle(int $notifyDays): array
    {
        // 这里需要根据公海规则来计算即将被回收的客户
        // 假设公海规则是：15天未跟进的客户会被回收
        $followDays = 15; // 这个值应该从配置中读取
        
        // 计算临界日期：距离回收还有 $notifyDays 天的客户
        // 即：最后跟进时间在 ($followDays - $notifyDays) 天前的客户
        $criticalDays = $followDays - $notifyDays;
        $criticalDate = date('Y-m-d H:i:s', strtotime("-{$criticalDays} days"));
        
        $customers = Db::table('crm_customer')
            ->alias('c')
            ->leftJoin('system_admin a', 'c.owner_user_id = a.id')
            ->where('c.in_sea', 0) // 不在公海中的客户
            ->where('c.owner_user_id', '>', 0) // 有负责人的客户
            ->where('c.status', 1) // 启用状态
            ->where(function($query) use ($criticalDate) {
                $query->whereNull('c.last_follow_time')
                      ->whereOr('c.last_follow_time', '<=', $criticalDate);
            })
            ->field([
                'c.id',
                'c.customer_name',
                'c.owner_user_id',
                'c.last_follow_time',
                'c.tenant_id',
                'a.real_name as owner_name'
            ])
            ->limit(100) // 限制处理数量
            ->select()
            ->toArray();
        
        // 计算距离回收的天数
        foreach ($customers as &$customer) {
            if (empty($customer['last_follow_time'])) {
                // 如果从未跟进，按创建时间计算
                $customer['days_until_recycle'] = $notifyDays;
            } else {
                $lastFollowTime = strtotime($customer['last_follow_time']);
                $recycleTime = $lastFollowTime + ($followDays * 24 * 3600);
                $daysUntilRecycle = ceil(($recycleTime - time()) / (24 * 3600));
                $customer['days_until_recycle'] = max(1, $daysUntilRecycle);
            }
            
            $customer['last_follow_date'] = $customer['last_follow_time'] 
                ? date('Y-m-d', strtotime($customer['last_follow_time']))
                : '从未跟进';
        }
        
        return $customers;
    }
    
    /**
     * 检查是否应该发送通知（防重复）
     */
    private function shouldSendNotice(int $customerId, string $frequency): bool
    {
        $today = date('Y-m-d');
        
        switch ($frequency) {
            case 'once':
                // 检查是否曾经发送过通知
                $count = Db::table('crm_advance_notice_log')
                    ->where('customer_id', $customerId)
                    ->count();
                return $count == 0;
                
            case 'daily':
                // 检查今天是否已发送
                $count = Db::table('crm_advance_notice_log')
                    ->where('customer_id', $customerId)
                    ->where('notice_date', $today)
                    ->count();
                return $count == 0;
                
            case 'every_2_days':
                // 检查最近2天是否已发送
                $twoDaysAgo = date('Y-m-d', strtotime('-2 days'));
                $count = Db::table('crm_advance_notice_log')
                    ->where('customer_id', $customerId)
                    ->where('notice_date', '>=', $twoDaysAgo)
                    ->count();
                return $count == 0;
                
            default:
                return true;
        }
    }
    
    /**
     * 发送提前通知
     */
    private function sendAdvanceNotice(array $customer, array $config): void
    {
        // 替换模板变量
        $template = $config['notice_template'] ?? '您负责的客户 {{customer_name}} 将在 {{days}} 天后被回收到公海，请及时跟进。';
        
        $content = str_replace([
            '{{customer_name}}',
            '{{days}}',
            '{{owner_name}}',
            '{{last_follow_date}}'
        ], [
            $customer['customer_name'],
            $customer['days_until_recycle'],
            $customer['owner_name'],
            $customer['last_follow_date']
        ], $template);
        
        // 确定通知对象
        $recipients = $this->getNoticeRecipients($customer, $config['notice_target'] ?? 'owner');
        
        // 发送系统通知
        try {
            $noticeService = NoticeDispatcherService::getInstance();
            $result = $noticeService->send('system', 'notice', [
                'title' => '客户即将回收提醒',
                'content' => $content
            ], $recipients);
            
            if ($result) {
                $this->log("提前通知发送成功", 'info', [
                    'customer_id' => $customer['id'],
                    'customer_name' => $customer['customer_name'],
                    'recipients' => $recipients
                ]);
            } else {
                $this->log("提前通知发送失败", 'error', [
                    'customer_id' => $customer['id'],
                    'customer_name' => $customer['customer_name']
                ]);
            }
            
        } catch (\Throwable $e) {
            $this->log("提前通知发送异常: " . $e->getMessage(), 'error', [
                'customer_id' => $customer['id'],
                'exception' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 获取通知接收人列表
     */
    private function getNoticeRecipients(array $customer, string $target): array
    {
        $recipients = [];
        
        switch ($target) {
            case 'owner':
                $recipients[] = $customer['owner_user_id'];
                break;
                
            case 'manager':
                // TODO: 获取部门经理ID
                // 这里需要根据实际的组织架构来实现
                $recipients[] = $customer['owner_user_id']; // 暂时发给负责人
                break;
                
            case 'both':
                $recipients[] = $customer['owner_user_id'];
                // TODO: 添加部门经理ID
                break;
                
            default:
                $recipients[] = $customer['owner_user_id'];
        }
        
        return array_unique($recipients);
    }
    
    /**
     * 记录通知日志
     */
    private function recordNoticeLog(int $customerId, int $daysBeforeRecycle): void
    {
        try {
            Db::table('crm_advance_notice_log')->insert([
                'customer_id' => $customerId,
                'notice_date' => date('Y-m-d'),
                'days_before_recycle' => $daysBeforeRecycle,
                'sent_at' => date('Y-m-d H:i:s'),
                'tenant_id' => 1 // 在Workerman中暂时使用固定租户ID
            ]);
        } catch (\Throwable $e) {
            $this->log("记录通知日志失败: " . $e->getMessage(), 'error');
        }
    }
}
