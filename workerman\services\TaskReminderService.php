<?php
declare(strict_types=1);

namespace workerman\services;

use app\project\model\ProjectTask;
use app\project\model\ProjectTaskRecord;
use app\crm\model\CrmFollowRecord;
use app\notice\service\NoticeDispatcherService;
use think\facade\Db;
use workerman\common\WorkerBase;

/**
 * 任务跟进提醒服务
 * 负责检查任务状态并发送相应的提醒通知
 */
class TaskReminderService extends WorkerBase
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        parent::__construct('TaskReminderService');
    }
    
    /**
     * Worker启动回调（基类要求实现）
     */
    protected function onWorkerStart($worker): void
    {
        // 此服务不直接作为Worker运行，所以留空
    }
    
    /**
     * 执行任务提醒检查
     */
    public function executeTaskReminder(array $config): array
    {
        if (empty($config['reminder_enabled'])) {
            $this->log("任务提醒功能未启用，跳过执行");
            return ['success' => true, 'message' => '功能未启用', 'data' => []];
        }
        
        $this->log("开始执行任务提醒检查");
        
        $results = [
            'overdue_reminders' => 0,
            'due_soon_reminders' => 0,
            'follow_up_reminders' => 0,
            'total_processed' => 0
        ];
        
        try {
            // 1. 逾期任务提醒
            if ($config['overdue_enabled']) {
                $overdueResult = $this->processOverdueReminders($config);
                $results['overdue_reminders'] = $overdueResult;
            }
            
            // 2. 即将到期任务提醒
            if ($config['due_soon_enabled']) {
                $dueSoonResult = $this->processDueSoonReminders($config);
                $results['due_soon_reminders'] = $dueSoonResult;
            }
            
            // 3. 跟进计划提醒
            if ($config['follow_up_enabled']) {
                $followUpResult = $this->processFollowUpReminders($config);
                $results['follow_up_reminders'] = $followUpResult;
            }
            
            $results['total_processed'] = $results['overdue_reminders'] + 
                                        $results['due_soon_reminders'] + 
                                        $results['follow_up_reminders'];
            
            $this->log("任务提醒检查完成", 'info', $results);
            
            return [
                'success' => true,
                'message' => "任务提醒执行完成，共发送{$results['total_processed']}条提醒",
                'data' => $results
            ];
            
        } catch (\Throwable $e) {
            $this->log("任务提醒执行异常: " . $e->getMessage(), 'error', [
                'exception' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '任务提醒执行失败: ' . $e->getMessage(),
                'data' => $results
            ];
        }
    }
    
    /**
     * 处理逾期任务提醒
     */
    private function processOverdueReminders(array $config): int
    {
        $this->log("开始处理逾期任务提醒");
        
        // 查询逾期任务
        $overdueTasks = $this->getOverdueTasks();
        $sentCount = 0;
        
        foreach ($overdueTasks as $task) {
            if ($this->shouldSendTaskReminder($task['id'], 'overdue', $config['overdue_frequency'])) {
                $this->sendTaskReminder($task, 'overdue');
                $this->recordTaskReminderLog($task['id'], 'overdue');
                $sentCount++;
            }
        }
        
        $this->log("逾期任务提醒处理完成", 'info', [
            'total_tasks' => count($overdueTasks),
            'sent_count' => $sentCount
        ]);
        
        return $sentCount;
    }
    
    /**
     * 处理即将到期任务提醒
     */
    private function processDueSoonReminders(array $config): int
    {
        $this->log("开始处理即将到期任务提醒");
        
        $dueSoonDays = $config['due_soon_days'] ?? [1, 3, 7];
        $sentCount = 0;
        
        foreach ($dueSoonDays as $days) {
            $dueSoonTasks = $this->getDueSoonTasks($days);
            
            foreach ($dueSoonTasks as $task) {
                if ($this->shouldSendTaskReminder($task['id'], "due_soon_{$days}", 'daily')) {
                    $task['days_until_due'] = $days;
                    $this->sendTaskReminder($task, 'due_soon');
                    $this->recordTaskReminderLog($task['id'], "due_soon_{$days}");
                    $sentCount++;
                }
            }
        }
        
        $this->log("即将到期任务提醒处理完成", 'info', ['sent_count' => $sentCount]);
        
        return $sentCount;
    }
    
    /**
     * 处理跟进计划提醒
     */
    private function processFollowUpReminders(array $config): int
    {
        $this->log("开始处理跟进计划提醒");
        
        $advanceHours = $config['follow_up_advance_hours'] ?? 2;
        $sentCount = 0;
        
        // 处理项目任务跟进提醒
        $taskFollowUps = $this->getTaskFollowUpReminders($advanceHours);
        foreach ($taskFollowUps as $followUp) {
            if ($this->shouldSendTaskReminder($followUp['task_id'], 'follow_up', 'daily')) {
                $this->sendFollowUpReminder($followUp, 'task');
                $this->recordTaskReminderLog($followUp['task_id'], 'follow_up');
                $sentCount++;
            }
        }
        
        // 处理CRM跟进提醒
        $crmFollowUps = $this->getCrmFollowUpReminders($advanceHours);
        foreach ($crmFollowUps as $followUp) {
            if ($this->shouldSendTaskReminder($followUp['id'], 'crm_follow_up', 'daily')) {
                $this->sendFollowUpReminder($followUp, 'crm');
                $this->recordTaskReminderLog($followUp['id'], 'crm_follow_up');
                $sentCount++;
            }
        }
        
        $this->log("跟进计划提醒处理完成", 'info', ['sent_count' => $sentCount]);
        
        return $sentCount;
    }
    
    /**
     * 获取逾期任务
     */
    private function getOverdueTasks(): array
    {
        return Db::table('project_task')
            ->alias('t')
            ->leftJoin('system_admin a', 't.assign_user_id = a.id')
            ->leftJoin('project p', 't.project_id = p.id')
            ->where('t.status', 'in', [1, 2]) // 进行中、待处理
            ->where('t.due_date', '<', date('Y-m-d H:i:s'))
            ->where('t.due_date', 'not null')
            ->field([
                't.id',
                't.task_name',
                't.assign_user_id',
                't.due_date',
                't.priority',
                't.tenant_id',
                'a.real_name as assign_user_name',
                'p.project_name'
            ])
            ->limit(100)
            ->select()
            ->toArray();
    }
    
    /**
     * 获取即将到期任务
     */
    private function getDueSoonTasks(int $days): array
    {
        $targetDate = date('Y-m-d', strtotime("+{$days} days"));
        
        return Db::table('project_task')
            ->alias('t')
            ->leftJoin('system_admin a', 't.assign_user_id = a.id')
            ->leftJoin('project p', 't.project_id = p.id')
            ->where('t.status', 'in', [1, 2]) // 进行中、待处理
            ->whereRaw("DATE(t.due_date) = '{$targetDate}'")
            ->field([
                't.id',
                't.task_name',
                't.assign_user_id',
                't.due_date',
                't.priority',
                't.tenant_id',
                'a.real_name as assign_user_name',
                'p.project_name'
            ])
            ->limit(50)
            ->select()
            ->toArray();
    }
    
    /**
     * 获取任务跟进提醒
     */
    private function getTaskFollowUpReminders(int $advanceHours): array
    {
        $targetTime = date('Y-m-d H:i:s', strtotime("+{$advanceHours} hours"));
        
        return Db::table('project_task_record')
            ->alias('r')
            ->leftJoin('project_task t', 'r.task_id = t.id')
            ->leftJoin('system_admin a', 't.assign_user_id = a.id')
            ->leftJoin('project p', 't.project_id = p.id')
            ->where('r.next_date', '<=', $targetTime)
            ->where('r.next_date', '>', date('Y-m-d H:i:s'))
            ->where('t.status', 'in', [1, 2])
            ->field([
                'r.id as record_id',
                'r.task_id',
                'r.next_date',
                't.task_name',
                't.assign_user_id',
                't.tenant_id',
                'a.real_name as assign_user_name',
                'p.project_name'
            ])
            ->limit(50)
            ->select()
            ->toArray();
    }
    
    /**
     * 获取CRM跟进提醒
     */
    private function getCrmFollowUpReminders(int $advanceHours): array
    {
        $targetTime = date('Y-m-d H:i:s', strtotime("+{$advanceHours} hours"));
        
        return Db::table('crm_follow_record')
            ->alias('f')
            ->leftJoin('crm_customer c', 'f.customer_id = c.id')
            ->leftJoin('system_admin a', 'c.owner_user_id = a.id')
            ->where('f.next_date', '<=', $targetTime)
            ->where('f.next_date', '>', date('Y-m-d H:i:s'))
            ->where('c.status', 1)
            ->field([
                'f.id',
                'f.customer_id',
                'f.next_date',
                'f.follow_content',
                'c.customer_name',
                'c.owner_user_id',
                'c.tenant_id',
                'a.real_name as owner_name'
            ])
            ->limit(50)
            ->select()
            ->toArray();
    }
    
    /**
     * 检查是否应该发送任务提醒
     */
    private function shouldSendTaskReminder(int $taskId, string $reminderType, string $frequency): bool
    {
        $today = date('Y-m-d');
        
        switch ($frequency) {
            case 'once':
                $count = Db::table('project_task_reminder_log')
                    ->where('task_id', $taskId)
                    ->where('reminder_type', $reminderType)
                    ->count();
                return $count == 0;
                
            case 'daily':
                $count = Db::table('project_task_reminder_log')
                    ->where('task_id', $taskId)
                    ->where('reminder_type', $reminderType)
                    ->where('reminder_date', $today)
                    ->count();
                return $count == 0;
                
            case 'weekly':
                $weekAgo = date('Y-m-d', strtotime('-7 days'));
                $count = Db::table('project_task_reminder_log')
                    ->where('task_id', $taskId)
                    ->where('reminder_type', $reminderType)
                    ->where('reminder_date', '>=', $weekAgo)
                    ->count();
                return $count == 0;
                
            default:
                return true;
        }
    }
    
    /**
     * 发送任务提醒
     */
    private function sendTaskReminder(array $task, string $type): void
    {
        $templates = [
            'overdue' => '您的任务"{{task_name}}"已逾期，截止时间：{{due_date}}，请尽快处理。',
            'due_soon' => '您的任务"{{task_name}}"将在{{days}}天后到期（{{due_date}}），请注意安排时间。'
        ];
        
        $template = $templates[$type] ?? '您有任务需要关注：{{task_name}}';
        
        $content = str_replace([
            '{{task_name}}',
            '{{due_date}}',
            '{{days}}',
            '{{project_name}}',
            '{{assign_user_name}}'
        ], [
            $task['task_name'],
            $task['due_date'] ?? '',
            $task['days_until_due'] ?? '',
            $task['project_name'] ?? '',
            $task['assign_user_name'] ?? ''
        ], $template);
        
        $title = $type === 'overdue' ? '任务逾期提醒' : '任务到期提醒';
        
        try {
            $noticeService = NoticeDispatcherService::getInstance();
            $result = $noticeService->send('system', 'notice', [
                'title' => $title,
                'content' => $content
            ], [$task['assign_user_id']]);
            
            if ($result) {
                $this->log("任务提醒发送成功", 'info', [
                    'task_id' => $task['id'],
                    'task_name' => $task['task_name'],
                    'type' => $type,
                    'recipient' => $task['assign_user_id']
                ]);
            }
            
        } catch (\Throwable $e) {
            $this->log("任务提醒发送异常: " . $e->getMessage(), 'error', [
                'task_id' => $task['id'],
                'exception' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 发送跟进提醒
     */
    private function sendFollowUpReminder(array $followUp, string $type): void
    {
        if ($type === 'task') {
            $content = "您有任务跟进计划：{$followUp['task_name']}，计划时间：{$followUp['next_date']}";
            $recipient = $followUp['assign_user_id'];
        } else {
            $content = "您有客户跟进计划：{$followUp['customer_name']}，计划时间：{$followUp['next_date']}";
            $recipient = $followUp['owner_user_id'];
        }
        
        try {
            $noticeService = NoticeDispatcherService::getInstance();
            $result = $noticeService->send('system', 'notice', [
                'title' => '跟进计划提醒',
                'content' => $content
            ], [$recipient]);
            
            if ($result) {
                $this->log("跟进提醒发送成功", 'info', [
                    'type' => $type,
                    'id' => $followUp['id'] ?? $followUp['task_id'],
                    'recipient' => $recipient
                ]);
            }
            
        } catch (\Throwable $e) {
            $this->log("跟进提醒发送异常: " . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 记录任务提醒日志
     */
    private function recordTaskReminderLog(int $taskId, string $reminderType): void
    {
        try {
            Db::table('project_task_reminder_log')->insert([
                'task_id' => $taskId,
                'reminder_type' => $reminderType,
                'reminder_date' => date('Y-m-d'),
                'sent_at' => date('Y-m-d H:i:s'),
                'tenant_id' => 1 // 在Workerman中暂时使用固定租户ID
            ]);
        } catch (\Throwable $e) {
            $this->log("记录任务提醒日志失败: " . $e->getMessage(), 'error');
        }
    }
}
