<?php
/**
 * ProjectProjectService 新增的统计方法
 * 基于模型关联查询的真实数据统计实现
 */

namespace app\project\service;

use app\project\model\ProjectProject;
use app\project\model\ProjectTask;
use app\project\model\ProjectTaskRecord;

class ProjectProjectService extends CrudService
{
    /**
     * 获取任务状态统计 - 真实数据版本
     */
    public function getTaskStatusStats($projectId)
    {
        // 状态配置
        $statusConfig = [
            1 => ['name' => '待办', 'color' => '#8C8C8C'],
            2 => ['name' => '进行中', 'color' => '#1664FF'], 
            3 => ['name' => '已完成', 'color' => '#00BC70'],
            4 => ['name' => '已关闭', 'color' => '#F54A45']
        ];
        
        // 获取项目实例
        $project = ProjectProject::find($projectId);
        if (!$project) {
            return [];
        }
        
        $result = [];
        
        // 使用模型关联查询各状态任务数量
        foreach ($statusConfig as $status => $config) {
            $count = $project->tasks()
                            ->where('status', $status)
                            ->count();
            
            // 只返回有数据的状态
            if ($count > 0) {
                $result[] = [
                    'name' => $config['name'],
                    'value' => $count,
                    'color' => $config['color']
                ];
            }
        }
        
        return $result;
    }
    
    /**
     * 获取任务优先级统计 - 真实数据版本
     */
    public function getTaskPriorityStats($projectId)
    {
        $priorityConfig = [
            1 => '低',
            2 => '中', 
            3 => '高'
        ];
        
        $project = ProjectProject::find($projectId);
        if (!$project) {
            return [];
        }
        
        $result = [];
        
        foreach ($priorityConfig as $priority => $name) {
            $count = $project->tasks()
                            ->where('priority', $priority)
                            ->count();
            
            if ($count > 0) {
                $result[] = [
                    'name' => $name,
                    'value' => $count
                ];
            }
        }
        
        return $result;
    }
    
    /**
     * 获取项目进度趋势 - 真实数据版本
     */
    public function getProgressTrend($projectId)
    {
        $project = ProjectProject::find($projectId);
        if (!$project) {
            return [];
        }
        
        // 方案1: 基于任务创建时间的月度完成率统计
        $tasks = $project->tasks()
                        ->field('created_at, status, completed_at')
                        ->order('created_at asc')
                        ->select();
        
        if ($tasks->isEmpty()) {
            return [];
        }
        
        // 按月份分组统计
        $monthlyData = [];
        foreach ($tasks as $task) {
            $month = date('Y-m', strtotime($task->created_at));
            
            if (!isset($monthlyData[$month])) {
                $monthlyData[$month] = ['total' => 0, 'completed' => 0];
            }
            
            $monthlyData[$month]['total']++;
            if ($task->status == 3) { // 已完成
                $monthlyData[$month]['completed']++;
            }
        }
        
        // 计算每月完成率
        $result = [];
        foreach ($monthlyData as $month => $data) {
            $progress = $data['total'] > 0 
                ? round(($data['completed'] / $data['total']) * 100, 2) 
                : 0;
                
            $result[] = [
                'date' => $month,
                'progress' => $progress
            ];
        }
        
        // 如果数据太少，补充一些历史月份
        if (count($result) < 3) {
            $result = $this->fillProgressTrendData($result);
        }
        
        return $result;
    }
    
    /**
     * 获取成员统计 - 真实数据版本
     */
    public function getMemberStats($projectId)
    {
        $project = ProjectProject::with(['members.user'])->find($projectId);
        if (!$project) {
            return [];
        }
        
        $result = [];
        
        foreach ($project->members as $member) {
            // 跳过无效用户
            if (!$member->user) {
                continue;
            }
            
            // 统计该成员在此项目中的任务
            $totalTasks = $project->tasks()
                                 ->where('assignee_id', $member->user_id)
                                 ->count();
            
            $completedTasks = $project->tasks()
                                    ->where('assignee_id', $member->user_id)
                                    ->where('status', 3)
                                    ->count();
            
            $completionRate = $totalTasks > 0 
                ? round(($completedTasks / $totalTasks) * 100, 2) 
                : 0;
            
            $result[] = [
                'id' => $member->user->id,
                'name' => $member->user->real_name,
                'avatar' => $member->user->avatar,
                'task_count' => $totalTasks,
                'completion_rate' => $completionRate
            ];
        }
        
        return $result;
    }
    
    /**
     * 获取最近活动 - 真实数据版本
     */
    public function getRecentActivities($projectId)
    {
        $project = ProjectProject::find($projectId);
        if (!$project) {
            return [];
        }
        
        // 获取项目所有任务ID
        $taskIds = $project->tasks()->column('id');
        if (empty($taskIds)) {
            return [];
        }
        
        // 查询最近的任务记录
        $records = ProjectTaskRecord::with(['task', 'creator'])
                                   ->whereIn('task_id', $taskIds)
                                   ->order('created_at desc')
                                   ->limit(10)
                                   ->select();
        
        $result = [];
        foreach ($records as $record) {
            if (!$record->task || !$record->creator) {
                continue;
            }
            
            $action = $this->getActionText($record->record_type);
            $time = $this->formatRelativeTime($record->created_at);
            
            $result[] = [
                'user' => $record->creator->real_name,
                'action' => $action,
                'target' => $record->task->title,
                'time' => $time
            ];
        }
        
        return $result;
    }
    
    /**
     * 优化版本：一次性获取所有统计数据
     */
    public function getAllProjectStats($projectId)
    {
        $project = ProjectProject::with([
            'tasks' => function($query) {
                $query->field('id,project_id,status,priority,assignee_id,created_at,completed_at');
            },
            'members.user' => function($query) {
                $query->field('id,real_name,avatar');
            }
        ])->find($projectId);
        
        if (!$project) {
            throw new \Exception('项目不存在');
        }
        
        return [
            'taskStatus' => $this->calculateTaskStatusStats($project->tasks),
            'taskPriority' => $this->calculateTaskPriorityStats($project->tasks),
            'progressTrend' => $this->calculateProgressTrend($project->tasks),
            'memberStats' => $this->calculateMemberStats($project->members, $project->tasks)
        ];
    }
    
    /**
     * 辅助方法：计算任务状态统计
     */
    private function calculateTaskStatusStats($tasks)
    {
        $statusConfig = [
            1 => ['name' => '待办', 'color' => '#8C8C8C'],
            2 => ['name' => '进行中', 'color' => '#1664FF'], 
            3 => ['name' => '已完成', 'color' => '#00BC70'],
            4 => ['name' => '已关闭', 'color' => '#F54A45']
        ];
        
        $statusCounts = [];
        foreach ($tasks as $task) {
            $statusCounts[$task->status] = ($statusCounts[$task->status] ?? 0) + 1;
        }
        
        $result = [];
        foreach ($statusConfig as $status => $config) {
            if (isset($statusCounts[$status])) {
                $result[] = [
                    'name' => $config['name'],
                    'value' => $statusCounts[$status],
                    'color' => $config['color']
                ];
            }
        }
        
        return $result;
    }
    
    /**
     * 辅助方法：计算任务优先级统计
     */
    private function calculateTaskPriorityStats($tasks)
    {
        $priorityConfig = [1 => '低', 2 => '中', 3 => '高'];
        $priorityCounts = [];
        
        foreach ($tasks as $task) {
            $priorityCounts[$task->priority] = ($priorityCounts[$task->priority] ?? 0) + 1;
        }
        
        $result = [];
        foreach ($priorityConfig as $priority => $name) {
            if (isset($priorityCounts[$priority])) {
                $result[] = [
                    'name' => $name,
                    'value' => $priorityCounts[$priority]
                ];
            }
        }
        
        return $result;
    }
    
    /**
     * 辅助方法：获取操作文本
     */
    private function getActionText($recordType)
    {
        $actionMap = [
            'comment' => '评论了任务',
            'follow' => '跟进了任务'
        ];
        
        return $actionMap[$recordType] ?? '更新了任务';
    }
    
    /**
     * 辅助方法：格式化相对时间
     */
    private function formatRelativeTime($datetime)
    {
        $time = strtotime($datetime);
        $now = time();
        $diff = $now - $time;
        
        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } elseif ($diff < 2592000) {
            return floor($diff / 86400) . '天前';
        } else {
            return date('Y-m-d', $time);
        }
    }
    
    /**
     * 辅助方法：补充进度趋势数据
     */
    private function fillProgressTrendData($existingData)
    {
        // 如果现有数据太少，生成最近6个月的数据
        $result = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = date('Y-m', strtotime("-{$i} months"));
            
            // 查找是否有现有数据
            $found = false;
            foreach ($existingData as $data) {
                if ($data['date'] == $month) {
                    $result[] = $data;
                    $found = true;
                    break;
                }
            }
            
            // 如果没有数据，添加默认值
            if (!$found) {
                $result[] = [
                    'date' => $month,
                    'progress' => 0
                ];
            }
        }
        
        return $result;
    }
}
?>
