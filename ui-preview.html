<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的待办 - UI设计方案预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .preview-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .design-option {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .design-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 2px solid #e8e8e8;
            padding-bottom: 10px;
        }
        
        /* 方案一：极简列表设计 */
        .minimal-list {
            width: 100%;
            max-width: 400px;
        }
        
        .widget-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 0 4px;
        }
        
        .widget-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .task-count {
            font-size: 14px;
            color: #666;
        }
        
        .task-list {
            space-y: 8px;
        }
        
        .task-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
            position: relative;
            overflow: hidden;
        }
        
        .task-item:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            transform: translateX(2px);
        }
        
        .task-item.urgent:hover {
            background: #fff2f0;
            border-color: #ff4d4f;
        }
        
        .task-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
        }
        
        .task-item.workflow::before { background: #FF8C00; }
        .task-item.crm::before { background: #1890FF; }
        .task-item.project::before { background: #52C41A; }
        .task-item.urgent::before { background: #FF4D4F; }
        
        .task-content {
            flex: 1;
            margin-left: 12px;
        }
        
        .task-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .task-meta {
            font-size: 12px;
            color: #666;
        }
        
        .task-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .urgent-badge {
            background: #ff4d4f;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
        }
        
        /* 方案二：卡片网格设计 */
        .card-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            width: 100%;
            max-width: 400px;
        }
        
        .task-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .task-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .task-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        
        .task-card.workflow::before { background: #FF8C00; }
        .task-card.crm::before { background: #1890FF; }
        .task-card.project::before { background: #52C41A; }
        .task-card.urgent::before { background: #FF4D4F; }
        
        .card-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .card-title {
            font-size: 13px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }
        
        .card-time {
            font-size: 11px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <!-- 方案一：极简列表设计 -->
        <div class="design-option">
            <h3 class="design-title">方案一：极简列表设计</h3>
            <div class="minimal-list">
                <div class="widget-header">
                    <span class="widget-title">我的待办</span>
                    <span class="task-count">(5)</span>
                </div>
                <div class="task-list">
                    <div class="task-item workflow">
                        <div class="task-content">
                            <div class="task-title">张三的请假申请</div>
                            <div class="task-meta">审批任务</div>
                        </div>
                        <div class="task-status">
                            <span>2小时前</span>
                            <span class="urgent-badge">⚠️</span>
                        </div>
                    </div>
                    
                    <div class="task-item crm">
                        <div class="task-content">
                            <div class="task-title">ABC公司客户回访</div>
                            <div class="task-meta">跟进任务</div>
                        </div>
                        <div class="task-status">
                            <span>今天</span>
                            <span>→</span>
                        </div>
                    </div>
                    
                    <div class="task-item project">
                        <div class="task-content">
                            <div class="task-title">项目A设计稿审核</div>
                            <div class="task-meta">项目任务</div>
                        </div>
                        <div class="task-status">
                            <span>明天</span>
                            <span>→</span>
                        </div>
                    </div>
                    
                    <div class="task-item urgent">
                        <div class="task-content">
                            <div class="task-title">合同到期提醒</div>
                            <div class="task-meta">重要提醒</div>
                        </div>
                        <div class="task-status">
                            <span>逾期</span>
                            <span style="color: #ff4d4f;">🔴</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 方案二：卡片网格设计 -->
        <div class="design-option">
            <h3 class="design-title">方案二：卡片网格设计</h3>
            <div class="widget-header">
                <span class="widget-title">我的待办</span>
                <span class="task-count">(4)</span>
            </div>
            <div class="card-grid">
                <div class="task-card workflow">
                    <div class="card-icon">📋</div>
                    <div class="card-title">张三的请假申请</div>
                    <div class="card-time">2小时前</div>
                </div>
                
                <div class="task-card crm">
                    <div class="card-icon">👥</div>
                    <div class="card-title">ABC公司客户回访</div>
                    <div class="card-time">今天</div>
                </div>
                
                <div class="task-card project">
                    <div class="card-icon">📝</div>
                    <div class="card-title">项目A设计稿审核</div>
                    <div class="card-time">明天</div>
                </div>
                
                <div class="task-card urgent">
                    <div class="card-icon">⚠️</div>
                    <div class="card-title">合同到期提醒</div>
                    <div class="card-time">逾期</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
