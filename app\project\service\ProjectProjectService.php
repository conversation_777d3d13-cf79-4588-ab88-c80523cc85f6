<?php
declare(strict_types=1);

namespace app\project\service;

use app\common\core\base\BaseService;
use app\common\exception\BusinessException;
use app\project\model\ProjectProject;
use app\project\model\ProjectMember;
use app\project\model\ProjectTask;
use app\project\model\ProjectTaskRecord;
use app\system\model\AdminModel;

/**
 * 项目表服务类
 */
class ProjectProjectService extends BaseService
{
	/**
	 * 构造函数
	 */
	public function __construct()
	{
		$this->model = new ProjectProject();
		parent::__construct();
		$this->crudService->setDataRangeField('owner_id');
	}
	
	/**
	 * 获取搜索字段配置
	 *
	 * @return array
	 */
	protected function getSearchFields(): array
	{
		return [
			
			'name' => ['type' => 'like'],
			
			'status' => ['type' => 'eq'],
			
			'priority' => ['type' => 'eq'],
			
			'start_date' => ['type' => 'between'],
			
			'end_date' => ['type' => 'between'],
			
			'owner_id' => ['type' => 'eq'],
			
			'is_archived' => ['type' => 'eq'],
		
		];
	}
	
	/**
	 * 获取验证规则 - 基于project_project表字段约束
	 *
	 * @param string $scene 场景
	 * @return array
	 */
	protected function getValidationRules(string $scene): array
	{
		// 基础规则
		$rules = [
			'name'              => 'require|max:100',
			'description'       => 'max:65535',
			'status'            => 'require|integer|in:1,2,3,4,5',
			'priority'          => 'integer|in:1,2,3,4',
			'start_date'        => 'date',
			'end_date'          => 'date',
			'actual_start_date' => 'date',
			'actual_end_date'   => 'date',
			'progress'          => 'float|between:0,100',
			'budget'            => 'float|egt:0',
			'actual_cost'       => 'float|egt:0',
			'owner_id'          => 'require|integer|gt:0',
			'department_id'     => 'integer|egt:0',
			'is_public'         => 'integer|in:0,1',
			'is_archived'       => 'integer|in:0,1',
			'tags'              => 'max:500',
			'cover_image'       => 'max:65535',
		];
		
		// 根据场景返回规则
		return match ($scene) {
			'add' => $rules,
			'edit' => $rules,
			default => [],
		};
	}
	
	/**
	 * 获取我的项目列表
	 */
	public function getMyProjects(int $userId, array $params = [])
	{
		// 获取我参与的项目ID列表
		$memberModel = new ProjectMember();
		$projectIds  = $memberModel->where('user_id', $userId)
		                           ->column('project_id');
		
		$page  = intval($params['page'] ?? 1);
		$limit = intval($params['limit'] ?? 10);
		$ids   = ProjectProject::where('owner_id', $userId)
		                       ->column('id');
		$where = [];
		if (!empty($projectIds)) {
			$ids = array_merge($ids, $projectIds);
			$ids = array_unique($ids);
		}
		
		if (!empty($ids)) {
			$where[] = [
				'id',
				'in',
				$ids
			];
		} else {
			// 如果用户没有任何项目，返回空结果
			return [
				'list' => [],
				'total' => 0,
				'page' => $page,
				'limit' => $limit,
				'last_page' => 1
			];
		}
		
		
		// 搜索条件
		if (!empty($params['name'])) {
			$where[] = [
				'name',
				'like',
				'%' . $params['name'] . '%'
			];
		}
		
		if (!empty($params['status'])) {
			$where[] = [
				'status',
				'=',
				$params['status']
			];
		}
		
		// 使用分页获取项目列表
		$result = $this->crudService->getPageList($where, ['id' => 'desc'], $page, $limit, ['owner']);

		// 为每个项目添加统计信息
		if (!empty($result['list'])) {
			foreach ($result['list'] as &$project) {
				$projectModel = ProjectProject::find($project['id']);
				if ($projectModel) {
					$stats = $projectModel->getProjectStats();
					$project['task_count'] = $stats['total_tasks'];
					$project['member_count'] = $stats['member_count'];
					$project['progress'] = $stats['progress'];
					$project['completed_tasks'] = $stats['completed_tasks'];
					$project['in_progress_tasks'] = $stats['in_progress_tasks'];
					$project['todo_tasks'] = $stats['todo_tasks'];
				}
			}
		}

		return $result;
	}
	
	/**
	 * 获取项目详情
	 */
	public function getProjectDetail(int $id)
	{
		$project = $this->crudService->getDetail($id);
		if ($project->isEmpty()) {
			throw new \Exception('项目不存在');
		}
		
		$stats = $project->getProjectStats();
		
		return [
			'project' => $project->toArray(),
			'stats'   => $stats
		];
	}
	
	/**
	 * 获取看板数据
	 */
	public function getKanbanData(int $projectId): array
	{
		$taskModel = new ProjectTask();
		
		$statuses = [
			[
				'id'    => 1,
				'name'  => '待办',
				'color' => '#8C8C8C'
			],
			[
				'id'    => 2,
				'name'  => '进行中',
				'color' => '#1664FF'
			],
			[
				'id'    => 3,
				'name'  => '已完成',
				'color' => '#00BC70'
			],
			[
				'id'    => 4,
				'name'  => '已关闭',
				'color' => '#F54A45'
			]
		];
		
		$kanbanData = [];
		foreach ($statuses as $status) {
			$tasks = $taskModel->where('project_id', $projectId)
			                   ->where('status', $status['id'])
			                   ->with(['assignee'])
			                   ->order('sort', 'asc')
			                   ->select()
			                   ->toArray();
			
			$kanbanData[] = [
				'status' => $status,
				'tasks'  => $tasks
			];
		}
		
		return $kanbanData;
	}
	
	/**
	 * 添加项目成员
	 */
	public function addProjectMember(int $projectId, int $userId, string $role = 'member'): bool
	{
		$project = $this->crudService->getDetail($projectId);
		if ($project->isEmpty()) {
			throw new \Exception('项目不存在');
		}
		
		return $project->addMember($userId, $role);
	}
	
	/**
	 * 移除项目成员
	 */
	public function removeProjectMember(int $projectId, int $userId): bool
	{
		$project = $this->crudService->getDetail($projectId);
		if ($project->isEmpty()) {
			throw new \Exception('项目不存在');
		}
		$member = (new ProjectMember())->where('project_id', $projectId)
		                               ->where('user_id', $userId)
		                               ->findOrEmpty();
		if ($member->isEmpty()) {
			throw new \Exception('项目成员不存在');
		}
		return $member->delete();
	}
	
	/**
	 * 获取任务状态统计 - 真实数据版本
	 */
	public function getTaskStatusStats($projectId)
	{
		// 状态配置
		$statusConfig = [
			1 => [
				'name'  => '待办',
				'color' => '#8C8C8C'
			],
			2 => [
				'name'  => '进行中',
				'color' => '#1664FF'
			],
			3 => [
				'name'  => '已完成',
				'color' => '#00BC70'
			],
			4 => [
				'name'  => '已关闭',
				'color' => '#F54A45'
			]
		];
		
		// 获取项目实例
		$project = ProjectProject::find($projectId);
		if (!$project) {
			return [];
		}
		
		// 先获取总任务数
		$totalTasks = $project->tasks()
		                      ->count();
		if ($totalTasks == 0) {
			return [];
		}
		
		$result = [];
		
		// 使用模型关联查询各状态任务数量
		foreach ($statusConfig as $status => $config) {
			$count = $project->tasks()
			                 ->where('status', $status)
			                 ->count();
			
			// 只返回有数据的状态
			if ($count > 0) {
				$percentage = round(($count / $totalTasks) * 100, 1);
				
				$result[] = [
					'name'       => $config['name'],
					'value'      => $count,
					'color'      => $config['color'],
					'percentage' => $percentage,
					'label'      => "{$config['name']}: {$count}个 ({$percentage}%)"
				];
			}
		}
		
		return $result;
	}
	
	/**
	 * 获取任务优先级统计 - 真实数据版本
	 */
	public function getTaskPriorityStats($projectId)
	{
		$priorityConfig = [
			1 => '低',
			2 => '中',
			3 => '高'
		];
		
		$project = ProjectProject::find($projectId);
		if (!$project) {
			return [];
		}
		
		// 获取总任务数
		$totalTasks = $project->tasks()
		                      ->count();
		if ($totalTasks == 0) {
			return [];
		}
		
		$result = [];
		
		foreach ($priorityConfig as $priority => $name) {
			$count = $project->tasks()
			                 ->where('priority', $priority)
			                 ->count();
			
			if ($count > 0) {
				$percentage = round(($count / $totalTasks) * 100, 1);
				
				$result[] = [
					'name'       => $name,
					'value'      => $count,
					'percentage' => $percentage,
					'label'      => "{$name}: {$count}个 ({$percentage}%)"
				];
			}
		}
		
		return $result;
	}
	
	/**
	 * 获取项目进度趋势 - 真实数据版本
	 */
	public function getProgressTrend($projectId)
	{
		$project = ProjectProject::find($projectId);
		if (!$project) {
			return [];
		}
		
		$tasks = $project->tasks()
		                 ->field('created_at, status, completed_at')
		                 ->order('created_at asc')
		                 ->select();
		
		if ($tasks->isEmpty()) {
			return $this->fillProgressTrendData([]);
		}
		
		// 按月份统计
		$monthlyData = [];
		foreach ($tasks as $task) {
			$month = date('Y-m', strtotime($task->created_at));
			if (!isset($monthlyData[$month])) {
				$monthlyData[$month] = [
					'total'     => 0,
					'completed' => 0
				];
			}
			
			$monthlyData[$month]['total']++;
			if ($task->status == 3) { // 已完成
				$monthlyData[$month]['completed']++;
			}
		}
		
		$result = [];
		foreach ($monthlyData as $month => $data) {
			$progress = $data['total'] > 0
				? round(($data['completed'] / $data['total']) * 100, 1)
				: 0;
			
			$result[] = [
				'date'     => $month,
				'progress' => $progress
			];
		}
		
		// 补充最近6个月数据
		if (count($result) < 6) {
			$result = $this->fillProgressTrendData($result);
		}
		
		return $result;
	}
	
	/**
	 * 获取成员统计 - 真实数据版本
	 */
	public function getMemberStats($projectId)
	{
		$project = ProjectProject::with(['members.user'])
		                         ->find($projectId);
		if (!$project) {
			return [];
		}
		
		// 获取当前用户ID
		$currentUserId = request()->adminId ?? 0;
		
		// 权限检查：超级管理员、租户管理员或项目所有者可以查看所有成员
		$canViewAll = is_super_admin() || is_tenant_super_admin() || $project->isOwner($currentUserId);
		
		$result = [];
		
		$members = $canViewAll
			? $project->members
			: $project->members()
			          ->where('user_id', $currentUserId)
			          ->select();
		foreach ($members as $member) {
			$userId = $member->user_id;
			// 统计该成员在此项目中的任务
			$totalTasks = $project->tasks()
			                      ->where('assignee_id', $userId)
			                      ->count();
			
			$completedTasks = $project->tasks()
			                          ->where('assignee_id', $userId)
			                          ->where('status', 3)
			                          ->count();
			
			$completionRate = $totalTasks > 0
				? round(($completedTasks / $totalTasks) * 100, 1)
				: 0;
			$user           = $member->user;
			$result[]       = [
				'id'              => $userId,
				'name'            => $user['real_name'] ?? '',
				'avatar'          => $user['avatar'] ?? '',
				'task_count'      => $totalTasks,
				'completion_rate' => $completionRate,
				'completed_tasks' => $completedTasks,
				'status_text'     => "已完成 {$completedTasks}/{$totalTasks} 个任务"
			];
		}
		
		// 按任务数量排序
		usort($result, function ($a, $b) {
			return $b['task_count'] - $a['task_count'];
		});
		
		return $result;
	}
	
	/**
	 * 获取最近活动 - 真实数据版本
	 */
	public function getRecentActivities($projectId)
	{
		$project = ProjectProject::find($projectId);
		if (!$project) {
			return [];
		}
		
		// 获取项目所有任务ID
		$taskIds = $project->tasks()
		                   ->column('id');
		if (empty($taskIds)) {
			return [];
		}
		
		// 查询最近的任务记录
		$records = ProjectTaskRecord::with([
			'task',
			'creator'
		])
		                            ->whereIn('task_id', $taskIds)
		                            ->order('created_at desc')
		                            ->limit(10)
		                            ->select();
		
		$result = [];
		foreach ($records as $record) {
			
			$action = $this->getActionText($record->record_type);
			$time   = $this->formatRelativeTime($record->created_at);
			
			$result[] = [
				'user'   => $record->creator_name,
				'action' => $action,
				'target' => $record->task->title,
				'time'   => $time
			];
		}
		
		return $result;
	}
	
	/**
	 * 辅助方法：获取操作文本
	 */
	private function getActionText($recordType)
	{
		$actionMap = [
			'comment' => '评论了任务',
			'follow'  => '跟进了任务'
		];
		
		return $actionMap[$recordType] ?? '更新了任务';
	}
	
	/**
	 * 辅助方法：格式化相对时间
	 */
	private function formatRelativeTime($datetime)
	{
		$time = strtotime($datetime);
		$now  = time();
		$diff = $now - $time;
		
		if ($diff < 60) {
			return '刚刚';
		}
		elseif ($diff < 3600) {
			return floor($diff / 60) . '分钟前';
		}
		elseif ($diff < 86400) {
			return floor($diff / 3600) . '小时前';
		}
		elseif ($diff < 2592000) {
			return floor($diff / 86400) . '天前';
		}
		else {
			return date('Y-m-d', $time);
		}
	}
	
	/**
	 * 辅助方法：补充进度趋势数据
	 */
	private function fillProgressTrendData($existingData)
	{
		$result = [];
		for ($i = 5; $i >= 0; $i--) {
			$month = date('Y-m', strtotime("-{$i} months"));
			
			$found = false;
			foreach ($existingData as $data) {
				if ($data['date'] == $month) {
					$result[] = $data;
					$found    = true;
					break;
				}
			}
			
			if (!$found) {
				$result[] = [
					'date'     => $month,
					'progress' => 0
				];
			}
		}
		
		return $result;
	}
	
	/**
	 * 获取项目成员选项（用于任务执行人选择）
	 *
	 * @param int $projectId 项目ID
	 * @param int $userId    当前用户ID
	 * @return array
	 */
	public function getMemberOptions(int $projectId, int $userId): array
	{
		// 验证项目是否存在
		$project = $this->crudService->getDetail($projectId);
		if (!$project) {
			throw new BusinessException('项目不存在');
		}
		
		// 检查权限：只有项目负责人可以选择执行人，非负责人只能看到自己
		$isOwner = $project->isOwner($userId);
		
		if ($isOwner) {
			// 项目负责人：获取所有项目成员
			$adminIds = (new ProjectMember())->where('project_id', $projectId)
			                                 ->where('deleted_at', null)
			                                 ->column('user_id');
		}
		else {
			// 非项目负责人：只能看到自己
			$adminIds = [$userId];
		}
		
		// 如果没有成员，返回空数组
		if (empty($adminIds)) {
			return [];
		}
		
		// 查询用户信息，返回ApiSelect需要的格式
		return (new AdminModel())->whereIn('id', $adminIds)
		                         ->where('deleted_at', null)
		                         ->field('id as value, real_name as name')
		                         ->select()
		                         ->toArray();
	}
	
	/**
	 * 检查用户是否为项目负责人
	 *
	 * @param int $projectId 项目ID
	 * @param int $userId    用户ID
	 * @return bool
	 */
	public function isProjectOwner(int $projectId, int $userId): bool
	{
		$project = $this->crudService->getDetail($projectId);
		if (!$project) {
			return false;
		}
		
		return $project->isOwner($userId);
	}
	
	/**
	 * 获取项目负责人选项列表
	 *
	 * @return array
	 */
	public function getOwnerOptions(): array
	{
		// 获取所有项目负责人的用户ID（去重）
		$ownerIds = $this->crudService->getModel()
		                              ->where('deleted_at', null)
		                              ->where('owner_id', '>', 0)
		                              ->column('owner_id');
		
		if (empty($ownerIds)) {
			return [];
		}
		
		// 去重
		$ownerIds = array_unique($ownerIds);
		
		// 查询用户信息，返回下拉选项格式
		return (new AdminModel())->whereIn('id', $ownerIds)
		                         ->where('deleted_at', null)
		                         ->field('id, real_name as name')
		                         ->select()
		                         ->toArray();
	}
}