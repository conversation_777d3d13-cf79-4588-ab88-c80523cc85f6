<?php
declare(strict_types=1);

namespace app\crm\controller\traits;

use app\crm\model\CrmContract;
use app\crm\model\CrmContractReceivable;
use app\workflow\service\UnifiedWorkflowService;
use app\workflow\model\WorkflowDefinition;
use app\workflow\model\WorkflowInstance;
use app\workflow\model\WorkflowType;
use think\facade\Db;
use think\response\Json;

/**
 * 客户回款操作Trait
 */
trait CustomerReceivableTrait
{
	/**
	 * 新增回款
	 */
	public function addReceivable(): Json
	{
		$data       = $this->request->post();
		$contractId = $data['contract_id'] ?? 0;
		
		if (!$contractId) {
			return $this->error('合同ID不能为空');
		}
		
		try {
			// 验证合同是否存在
			$contract = (new CrmContract())->findOrEmpty($contractId);
			if ($contract->isEmpty()) {
				return $this->error('合同不存在');
			}
			
			// 验证合同状态 - 只有已通过的合同才能添加回款
			if ($contract->approval_status !== 2) {
				return $this->error('只有审批通过的合同才能添加回款');
			}
			
			// 生成回款编号
			if (empty($data['receivable_number'])) {
				$data['receivable_number'] = $this->generateReceivableNo();
			}
			$data['owner_user_id'] = get_user_id();
			$receivableInfo        = CrmContractReceivable::where('receivable_number', $data['receivable_number'])
			                                              ->findOrEmpty();
			if (!$receivableInfo->isEmpty()) {
				return $this->error('回款编号已存在');
			}
			$data['approval_status']      = 0;
			$data['workflow_instance_id'] = 0;
			$res                          = $receivableInfo->saveByCreate($data);
			return $res
				? $this->success('添加成功', $res)
				: $this->error('添加失败');
			
		}
		catch (\Exception $e) {
			return $this->error('添加失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 编辑回款
	 */
	public function editReceivable(): Json
	{
		$data         = $this->request->post();
		$receivableId = $data['id'] ?? 0;
		
		if (!$receivableId) {
			return $this->error('回款ID不能为空');
		}
		
		try {
			$receivable = CrmContractReceivable::find($receivableId);
			if (!$receivable) {
				return $this->error('回款不存在');
			}
			
			$data['approval_status']      = 0;
			$data['workflow_instance_id'] = 0;
			$res                          = $receivable->saveByUpdate($data);
			
			return $res
				? $this->success('更新成功')
				: $this->error('更新失败');
			
		}
		catch (\Exception $e) {
			return $this->error('回款更新失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 删除回款
	 */
	public function deleteReceivable(): Json
	{
		$data         = $this->request->post();
		$receivableId = $data['id'] ?? 0;
		
		if (!$receivableId) {
			return $this->error('回款参数不能为空');
		}
		
		try {
			$receivable = CrmContractReceivable::find($receivableId);
			if (!$receivable) {
				return $this->error('回款不存在');
			}
			
			// 检查审批状态 - 只允许删除草稿状态
			if ($receivable->approval_status !== 0) {
				return $this->error('只有草稿状态的回款才能删除，其他状态请使用作废功能');
			}
			
			// 软删除
			$res = $receivable->delete();
			
			return $res
				? $this->success('删除成功')
				: $this->error('删除失败');
			
		}
		catch (\Exception $e) {
			return $this->error('删除失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 回款详情
	 */
	public function receivableDetail(): Json
	{
		$receivableId = $this->request->param('id', 0, 'int');
		
		if (!$receivableId) {
			return $this->error('回款ID不能为空');
		}
		
		try {
			$receivable = CrmContractReceivable::with([
				'creator'
			])
			                                   ->find($receivableId);
			
			if (!$receivable) {
				return $this->error('回款不存在');
			}
			
			return $this->success('获取成功', $receivable);
			
		}
		catch (\Exception $e) {
			return $this->error('获取回款详情失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 回款列表
	 */
	public function receivableList(): Json
	{
		$contractId = $this->request->param('contract_id', 0, 'int');
		$page       = $this->request->param('page', 1, 'int');
		$limit      = $this->request->param('limit', 15, 'int');
		
		if (!$contractId) {
			return $this->error('合同ID不能为空');
		}
		
		// 验证合同访问权限
		// $permissionService = app(CustomerPermissionService::class);
		// if (!$permissionService->validateContractAccess((int)$contractId, (int)get_user_id())) {
		//		return $this->error('无权限访问此合同');
		//	}
		
		try {
			$where = [
				'contract_id' => $contractId
			];
			
			// BaseModel已自动应用租户隔离，无需手动添加
			$crmContractReceivable = new CrmContractReceivable();
			// 统计总数 - BaseModel自动应用租户隔离
			$total = $crmContractReceivable->where($where)
			                               ->count();
			
			// 查询列表数据 - BaseModel自动应用租户隔离，包含工作流实例关联
			$list = $crmContractReceivable->with(['workflow'])
			                              ->where($where)
			                              ->order('created_at desc')
			                              ->page($page, $limit)
			                              ->select();
			
			return $this->success('获取成功', [
				'list'  => $list,
				'total' => $total,
				'page'  => $page,
				'limit' => $limit
			]);
			
		}
		
		catch (\Exception $e) {
			return $this->error('获取回款列表失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 提交回款审批
	 */
	public function submitReceivableApproval(): Json
	{
		$data         = $this->request->post();
		$receivableId = intval($data['id'] ?? 0);
		
		if (!$receivableId) {
			return $this->error('回款ID不能为空');
		}
		
		try {
			$receivable = CrmContractReceivable::with(['workflow'])
			                                   ->findOrEmpty($receivableId);
			if ($receivable->isEmpty()) {
				return $this->error('回款不存在');
			}
			
			// 检查工作流状态，只有草稿状态(0)或被驳回状态(3)的回款可以提交
			if ($receivable->workflow_instance_id && $receivable->workflow_status !== null) {
				if (!in_array($receivable->workflow_status, [
					0,
					5
				])) {
					return $this->error('当前状态不允许提交审批');
				}
			}
			
			// 使用统一工作流服务提交审批
			try {
				$unifiedWorkflowService = new UnifiedWorkflowService();
				$result                 = $unifiedWorkflowService->executeWorkflowOperation('submit', [
					'business_code' => 'crm_contract_receivable',
					'business_id'   => $receivableId,
					'operator_id'   => get_user_id()
				]);
				
				if (!$result) {
					return $this->error('提交审批失败');
				}
				
				return $this->success('提交成功');
			}
			catch (\Exception $e) {
				return $this->error('提交审批失败：' . $e->getMessage());
			}
			
		}
		catch (\Exception $e) {
			return $this->error('提交审批失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 撤回收款审批
	 */
	public function withdrawReceivableApproval(): Json
	{
		$data         = $this->request->post();
		$receivableId = intval($data['id'] ?? 0);
		
		if (!$receivableId) {
			return $this->error('回款ID不能为空');
		}
		
		try {
			$receivable = CrmContractReceivable::with(['workflow'])
			                                   ->find($receivableId);
			if (!$receivable) {
				return $this->error('回款不存在');
			}
			
			// 使用统一工作流服务撤回审批
			try {
				$unifiedWorkflowService = new UnifiedWorkflowService();
				$result                 = $unifiedWorkflowService->executeWorkflowOperation('withdraw', [
					'business_code' => 'crm_contract_receivable',
					'business_id'   => $receivableId,
					'operator_id'   => get_user_id(),
					'reason'        => '用户撤回申请'
				]);
				
				if (!$result) {
					return $this->error('撤回失败');
				}
				return $this->success('撤回成功');
			}
			catch (\Exception $e) {
				return $this->error('撤回失败：' . $e->getMessage());
			}
			
		}
		catch (\Exception $e) {
			return $this->error('撤回失败：' . $e->getMessage());
		}
	}
	
	/**
	 * 生成回款编号
	 */
	private function generateReceivableNo(): string
	{
		$prefix = 'HK';
		$date   = date('Ymd');
		$random = str_pad((string)mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
		
		return $prefix . $date . $random;
	}
}
