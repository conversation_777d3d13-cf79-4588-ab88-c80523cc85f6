<?php
declare(strict_types=1);

namespace app\workflow\controller;

use app\common\core\base\BaseController;
use app\workflow\service\WorkflowHistoryService;
use app\workflow\service\WorkflowInstanceService;
use app\workflow\service\WorkflowTaskService;
use think\response\Json;

/**
 * 工作流程任务表控制器
 */
class TaskController extends BaseController
{
	/**
	 * @var WorkflowTaskService
	 */
	protected WorkflowTaskService $service;
	
	/**
	 * 构造函数
	 */
	public function initialize(): void
	{
		parent::initialize();
		$this->service = WorkflowTaskService::getInstance();
	}
	
	/**
	 * 获取我的待办任务列表
	 */
	public function index(): Json
	{
		$result = $this->service->getWorkList($this->request->get());
		return $this->success('success', $result);
	}
	
	/**
	 * 获取任务详情
	 */
	public function detail(int $id): Json
	{
		$result = $this->service->getTaskDetail($id, [
			'instance' => function ($query) {
				$query->field('id,title,process_id,definition_id,submitter_id,submitter_dept_id,status,start_time,form_data,business_code')
				      ->with([
					      'definition' => function ($q) {
						      $q->field('id,name,icon');
					      },
					      'submitter',
					      'dept'       => function ($q) {
						      $q->field('id,name');
					      }
				      ]);
			}
		]);
		
		// 如果任务存在，重新获取按时间正序排序的历史记录
		if ($result->instance) {
			// 获取流程历史记录
			$historyService = new WorkflowHistoryService();
			$flowLogs       = $historyService->getList([
				'instance_id' => $result->instance_id
			], ['operation_time' => 'asc']); // 按时间正序排序
			
			// 将历史记录添加到实例数据中
			$result->instance->flowLogs = $flowLogs;
		}
		
		return $this->success('success', $result);
	}
	
	/**
	 * 审批通过任务
	 */
	public function approve(): Json
	{
		$params = $this->request->post();
		
		// 参数验证
		if (empty($params['task_id'])) {
			return $this->error('任务ID不能为空');
		}
		
		// 获取任务信息并验证权限
		$task = $this->service->getModel()
		                      ->where(['id' => $params['task_id']])
		                      ->findOrEmpty();
		if ($task->isEmpty()) {
			return $this->error('任务不存在');
		}
		
		// 检查当前用户是否有权限操作此任务
		if ($task['approver_id'] != $this->request->adminId && !$this->hasAdminPermission()) {
			return $this->error('无权操作此任务');
		}
		
		// 检查任务状态是否允许审批
		if ($task['status'] != 0) { // 只有待处理状态可以审批
			return $this->error('当前任务状态不允许审批');
		}
		
		// 审批通过任务
		$result = $this->service->approveTask($params);
		
		return $result
			? $this->success('审批通过成功')
			: $this->error('审批通过失败');
	}
	
	/**
	 * 拒绝任务
	 */
	public function reject(): Json
	{
		$params = $this->request->post();
		
		// 参数验证
		if (empty($params['task_id'])) {
			return $this->error('任务ID不能为空');
		}
		
		// 获取任务信息并验证权限
		$task = $this->service->getOne(['id' => $params['task_id']]);
		if ($task->isEmpty()) {
			return $this->error('任务不存在');
		}
		
		// 检查当前用户是否有权限操作此任务
		if ($task['approver_id'] != $this->request->adminId && !$this->hasAdminPermission()) {
			return $this->error('无权操作此任务');
		}
		
		// 检查任务状态是否允许拒绝
		if ($task['status'] != 0) { // 只有待处理状态可以拒绝
			return $this->error('当前任务状态不允许拒绝');
		}
		
		// 拒绝任务
		$result = $this->service->rejectTask($params);
		
		return $result
			? $this->success('拒绝成功')
			: $this->error('拒绝失败');
	}
	
	/**
	 * 转交任务
	 */
	public function transfer(): Json
	{
		$params = $this->request->post();
		
		// 参数验证
		if (empty($params['task_id'])) {
			return $this->error('任务ID不能为空');
		}
		
		if (empty($params['target_user_id'])) {
			return $this->error('接收人ID不能为空');
		}
		
		// 获取任务信息并验证权限
		$task = $this->service->getOne(['id' => $params['task_id']]);
		if ($task->isEmpty()) {
			return $this->error('任务不存在');
		}
		
		// 检查当前用户是否有权限操作此任务
		if ($task['approver_id'] != $this->request->adminId && !$this->hasAdminPermission()) {
			return $this->error('无权操作此任务');
		}
		
		// 检查任务状态是否允许转交
		if ($task['status'] != 0) { // 只有待处理状态可以转交
			return $this->error('当前任务状态不允许转交');
		}
		
		// 调用服务转交任务
		$result = $this->service->transferTask($params['task_id'], $params['target_user_id'], $this->request->adminId, $params['remark'] ?? '');
		
		return $result
			? $this->success('转交成功')
			: $this->error('转交失败');
	}
	
	/**
	 * todo 暂时不需要 回退到指定节点
	 */
	public function backTo(): Json
	{
		$params = $this->request->post();
		
		// 参数验证
		if (empty($params['instance_id'])) {
			return $this->error('流程实例ID不能为空');
		}
		
		if (empty($params['target_node_id'])) {
			return $this->error('目标节点ID不能为空');
		}
		
		// 验证当前用户是否有管理员权限
		if (!$this->hasAdminPermission()) {
			return $this->error('无权执行回退操作');
		}
		
		// 调用服务回退流程
		$result = $this->service->backToNode($params);
		
		return $result
			? $this->success('回退成功')
			: $this->error('回退失败');
	}
	
	/**
	 * 催办任务 todo 放入到申请控制器中
	 */
	public function urge(): Json
	{
		$params = $this->request->post();
		if (empty($params['instance_id'])) {
			return $this->error('流程实例ID不能为空');
		}
		
		// 获取流程实例信息
		$instanceService = WorkflowInstanceService::getInstance();
		$instance        = $instanceService->getOne(['id' => $params['instance_id']]);
		
		if ($instance->isEmpty()) {
			return $this->error('流程实例不存在');
		}
		
		// 检查当前用户是否有权限催办
		// 只有流程发起人或管理员可以催办
		if ($instance['submitter_id'] != $this->request->adminId && !$this->hasAdminPermission()) {
			return $this->error('无权催办此流程');
		}
		
		$result = $this->service->urgeTask($params);
		
		return $result
			? $this->success('催办成功')
			: $this->error('催办失败，可能没有待处理的任务');
	}
	
	/**
	 * 终止流程
	 */
	public function terminate(): Json
	{
		$params = $this->request->post();
		
		// 参数验证
		if (empty($params['instance_id'])) {
			return $this->error('流程实例ID不能为空');
		}
		
		if (empty($params['reason'])) {
			return $this->error('终止原因不能为空');
		}
		
		// 获取流程实例信息
		$instanceService = WorkflowInstanceService::getInstance();
		$instance        = $instanceService->getModel()
		                                   ->where(['id' => $params['instance_id']])
		                                   ->findOrEmpty();
		
		if ($instance->isEmpty()) {
			return $this->error('流程实例不存在');
		}
		
		// 检查当前用户是否有权限终止流程
		// 只有流程发起人或管理员可以终止流程
		/*$hasPermission = false;
		
		// 流程发起人可以终止
		if ($instance['submitter_id'] == $this->request->adminId) {
			$hasPermission = true;
		}
		
		// 管理员可以终止
		if ($this->hasAdminPermission()) {
			$hasPermission = true;
		}*/
		
		
		/*if (!$hasPermission) {
			return $this->error('无权终止此流程');
		}*/
		
		// 调用服务终止流程
		$result = $this->service->terminateWorkflow($params);
		
		return $result
			? $this->success('流程终止成功')
			: $this->error('流程终止失败');
	}
	
	/**
	 * 检查当前用户是否具有管理员权限
	 *
	 * @return bool
	 */
	protected function hasAdminPermission(): bool
	{
		// 可以根据实际情况检查用户是否是管理员
		// 例如检查角色ID、权限标识等
		return is_super_admin();
	}
	
	/**
	 * 获取流程历史记录
	 */
	public function getHistory(int $instanceId): Json
	{
		// 参数验证
		if (empty($instanceId)) {
			return $this->error('流程实例ID不能为空');
		}
		
		// 获取流程历史记录
		$historyService = new WorkflowHistoryService();
		$flowLogs       = $historyService->getList([
			'instance_id' => $instanceId
		], ['operation_time' => 'asc']); // 按时间正序排序
		
		return $this->success('success', $flowLogs);
	}
} 