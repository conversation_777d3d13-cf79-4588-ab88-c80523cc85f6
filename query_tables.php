<?php
// 查询项目管理相关表结构的脚本

// 数据库连接配置
$host = '*************';
$port = 3306;
$username = 'www_bs_com';
$password = 'PdadjMXmNy8Pn9tj';
$database = 'www_bs_com';

try {
    $pdo = new PDO("mysql:host=$host;port=$port;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 数据库连接成功 ===\n\n";
    
    // 要查询的表
    $tables = [
        'project_project',
        'project_task', 
        'project_member',
        'project_task_comment',
        'project_task_log'
    ];
    
    foreach ($tables as $table) {
        echo "=== 表: $table ===\n";
        
        // 检查表是否存在
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        
        if ($stmt->rowCount() == 0) {
            echo "表 $table 不存在\n\n";
            continue;
        }
        
        // 查询表结构
        $stmt = $pdo->query("DESCRIBE $table");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "字段结构:\n";
        printf("%-20s %-30s %-10s %-10s %-10s %-20s\n", 
               "Field", "Type", "Null", "Key", "Default", "Extra");
        echo str_repeat("-", 100) . "\n";
        
        foreach ($columns as $column) {
            printf("%-20s %-30s %-10s %-10s %-10s %-20s\n",
                   $column['Field'],
                   $column['Type'],
                   $column['Null'],
                   $column['Key'],
                   $column['Default'] ?? 'NULL',
                   $column['Extra']
            );
        }
        
        // 查询表数据量
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "\n数据量: $count 条记录\n";
        
        // 如果有数据，显示几条示例
        if ($count > 0) {
            echo "\n示例数据 (前3条):\n";
            $stmt = $pdo->query("SELECT * FROM $table LIMIT 3");
            $samples = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($samples)) {
                // 显示字段名
                $fields = array_keys($samples[0]);
                echo implode(" | ", array_map(function($field) {
                    return str_pad($field, 15);
                }, $fields)) . "\n";
                echo str_repeat("-", count($fields) * 17) . "\n";
                
                // 显示数据
                foreach ($samples as $row) {
                    echo implode(" | ", array_map(function($value) {
                        return str_pad(substr($value ?? 'NULL', 0, 15), 15);
                    }, $row)) . "\n";
                }
            }
        }
        
        echo "\n" . str_repeat("=", 80) . "\n\n";
    }
    
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
}
?>
