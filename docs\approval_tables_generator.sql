-- 企业审批系统数据表设计（CRUD生成器兼容版本）
-- 适用于150人以下企业规模
-- 设计时间：2025-07-25
-- 
-- 表结构说明：
-- 1. 独立场景审批：出库、入库、出货、采购、付款
-- 2. 通用业务审批：样品邮寄、报销、出差、外出
-- 3. 集成工作流系统，统一审批状态管理
-- 4. 支持多租户数据隔离
-- 5. 包含完整的CRUD生成器注释
--
-- 审批状态枚举：
-- 0=草稿, 1=审批中, 2=已通过, 3=已拒绝, 4=已终止, 5=已撤回, 6=已作废
--
-- 生成器命令示例：
-- php think generator:crud ims_outbound_approval --module=ims --frontend --overwrite
-- php think generator:crud business_sample_mail --module=business --frontend --overwrite

-- =====================================================================================
-- 1. 独立场景审批表
-- =====================================================================================

-- 出库申请表
-- 生成器命令: php think generator:crud ims_outbound_approval --module=ims --frontend --overwrite
DROP TABLE IF EXISTS `ims_outbound_approval`;
CREATE TABLE `ims_outbound_approval`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '出库申请ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
    `outbound_no`          varchar(50)         NOT NULL DEFAULT '' COMMENT '出库单号 | @required @max:50 @s=like @e @exp @imp',
    `outbound_type`        tinyint(1)          NOT NULL DEFAULT 1 COMMENT '出库类型:1=销售出库,2=调拨出库,3=其他出库 | @required @s=eq @e @exp @imp @fmt=tag',
    `dept_id`              bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '所在部门ID | @required @s=eq @e @exp @imp @c=select',
    `warehouse_id`         bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '出库仓库ID | @required @s=eq @e @exp @imp @c=select',
    `customer_id`          bigint(20) unsigned          DEFAULT 0 COMMENT '客户ID（销售出库时使用） | @s=eq @e @exp @imp @c=select',
#     `target_warehouse_id`  bigint(20) unsigned DEFAULT 0 COMMENT '目标仓库ID（调拨出库时使用） | @s=eq @e @exp @imp @c=select',
    `outbound_date`        date                NOT NULL COMMENT '出库日期 | @required @s=date @e @exp @imp @fmt=date',
    `total_amount`         decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '出库总金额 | @number @component:currency @s=between @e @exp @imp',
    `total_amount_upper`   varchar(200)                 DEFAULT '' COMMENT '出库总金额大写 | @max:200 @e @exp @imp @h',
    `total_quantity`       decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '出库总数量 | @number @s=between @e @exp @imp',
    `logistics_company`    varchar(100)                 DEFAULT '' COMMENT '物流公司 | @max:100 @s=like @e @exp @imp',
    `tracking_no`          varchar(100)                 DEFAULT '' COMMENT '物流单号 | @max:100 @s=like @e @exp @imp',
    `vehicle_info`         varchar(200)                 DEFAULT '' COMMENT '运输车辆信息 | @max:200 @s=like @e @exp @imp @form:textarea',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_outbound_no` (`outbound_no`),
    KEY `idx_outbound_date` (`outbound_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='出库申请表 @module:ims @exp:true @imp:true';

-- 出库明细表
-- 生成器命令: php think generator:crud ims_outbound_item --module=ims --frontend --overwrite
DROP TABLE IF EXISTS `ims_outbound_item`;
CREATE TABLE `ims_outbound_item`
(
    `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '明细ID | @h',
    `tenant_id`    bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',
    `supplier_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID @required @search:eq @exp @imp',
    `outbound_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '出库申请ID | @required @s=eq @e @exp @imp',
    `product_id`   bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '产品ID | @required @s=eq @e @exp @imp @c=select',
    `quantity`     decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '出库数量 | @required @number @min:0.01 @s=between @e @exp @imp',
    `unit_price`   decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '单价 | @required @number @component:currency @s=between @e @exp @imp',
    `total_amount` decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '小计金额 | @number @component:currency @s=between @e @exp @imp',
    `remark`       varchar(255)                 DEFAULT '' COMMENT '备注 | @max:255 @e @exp @imp @form:textarea',

    -- 系统字段
    `created_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`   datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_outbound_id` (`outbound_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='出库明细表 @module:ims @exp:true @imp:true';

-- todo 暂时不需要 入库申请表
-- 生成器命令: php think generator:crud ims_inbound_approval --module=ims --frontend --overwrite
DROP TABLE IF EXISTS `ims_inbound_approval`;
CREATE TABLE `ims_inbound_approval`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '入库申请ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
    `inbound_no`           varchar(50)         NOT NULL DEFAULT '' COMMENT '入库单号 | @required @max:50 @s=like @e @exp @imp',
    `inbound_type`         tinyint(1)          NOT NULL DEFAULT 1 COMMENT '入库类型:1=采购入库,2=调拨入库,3=退货入库,4=其他入库 | @required @s=eq @e @exp @imp @fmt=tag',
    `warehouse_id`         bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '入库仓库ID | @required @s=eq @e @exp @imp @c=select',
    `supplier_id`          bigint(20) unsigned          DEFAULT 0 COMMENT '供应商ID（采购入库时使用） | @s=eq @e @exp @imp @c=select',
    `source_warehouse_id`  bigint(20) unsigned          DEFAULT 0 COMMENT '来源仓库ID（调拨入库时使用） | @s=eq @e @exp @imp @c=select',
    `inbound_date`         date                NOT NULL COMMENT '计划入库日期 | @required @s=date @e @exp @imp @fmt=date',
    `total_amount`         decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '入库总金额 | @number @component:currency @s=between @e @exp @imp',
    `total_quantity`       decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '入库总数量 | @number @s=between @e @exp @imp',
    `reason`               varchar(500)        NOT NULL DEFAULT '' COMMENT '入库原因 | @required @max:500 @s=like @e @exp @imp @form:textarea',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_inbound_no` (`inbound_no`),
    KEY `idx_inbound_date` (`inbound_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='入库申请表 @module:ims @exp:true @imp:true';

-- todo 暂时不需要 入库明细表
-- 生成器命令: php think generator:crud ims_inbound_item --module=ims --frontend --overwrite
DROP TABLE IF EXISTS `ims_inbound_item`;
CREATE TABLE `ims_inbound_item`
(
    `id`              bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '明细ID | @h',
    `tenant_id`       bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',
    `inbound_id`      bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '入库申请ID | @required @s=eq @e @exp @imp',
    `product_id`      bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '产品ID | @required @s=eq @e @exp @imp @c=select',
    `product_spec_id` bigint(20) unsigned          DEFAULT 0 COMMENT '产品规格ID | @s=eq @e @exp @imp @c=select',
    `quantity`        decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '入库数量 | @required @number @min:0.01 @s=between @e @exp @imp',
    `unit_price`      decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '单价 | @required @number @component:currency @s=between @e @exp @imp',
    `total_amount`    decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '小计金额 | @number @component:currency @s=between @e @exp @imp',
    `remark`          varchar(255)                 DEFAULT '' COMMENT '备注 | @max:255 @e @exp @imp @form:textarea',

    -- 系统字段
    `created_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`      datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`      datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_inbound_id` (`inbound_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='入库明细表 @module:ims @exp:true @imp:true';

-- 出货申请表（从供应商虚拟仓发货）
-- 生成器命令: php think generator:crud ims_shipment_approval --module=ims --frontend --overwrite
DROP TABLE IF EXISTS `ims_shipment_approval`;
CREATE TABLE `ims_shipment_approval`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '出货申请ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
    `shipment_no`          varchar(50)         NOT NULL DEFAULT '' COMMENT '出货单号 | @required @max:50 @s=like @e @exp @imp',
    `dept_id`              bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '所在部门ID | @required @s=eq @e @exp @imp @c=select',
    `supplier_id`          bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '供应商ID（虚拟仓） | @required @s=eq @e @exp @imp @c=select',
    `customer_id`          bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '客户ID | @required @s=eq @e @exp @imp @c=select',
    `shipment_date`        date                NOT NULL COMMENT '出货日期 | @required @s=date @e @exp @imp @fmt=date',
    `delivery_address`     text COMMENT '收货地址 | @e @exp @imp @form:textarea',
    `contact_person`       varchar(50)                  DEFAULT '' COMMENT '联系人 | @max:50 @s=like @e @exp @imp',
    `contact_phone`        varchar(20)                  DEFAULT '' COMMENT '联系电话 | @max:20 @s=like @e @exp @imp',
    `total_amount`         decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '出货总金额 | @number @component:currency @s=between @e @exp @imp',
    `total_amount_upper`   varchar(200)                 DEFAULT '' COMMENT '出货总金额大写 | @max:200 @e @exp @imp @h',
    `total_quantity`       decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '出货总数量 | @number @s=between @e @exp @imp',
    `logistics_company`    varchar(100)                 DEFAULT '' COMMENT '物流公司 | @max:100 @s=like @e @exp @imp',
    `tracking_no`          varchar(100)                 DEFAULT '' COMMENT '物流单号 | @max:100 @s=like @e @exp @imp',
    `vehicle_info`         varchar(200)                 DEFAULT '' COMMENT '运输车辆信息 | @max:200 @s=like @e @exp @imp @form:textarea',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_shipment_no` (`shipment_no`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_shipment_date` (`shipment_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='出货申请表 @module:ims @exp:true @imp:true';

-- 出货明细表
-- 生成器命令: php think generator:crud ims_shipment_item --module=ims --frontend --overwrite
DROP TABLE IF EXISTS `ims_shipment_item`;
CREATE TABLE `ims_shipment_item`
(
    `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '明细ID | @h',
    `tenant_id`    bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',
    `shipment_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '出货申请ID | @required @s=eq @e @exp @imp',
    `supplier_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '供应商ID @required @search:eq @exp @imp',
    `product_id`   bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '产品ID | @required @s=eq @e @exp @imp @c=select',
    `quantity`     decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '出货数量 | @required @number @min:0.01 @s=between @e @exp @imp',
    `unit_price`   decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '单价 | @required @number @component:currency @s=between @e @exp @imp',
    `total_amount` decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '小计金额 | @number @component:currency @s=between @e @exp @imp',
    `remark`       varchar(255)                 DEFAULT '' COMMENT '备注 | @max:255 @e @exp @imp @form:textarea',

    -- 系统字段
    `created_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`   datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_shipment_id` (`shipment_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='出货明细表 @module:ims @exp:true @imp:true';

-- 采购申请表
-- 生成器命令: php think generator:crud ims_purchase_approval --module=ims --frontend --overwrite
DROP TABLE IF EXISTS `ims_purchase_approval`;
CREATE TABLE `ims_purchase_approval`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '采购申请ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
    `purchase_no`          varchar(50)         NOT NULL DEFAULT '' COMMENT '采购申请单号 | @required @max:50 @s=like @e @exp @imp',
    `purchase_type`        tinyint(1)          NOT NULL DEFAULT 1 COMMENT '采购类型:1=常规采购,2=紧急采购,3=补货采购 | @required @s=eq @e @exp @imp @fmt=tag',
    `supplier_id`          bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '供应商ID | @required @s=eq @e @exp @imp @c=select',
    `expected_date`        date                NOT NULL COMMENT '交付日期 | @required @s=date @e @exp @imp @fmt=date',
    `delivery_address`     text COMMENT '收货地址 | @e @exp @imp @form:textarea',
    `contact_person`       varchar(50)                  DEFAULT '' COMMENT '收款人 | @max:50 @s=like @e @exp @imp',
    `contact_phone`        varchar(20)                  DEFAULT '' COMMENT '收款人电话 | @max:20 @s=like @e @exp @imp',
    `total_amount`         decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '采购总金额 | @number @component:currency @s=between @e @exp @imp',
    `total_amount_upper`   varchar(200)                 DEFAULT '' COMMENT '采购总金额大写 | @max:200 @e @exp @imp @h',
    `total_quantity`       decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '采购总数量 | @number @s=between @e @exp @imp',
    `payee_name`           varchar(100)        NOT NULL DEFAULT '' COMMENT '收款方名称 | @required @max:100 @s=like @e @exp @imp',
    `payee_account`        varchar(100)                 DEFAULT '' COMMENT '收款账户 | @max:100 @s=like @e @exp @imp',
    `payee_bank`           varchar(100)                 DEFAULT '' COMMENT '开户行 | @max:100 @s=like @e @exp @imp @c=select',
    `payment_method`       tinyint(1)          NOT NULL DEFAULT 1 COMMENT '付款方式:1=银行转账,2=现金,3=支票,4=其他 | @required @s=eq @e @exp @imp @fmt=tag',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_purchase_no` (`purchase_no`),
    KEY `idx_supplier_id` (`supplier_id`),
    KEY `idx_expected_date` (`expected_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='采购申请表 @module:ims @exp:true @imp:true';

-- 采购明细表
-- 生成器命令: php think generator:crud ims_purchase_item --module=ims --frontend --overwrite
DROP TABLE IF EXISTS `ims_purchase_item`;
CREATE TABLE `ims_purchase_item`
(
    `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '明细ID | @h',
    `tenant_id`    bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',
    `purchase_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '采购申请ID | @required @s=eq @e @exp @imp',
    `product_id`   bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '产品ID | @required @s=eq @e @exp @imp @c=select',
    `quantity`     decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '采购数量 | @required @number @min:0.01 @s=between @e @exp @imp',
    `unit_price`   decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '单价 | @required @number @component:currency @s=between @e @exp @imp',
    `total_amount` decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '小计金额 | @number @component:currency @s=between @e @exp @imp',
    `urgent_level` tinyint(1)          NOT NULL DEFAULT 1 COMMENT '紧急程度:1=一般,2=紧急,3=特急 | @s=eq @e @exp @imp @fmt=tag',
    `remark`       varchar(255)                 DEFAULT '' COMMENT '备注 | @max:255 @e @exp @imp @form:textarea',

    -- 系统字段
    `created_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`   datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`   datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_purchase_id` (`purchase_id`),
    KEY `idx_product_id` (`product_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='采购明细表 @module:ims @exp:true @imp:true';

-- 付款申请表
-- 生成器命令: php think generator:crud hr_finance_payment_approval --module=finance --frontend --overwrite
DROP TABLE IF EXISTS `finance_payment_approval`;
CREATE TABLE `finance_payment_approval`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '付款申请ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
    `payment_no`           varchar(50)         NOT NULL DEFAULT '' COMMENT '付款申请单号 | @required @max:50 @s=like @e @exp @imp',
#     `payment_type`         tinyint(1)          NOT NULL DEFAULT 1 COMMENT '付款类型:1=采购付款,2=费用报销,3=预付款,4=其他付款 | @required @s=eq @e @exp @imp @fmt=tag',
#     `payee_type`           tinyint(1)          NOT NULL DEFAULT 1 COMMENT '收款方类型:1=供应商,2=员工,3=其他 | @required @s=eq @e @exp @imp @fmt=tag',
#     `payee_id`             bigint(20) unsigned          DEFAULT 0 COMMENT '收款方ID | @s=eq @e @exp @imp @c=select',
    `payee_name`           varchar(120)        NOT NULL DEFAULT '' COMMENT '支付对象 | @required @max:100 @s=like @e @exp @imp',
    `payee_account`        varchar(100)                 DEFAULT '' COMMENT '银行账户 | @max:100 @s=like @e @exp @imp',
    `payee_bank`           varchar(100)                 DEFAULT '' COMMENT '开户行 | @max:100 @s=like @e @exp @imp @c=select',
    `payment_amount`       decimal(15, 2)      NOT NULL DEFAULT 0.00 COMMENT '付款总额 | @required @number @component:currency @min:0.01 @s=between @e @exp @imp',
    `payment_amount_upper` varchar(200)                 DEFAULT '' COMMENT '付款总额大写 | @max:200 @e @exp @imp @h',
    `payment_date`         date                NOT NULL COMMENT '支付日期 | @required @s=date @e @exp @imp @fmt=date',
    `payment_method`       tinyint(1)          NOT NULL DEFAULT 1 COMMENT '付款方式:1=银行转账,2=现金,3=支票,4=其他 | @required @s=eq @e @exp @imp @fmt=tag',
#     `business_id`          bigint(20) unsigned          DEFAULT 0 COMMENT '关联业务ID（如采购单、报销单等） | @s=eq @e @exp @imp',
#     `business_type`        varchar(50)                  DEFAULT '' COMMENT '关联业务类型 | @max:50 @s=like @e @exp @imp',
    `reason`               text COMMENT '付款事由 | @required @e @exp @imp @form:textarea',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_payment_no` (`payment_no`),
    KEY `idx_payment_date` (`payment_date`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='付款申请表 @module:finance @exp:true @imp:true';

-- =====================================================================================
-- 2. 通用业务审批表（集成在"我的申请"中）
-- =====================================================================================

-- 样品邮寄申请表
-- 生成器命令: php think generator:crud office_sample_mail --module=office --frontend --overwrite
DROP TABLE IF EXISTS `office_sample_mail`;
CREATE TABLE `office_sample_mail`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
#     `customer_id`          bigint(20) unsigned          DEFAULT 0 COMMENT '客户ID | @s=eq @e @exp @imp @c=select',
#     `customer_name`        varchar(100)        NOT NULL DEFAULT '' COMMENT '客户名称 | @required @max:100 @s=like @e @exp @imp',
    `delivery_address`     text                NOT NULL COMMENT '收件信息 | @required @e @exp @imp @form:textarea',
#     `sample_type`          tinyint(1)          NOT NULL DEFAULT 1 COMMENT '样品类型:1=产品样品,2=包装样品,3=其他样品 | @required @s=eq @e @exp @imp @fmt=tag',
    `sample_name`          varchar(100)        NOT NULL DEFAULT '' COMMENT '样品名称 | @required @max:100 @s=like @e @exp @imp',
    `sample_description`   text                NOT NULL COMMENT '样品描述 | @required @e @exp @imp @form:textarea',
    `sender_phone`         varchar(20)                  DEFAULT '' COMMENT '寄件人电话 | @max:20 @s=like @e @exp @imp',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='样品邮寄申请表 @module:office @exp:true @imp:true';

-- 报销申请表
-- 生成器命令: php think generator:crud finance_expense_reimbursement --module=finance --frontend --overwrite
DROP TABLE IF EXISTS `finance_expense_reimbursement`;
CREATE TABLE `finance_expense_reimbursement`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
    `reimbursement_no`     varchar(50)         NOT NULL DEFAULT '' COMMENT '报销单号 | @required @max:50 @s=like @e @exp @imp',
    `expense_type`         tinyint(1)          NOT NULL DEFAULT 1 COMMENT '报销类型:1=差旅费,2=交通费,3=餐费,4=住宿费,5=办公费,6=通讯费,7=其他 | @required @s=eq @e @exp @imp @fmt=tag',
    `total_amount`         decimal(10, 2)      NOT NULL DEFAULT 0.00 COMMENT '报销总金额 | @required @number @component:currency @min:0.01 @s=between @e @exp @imp',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件（发票、收据等） | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_reimbursement_no` (`reimbursement_no`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报销申请表 @module:finance @exp:true @imp:true';

-- 报销明细表
-- 生成器命令: php think generator:crud hr_finance_expense_item --module=finance --frontend --overwrite
DROP TABLE IF EXISTS `finance_expense_item`;
CREATE TABLE `finance_expense_item`
(
    `id`               bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '报销明细ID | @h',
    `tenant_id`        bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',
    `reimbursement_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '报销申请ID | @required @s=eq @e @exp @imp',
    `description`      text COMMENT '费用说明 | @max:255 @s=like @e @exp @imp @form:textarea',

    -- 系统字段
    `created_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`       datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`       datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_reimbursement_id` (`reimbursement_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='报销明细表 @module:finance @exp:true @imp:true';

-- 出差申请表（优化版本，支持多行程明细）
-- 生成器命令: php think generator:crud hr_business_trip --module=hr --frontend --overwrite
DROP TABLE IF EXISTS `hr_business_trip`;
CREATE TABLE `hr_business_trip`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '出差申请ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
#     `trip_type`            tinyint(1)          NOT NULL DEFAULT 1 COMMENT '出差类型:1=国内,2=国际 | @required @s=eq @e @exp @imp @fmt=tag',
#     `trip_mode`            tinyint(1)          NOT NULL DEFAULT 1 COMMENT '单程往返:1=往返,2=单程 | @required @s=eq @e @exp @imp @fmt=tag',
    `duration`             decimal(10, 1)      NOT NULL DEFAULT 0.0 COMMENT '出差总时长(天) | @number @s=between @e @exp @imp',
    `purpose`              text COMMENT '出差事由 | @required @max:500 @s=like @e @exp @imp @form:textarea',
    -- 其他信息
    `companions`           text COMMENT '同行人 | @e @exp @imp @form:textarea',
#     `emergency_contact`    varchar(50)                  DEFAULT '' COMMENT '紧急联系人 | @max:50 @s=like @e @exp @imp',
#     `emergency_phone`      varchar(20)                  DEFAULT '' COMMENT '紧急联系电话 | @max:20 @s=like @e @exp @imp',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='出差申请表 @module:hr @exp:true @imp:true';

-- 出差行程明细表
-- 生成器命令: php think generator:crud hr_business_trip_itinerary --module=hr --frontend --overwrite
DROP TABLE IF EXISTS `hr_business_trip_itinerary`;
CREATE TABLE `hr_business_trip_itinerary`
(
    `id`                    bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '行程明细ID | @h',
    `tenant_id`             bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',
    `business_trip_id`      bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '出差申请ID | @required @s=eq @e @exp @imp',

    -- 行程核心信息
    `departure_city`        varchar(50)         NOT NULL DEFAULT '' COMMENT '出发城市 | @required @max:50 @s=like @e @exp @imp',
    `departure_city_code`   varchar(50)         NOT NULL DEFAULT '' COMMENT '出发城市CODE',
    `destination_city`      varchar(50)         NOT NULL DEFAULT '' COMMENT '目的地城市 | @required @max:50 @s=like @e @exp @imp',
    `destination_city_code` varchar(50)         NOT NULL DEFAULT '' COMMENT '出发城市CODE',
    `trip_mode`             tinyint(1)          NOT NULL DEFAULT 1 COMMENT '单程往返:1=往返,2=单程 | @required @s=eq @e @exp @imp @fmt=tag',
    `start_time`            datetime            NOT NULL COMMENT '开始时间 | @required @s=datetime @e @exp @imp @fmt=datetime',
    `end_time`              datetime            NOT NULL COMMENT '结束时间 | @required @s=datetime @e @exp @imp @fmt=datetime',
    `duration`              decimal(5, 1)       NOT NULL DEFAULT 0.0 COMMENT '时长(天) | @number @s=between @e @exp @imp',

    -- 交通信息
    `transport_type`        tinyint(1)          NOT NULL DEFAULT 1 COMMENT '交通工具:1=飞机,2=高铁,3=火车,4=汽车,5=其他 | @required @s=eq @e @exp @imp @fmt=tag',
#     `transport_no`     varchar(50)                  DEFAULT '' COMMENT '交通工具班次号 | @max:50 @s=like @e @exp @imp',
#     `departure_time`   datetime                     DEFAULT NULL COMMENT '出发时间 | @s=datetime @e @exp @imp @fmt=datetime',
#     `arrival_time`     datetime                     DEFAULT NULL COMMENT '到达时间 | @s=datetime @e @exp @imp @fmt=datetime',

    -- 住宿信息
    /*`hotel_name`       varchar(100)                 DEFAULT '' COMMENT '酒店名称 | @max:100 @s=like @e @exp @imp',
    `hotel_address`    varchar(200)                 DEFAULT '' COMMENT '酒店地址 | @max:200 @s=like @e @exp @imp',
    `check_in_date`    date                         DEFAULT NULL COMMENT '入住日期 | @s=date @e @exp @imp @fmt=date',
    `check_out_date`   date                         DEFAULT NULL COMMENT '退房日期 | @s=date @e @exp @imp @fmt=date',*/

    -- 业务信息

    /*`meeting_info`     text COMMENT '会议安排 | @e @exp @imp @form:textarea',
    `contact_person`   varchar(50)                  DEFAULT '' COMMENT '联系人 | @max:50 @s=like @e @exp @imp',
    `contact_phone`    varchar(20)                  DEFAULT '' COMMENT '联系电话 | @max:20 @s=like @e @exp @imp',*/

    -- 备注信息
    `remark`                text COMMENT '行程备注 | @e @exp @imp @form:textarea',
    `attachment`            text COMMENT '行程相关附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `created_at`            datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`            datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`            datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_business_trip_id` (`business_trip_id`),
    KEY `idx_sequence_no` (`business_trip_id`),
    KEY `idx_itinerary_time` (`start_time`, `end_time`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='出差行程明细表 @module:hr @exp:true @imp:true';

-- 外出申请表
-- 生成器命令: php think generator:crud hr_outing --module=hr --frontend --overwrite
DROP TABLE IF EXISTS `hr_outing`;
CREATE TABLE `hr_outing`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '外出申请ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
    `start_time`           datetime            NOT NULL COMMENT '开始时间 | @required @s=datetime @e @exp @imp @fmt=datetime',
    `end_time`             datetime            NOT NULL COMMENT '结束时间 | @required @s=datetime @e @exp @imp @fmt=datetime',
    `duration`             decimal(5, 1)       NOT NULL DEFAULT 0.0 COMMENT '外出时长(小时) | @number @s=between @e @exp @imp',
    `purpose`              text COMMENT '外出事由 | @required @max:500 @s=like @e @exp @imp @form:textarea',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '附件 | @e @exp @imp @form:upload @component:file',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_outing_time` (`start_time`, `end_time`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='外出申请表 @module:hr @exp:true @imp:true';

-- 办公采购申请表
-- 生成器命令: php think generator:crud office_procurement --module=office --frontend --overwrite
DROP TABLE IF EXISTS `office_procurement`;
CREATE TABLE `office_procurement`
(
    `id`                   bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '办公采购ID | @h',
    `tenant_id`            bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户ID | @h',

    -- 工作流集成字段
    `workflow_instance_id` bigint(20) unsigned          DEFAULT NULL COMMENT '工作流实例ID | @s=eq @e @exp',
    `approval_status`      tinyint(1)                   DEFAULT NULL COMMENT '审批状态:0=草稿,1=审批中,2=已通过,3=已拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @exp @imp @fmt=status @col=tag',
    `submit_time`          datetime                     DEFAULT NULL COMMENT '提交审批时间 | @s=datetime @e @exp @fmt=datetime',
    `approval_time`        datetime                     DEFAULT NULL COMMENT '审批完成时间 | @s=datetime @e @exp @fmt=datetime',
    `submitter_id`         bigint(20) unsigned          DEFAULT NULL COMMENT '提交人ID | @s=eq @e @exp',

    -- 业务字段
    `procurement_type`     tinyint(1)                   DEFAULT NULL COMMENT '采购类型:1=生产原料,2=办公用品,3=其他 | @s=eq @e @exp @imp @fmt=tag',
    `delivery_date`        date                NOT NULL COMMENT '交付日期 | @required @s=date @e @exp @imp @fmt=date',
    `item_name`            varchar(200)        NOT NULL DEFAULT '' COMMENT '物品名称 | @required @max:200 @s=like @e @exp @imp',
    `supplier_name`        varchar(200)        NOT NULL DEFAULT '' COMMENT '采购来源单位名称 | @required @max:200 @s=like @e @exp @imp',
    `unit_price`           decimal(10, 2)      NOT NULL DEFAULT 0.00 COMMENT '单价(元) | @required @number @component:currency @min:0.01 @s=between @e @exp @imp',
    `quantity`             decimal(10, 2)      NOT NULL DEFAULT 0.00 COMMENT '数量 | @required @number @min:1 @s=between @e @exp @imp',
    `payment_amount`       decimal(10, 2)      NOT NULL DEFAULT 0.00 COMMENT '付款金额 | @required @number @component:currency @min:0.01 @s=between @e @exp @imp',
    `payment_amount_words` varchar(500)        NOT NULL DEFAULT '' COMMENT '付款金额大写 | @required @max:500 @readonly @e @exp @imp',
    `payee_name`           varchar(100)        NOT NULL DEFAULT '' COMMENT '收款人 | @required @max:100 @s=like @e @exp @imp',
    `bank_name`            varchar(200)        NOT NULL DEFAULT '' COMMENT '开户行 | @required @max:200 @s=like @e @exp @imp',
    `bank_account`         varchar(50)         NOT NULL DEFAULT '' COMMENT '收款账号 | @required @max:50 @s=like @e @exp @imp',
    `payment_method`       tinyint(1)          NOT NULL DEFAULT 1 COMMENT '支付方式:1=银行转账,2=现金支付,3=支票,4=支付宝,5=微信,6=其他 | @required @s=eq @e @exp @imp @fmt=tag',
    `remark`               text COMMENT '备注 | @e @exp @imp @form:textarea',
    `attachment`           text COMMENT '图片附件 | @e @exp @imp @form:upload @component:image',

    -- 系统字段
    `creator_id`           bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人 | @h',
    `created_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间 | @fmt=datetime @exp',
    `updated_at`           datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间 | @fmt=datetime @exp',
    `deleted_at`           datetime                     DEFAULT NULL COMMENT '删除时间 | @h',

    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`),
    KEY `idx_workflow_instance_id` (`workflow_instance_id`),
    KEY `idx_approval_status` (`approval_status`),
    KEY `idx_delivery_date` (`delivery_date`),
    KEY `idx_item_name` (`item_name`),
    KEY `idx_supplier_name` (`supplier_name`),
    KEY `idx_payment_amount` (`payment_amount`),
    KEY `idx_deleted_at` (`deleted_at`)
) ENGINE = InnoDB COMMENT ='办公采购申请表 @module:office @exp:true @imp:true';
