<?php
/**
 * 基于模型查询的统计方法示例代码
 * 展示如何使用ThinkPHP模型关联进行统计查询
 */

// 模拟ThinkPHP环境
require_once 'vendor/autoload.php';

use app\project\model\ProjectProject;
use app\project\model\ProjectTask;
use app\project\model\ProjectMember;
use app\project\model\ProjectTaskRecord;
use app\system\model\AdminModel;

/**
 * 1. 任务状态分布统计 - 基于模型关联
 */
function getTaskStatusStats($projectId)
{
    // 状态映射
    $statusMap = [
        1 => ['name' => '待办', 'color' => '#8C8C8C'],
        2 => ['name' => '进行中', 'color' => '#1664FF'], 
        3 => ['name' => '已完成', 'color' => '#00BC70'],
        4 => ['name' => '已关闭', 'color' => '#F54A45']
    ];
    
    // 方法1: 使用项目模型的关联查询
    $project = ProjectProject::find($projectId);
    if (!$project) {
        return [];
    }
    
    // 获取各状态的任务数量
    $result = [];
    foreach ($statusMap as $status => $config) {
        $count = $project->tasks()
                        ->where('status', $status)
                        ->count();
        
        if ($count > 0) {
            $result[] = [
                'name' => $config['name'],
                'value' => $count,
                'color' => $config['color']
            ];
        }
    }
    
    return $result;
}

/**
 * 2. 任务优先级分布统计 - 基于模型关联
 */
function getTaskPriorityStats($projectId)
{
    $priorityMap = [
        1 => '低',
        2 => '中', 
        3 => '高'
    ];
    
    $project = ProjectProject::find($projectId);
    if (!$project) {
        return [];
    }
    
    $result = [];
    foreach ($priorityMap as $priority => $name) {
        $count = $project->tasks()
                        ->where('priority', $priority)
                        ->count();
        
        if ($count > 0) {
            $result[] = [
                'name' => $name,
                'value' => $count
            ];
        }
    }
    
    return $result;
}

/**
 * 3. 项目进度趋势 - 基于任务完成情况
 */
function getProgressTrend($projectId)
{
    $project = ProjectProject::find($projectId);
    if (!$project) {
        return [];
    }
    
    // 方法1: 基于任务创建时间的月度完成率
    $tasks = $project->tasks()
                    ->field('created_at, status')
                    ->order('created_at asc')
                    ->select();
    
    $monthlyData = [];
    foreach ($tasks as $task) {
        $month = date('Y-m', strtotime($task->created_at));
        if (!isset($monthlyData[$month])) {
            $monthlyData[$month] = ['total' => 0, 'completed' => 0];
        }
        $monthlyData[$month]['total']++;
        if ($task->status == 3) {
            $monthlyData[$month]['completed']++;
        }
    }
    
    $result = [];
    foreach ($monthlyData as $month => $data) {
        $progress = $data['total'] > 0 ? round(($data['completed'] / $data['total']) * 100, 2) : 0;
        $result[] = [
            'date' => $month,
            'progress' => $progress
        ];
    }
    
    return $result;
}

/**
 * 4. 成员工作量统计 - 基于关联查询
 */
function getMemberStats($projectId)
{
    $project = ProjectProject::find($projectId);
    if (!$project) {
        return [];
    }
    
    // 获取项目成员及其任务统计
    $members = $project->members()
                      ->with(['user']) // 预加载用户信息
                      ->select();
    
    $result = [];
    foreach ($members as $member) {
        if (!$member->user) {
            continue;
        }
        
        // 统计该成员在此项目中的任务
        $totalTasks = ProjectTask::where('project_id', $projectId)
                                ->where('assignee_id', $member->user_id)
                                ->count();
        
        $completedTasks = ProjectTask::where('project_id', $projectId)
                                   ->where('assignee_id', $member->user_id)
                                   ->where('status', 3)
                                   ->count();
        
        $completionRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0;
        
        $result[] = [
            'id' => $member->user->id,
            'name' => $member->user->real_name,
            'avatar' => $member->user->avatar,
            'task_count' => $totalTasks,
            'completion_rate' => $completionRate
        ];
    }
    
    return $result;
}

/**
 * 5. 最近活动 - 基于任务记录
 */
function getRecentActivities($projectId)
{
    // 获取项目的所有任务ID
    $project = ProjectProject::find($projectId);
    if (!$project) {
        return [];
    }
    
    $taskIds = $project->tasks()->column('id');
    if (empty($taskIds)) {
        return [];
    }
    
    // 获取最近的任务记录
    $records = ProjectTaskRecord::with(['task', 'creator'])
                                ->whereIn('task_id', $taskIds)
                                ->order('created_at desc')
                                ->limit(10)
                                ->select();
    
    $result = [];
    foreach ($records as $record) {
        if (!$record->task || !$record->creator) {
            continue;
        }
        
        $action = $record->record_type == 'comment' ? '评论了任务' : '跟进了任务';
        $time = $this->formatTime($record->created_at);
        
        $result[] = [
            'user' => $record->creator->real_name,
            'action' => $action,
            'target' => $record->task->title,
            'time' => $time
        ];
    }
    
    return $result;
}

/**
 * 6. 优化版本 - 使用单次查询获取多个统计
 */
function getProjectStatsOptimized($projectId)
{
    $project = ProjectProject::with([
        'tasks' => function($query) {
            $query->field('id,project_id,status,priority,assignee_id,created_at');
        },
        'members.user' => function($query) {
            $query->field('id,real_name,avatar');
        }
    ])->find($projectId);
    
    if (!$project) {
        return [];
    }
    
    $tasks = $project->tasks;
    
    // 状态统计
    $statusStats = [];
    $statusMap = [
        1 => ['name' => '待办', 'color' => '#8C8C8C'],
        2 => ['name' => '进行中', 'color' => '#1664FF'], 
        3 => ['name' => '已完成', 'color' => '#00BC70'],
        4 => ['name' => '已关闭', 'color' => '#F54A45']
    ];
    
    $statusCounts = [];
    foreach ($tasks as $task) {
        $statusCounts[$task->status] = ($statusCounts[$task->status] ?? 0) + 1;
    }
    
    foreach ($statusMap as $status => $config) {
        if (isset($statusCounts[$status])) {
            $statusStats[] = [
                'name' => $config['name'],
                'value' => $statusCounts[$status],
                'color' => $config['color']
            ];
        }
    }
    
    // 优先级统计
    $priorityStats = [];
    $priorityMap = [1 => '低', 2 => '中', 3 => '高'];
    $priorityCounts = [];
    
    foreach ($tasks as $task) {
        $priorityCounts[$task->priority] = ($priorityCounts[$task->priority] ?? 0) + 1;
    }
    
    foreach ($priorityMap as $priority => $name) {
        if (isset($priorityCounts[$priority])) {
            $priorityStats[] = [
                'name' => $name,
                'value' => $priorityCounts[$priority]
            ];
        }
    }
    
    // 成员统计
    $memberStats = [];
    foreach ($project->members as $member) {
        if (!$member->user) continue;
        
        $memberTasks = array_filter($tasks->toArray(), function($task) use ($member) {
            return $task['assignee_id'] == $member->user_id;
        });
        
        $totalTasks = count($memberTasks);
        $completedTasks = count(array_filter($memberTasks, function($task) {
            return $task['status'] == 3;
        }));
        
        $completionRate = $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0;
        
        $memberStats[] = [
            'id' => $member->user->id,
            'name' => $member->user->real_name,
            'avatar' => $member->user->avatar,
            'task_count' => $totalTasks,
            'completion_rate' => $completionRate
        ];
    }
    
    return [
        'taskStatus' => $statusStats,
        'taskPriority' => $priorityStats,
        'memberStats' => $memberStats
    ];
}

/**
 * 辅助方法：格式化时间
 */
function formatTime($datetime)
{
    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;
    
    if ($diff < 3600) {
        return floor($diff / 60) . '分钟前';
    } elseif ($diff < 86400) {
        return floor($diff / 3600) . '小时前';
    } elseif ($diff < 2592000) {
        return floor($diff / 86400) . '天前';
    } else {
        return date('Y-m-d', $time);
    }
}

// 测试代码
echo "=== 模型查询方案示例 ===\n";
echo "1. 任务状态分布: " . json_encode(getTaskStatusStats(1), JSON_UNESCAPED_UNICODE) . "\n";
echo "2. 任务优先级分布: " . json_encode(getTaskPriorityStats(1), JSON_UNESCAPED_UNICODE) . "\n";
echo "3. 成员工作量统计: " . json_encode(getMemberStats(1), JSON_UNESCAPED_UNICODE) . "\n";
?>
