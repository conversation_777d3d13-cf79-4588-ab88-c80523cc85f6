import request from '@/utils/http'
import { PaginationResult, BaseResult } from '@/types/axios'

/**
 * 我的申请 API 接口
 */
export class ApplicationApi {
  /**
   * 获取流程类型列表
   */
  static getFlowTypes() {
    return request.get<PaginationResult<any[]>>({
      url: '/workflow/myapp/moduleTypes'
    })
  }

  /**
   * 获取我的申请列表
   * @param params 查询参数
   */
  static list(params: any) {
    return request.get<PaginationResult<any[]>>({
      url: '/workflow/myapp/index',
      params
    })
  }

  /**
   * 获取申请详情
   * @param id 申请ID
   * @param options
   */
  static detail(id: number | string, options?: any) {
    return request.get<BaseResult>({
      url: `/workflow/myapp/detail/${id}`,
      params: options
    })
  }

  /**
   * 获取流程图数据
   * @param id 申请ID
   */
  static processGraph(id: number | string) {
    return request.get<BaseResult>({
      url: `/workflow/myapp/processGraph/${id}`
    })
  }

  /**
   * 获取审批历史记录
   * @param id 申请ID
   */
  static history(id: number | string) {
    return request.get<BaseResult>({
      url: `/workflow/myapp/history/${id}`
    })
  }

  /**
   * 撤回申请
   * @param id 申请ID
   */
  static recall(id: number | string) {
    return request.post<BaseResult>({
      url: `/workflow/myapp/recall/${id}`
    })
  }

  /**
   * 终止流程
   * @param id 申请ID
   */
  static terminate(id: number | string) {
    return request.post<BaseResult>({
      url: `/workflow/myapp/terminate/${id}`
    })
  }

  /**
   * 删除申请
   * @param id 申请ID
   */
  static delete(id: number | string) {
    return request.post<BaseResult>({
      url: `/workflow/myapp/delete/${id}`
    })
  }

  /**
   * 提交申请（新建并提交）
   * @param data 申请数据
   */
  static submit(data: any) {
    return request.post<BaseResult>({
      url: '/workflow/myapp/submit',
      data
    })
  }

  /**
   * 编辑申请
   * @param id 申请ID
   * @param data 申请数据
   */
  static edit(id: number | string, data: any) {
    return request.post<BaseResult>({
      url: `/workflow/myapp/edit/${id}`,
      data
    })
  }

  /**
   * 表格列提交申请
   * @param id 申请ID
   */
  static confirm(id: number | string) {
    return request.post<BaseResult>({
      url: `/workflow/myapp/confirm/${id}`
    })
  }

  /**
   * 保存草稿
   * @param data 申请数据
   */
  static save(data: any) {
    return request.post<BaseResult>({
      url: '/workflow/myapp/save',
      data
    })
  }

  /**
   * 作废申请
   * @param id 申请ID
   * @param reason 作废原因
   */
  static voidApplication(id: number | string, reason?: string) {
    return request.post<BaseResult>({
      url: `/workflow/myapp/void/${id}`,
      data: {
        reason: reason || ''
      }
    })
  }
}
