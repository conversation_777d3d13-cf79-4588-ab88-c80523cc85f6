-- 任务提醒记录表
-- 用于防止重复提醒和记录提醒历史

CREATE TABLE `project_task_reminder_log` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `task_id` bigint(20) UNSIGNED NOT NULL COMMENT '任务ID',
    `reminder_type` varchar(50) NOT NULL COMMENT '提醒类型：overdue(逾期), due_soon_1(1天后到期), due_soon_3(3天后到期), due_soon_7(7天后到期), follow_up(跟进计划), crm_follow_up(CRM跟进)',
    `reminder_date` date NOT NULL COMMENT '提醒日期（用于防重复）',
    `sent_at` datetime NOT NULL COMMENT '发送时间',
    `tenant_id` bigint(20) UNSIGNED NOT NULL DEFAULT 1 COMMENT '租户ID',
    `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_type_date` (`task_id`, `reminder_type`, `reminder_date`) COMMENT '防止同一任务同一类型同一天重复提醒',
    KEY `idx_tenant_date` (`tenant_id`, `reminder_date`) COMMENT '租户和日期索引',
    KEY `idx_task_id` (`task_id`) COMMENT '任务ID索引',
    KEY `idx_reminder_type` (`reminder_type`) COMMENT '提醒类型索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务提醒记录表';

-- 插入示例数据（可选）
-- INSERT INTO `project_task_reminder_log` (`task_id`, `reminder_type`, `reminder_date`, `sent_at`, `tenant_id`) VALUES
-- (1, 'overdue', '2025-08-04', '2025-08-04 10:30:00', 1),
-- (2, 'due_soon_1', '2025-08-04', '2025-08-04 11:00:00', 1),
-- (3, 'follow_up', '2025-08-04', '2025-08-04 11:30:00', 1);
