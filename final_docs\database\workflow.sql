CREATE TABLE `workflow_type`
(
    `id`                    bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '流程定义ID',
    `name`                  varchar(100)        NOT NULL COMMENT '类型名称',
    `module_code`           varchar(50)         NOT NULL DEFAULT '' COMMENT '所属模块编码',
    `business_code`         varchar(50)         NOT NULL DEFAULT '' COMMENT '所属业务编码',
    `condition_form_fields` text COMMENT '工作流条件表单字段配置，JSON格式',
    `status`                tinyint(1)          NOT NULL DEFAULT '0' COMMENT '状态：0禁用，1启用',
    `creator_id`            bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
    `created_at`            datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`            datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`            datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`             bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_module_code` (`module_code`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 2
  DEFAULT CHARSET = utf8mb4 COMMENT ='工作流程类型表';

# 工作流程定义表（整合了条件表功能）
CREATE TABLE `workflow_definition`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '流程定义ID',
    `name`        varchar(100)        NOT NULL COMMENT '流程名称',
    `type_id`     bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '流程类型ID',
    `form_id`     bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '表单ID(弃用，后续迭代)',
    `flow_config` text COMMENT '流程配置，JSON格式',
    `icon`        varchar(100)                 DEFAULT NULL COMMENT '图标',
    `status`      tinyint(1)          NOT NULL DEFAULT '0' COMMENT '状态：0禁用，1启用',
    `is_template` tinyint(1)          NOT NULL DEFAULT '0' COMMENT '是否为模板：0否，1是',
    `remark`      varchar(255)                 DEFAULT NULL COMMENT '说明',
    `creator_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`   bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='工作流程定义表';

# 工作流程实例表
DROP TABLE IF EXISTS `workflow_instance`;
CREATE TABLE `workflow_instance`
(
    `id`                bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '实例ID',
    `process_id`        varchar(64)         NOT NULL COMMENT '流程实例ID',
    `definition_id`     bigint(20) unsigned NOT NULL COMMENT '流程定义ID',
    `type_id`           bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '流程类型ID',
    `title`             varchar(200)        NOT NULL default '' COMMENT '流程标题',
    `business_id`       varchar(64)         NOT NULL DEFAULT '' COMMENT '业务ID',
    `business_code`     varchar(64)                  DEFAULT NULL COMMENT '业务ID',
    `form_data`         text COMMENT '表单数据，JSON格式',
    `process_data`      text COMMENT '流程数据，JSON格式',
    `status`            tinyint(1)          NOT NULL DEFAULT '0' COMMENT '状态:0=已保存(草稿),1=审批中,2=已通过,3=拒绝,4=已终止,5=已撤回,6=已作废 | @s=eq @e @fmt=status',
    `current_node`      varchar(64)                  DEFAULT NULL COMMENT '当前节点ID',
    `start_time`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    `end_time`          datetime                     DEFAULT NULL COMMENT '结束时间',
    `duration`          int(11)                      DEFAULT NULL COMMENT '持续时间(秒)',
    `submitter_id`      bigint(20) unsigned NOT NULL COMMENT '提交人ID',
    `submitter_dept_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '提交人部门ID',
    `cc_users`          text COMMENT '抄送人列表，JSON格式',
    `remark`            varchar(255)                 DEFAULT NULL COMMENT '备注',
    `creator_id`        bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
    `created_at`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`        datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`        datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`         bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_process_id` (`process_id`),
    KEY `idx_business_code` (`business_code`),
    KEY `idx_created_at` (`created_at`),
    KEY `idx_deleted_at` (`deleted_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='工作流程实例表';

# 工作流程任务表
DROP TABLE IF EXISTS `workflow_task`;
CREATE TABLE `workflow_task`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
    `task_id`     varchar(64)         NOT NULL COMMENT '任务ID',
    `instance_id` bigint(20) unsigned NOT NULL COMMENT '流程实例ID',
    `process_id`  varchar(64)         NOT NULL COMMENT '流程实例ID',
    `node_id`     varchar(64)         NOT NULL COMMENT '节点ID',
    `node_name`   varchar(100)        NOT NULL COMMENT '节点名称',
    `node_type`   varchar(30)         NOT NULL COMMENT '节点类型',
    `task_type`   tinyint(1)          NOT NULL DEFAULT '0' COMMENT '任务类型：0审批任务，1抄送任务',
    `approver_id` bigint(20) unsigned NOT NULL COMMENT '审批人ID',
    `agent_id`    bigint(20) unsigned          DEFAULT NULL COMMENT '代理人ID（替代委托表）',
    `status`      tinyint(1)          NOT NULL DEFAULT '0' COMMENT '状态：0待处理，1已同意，2已驳回，3已转交，4已终止，5已撤回',
    `sort`        int(11)             NOT NULL DEFAULT '0' COMMENT '排序',
    `opinion`     varchar(500)                 DEFAULT NULL COMMENT '审批意见',
    `handle_time` datetime                     DEFAULT NULL COMMENT '处理时间',
    `due_time`    datetime                     DEFAULT NULL COMMENT '截止时间',
    `duration`    int(11)                      DEFAULT NULL COMMENT '处理耗时(秒)',
    `form_data`   text COMMENT '表单数据，JSON格式',
    `is_urged`    tinyint(1)          NOT NULL DEFAULT '0' COMMENT '是否已催办：0否，1是',
    `urge_time`   datetime                     DEFAULT NULL COMMENT '最后催办时间',
    `remark`      varchar(255)                 DEFAULT NULL COMMENT '备注',
    `creator_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建人',
    `created_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`  datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`  datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`   bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_task_id` (`task_id`),
    KEY `idx_instance_id` (`instance_id`),
    KEY `idx_approver_id` (`approver_id`),
    KEY `idx_deleted_at` (`deleted_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='工作流程任务表';

# 工作流程历史记录表（合并了task_history和flow_log）
DROP TABLE IF EXISTS `workflow_history`;
CREATE TABLE `workflow_history`
(
    `id`             bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `instance_id`    bigint(20) unsigned NOT NULL COMMENT '流程实例ID',
    `process_id`     varchar(64)         NOT NULL COMMENT '流程实例ID',
    `task_id`        varchar(64)                  DEFAULT NULL COMMENT '任务ID',
    `node_id`        varchar(64)         NOT NULL COMMENT '节点ID',
    `node_name`      varchar(100)        NOT NULL COMMENT '节点名称',
    `node_type`      varchar(30)         NOT NULL COMMENT '节点类型',
    `prev_node_id`   varchar(64)                  DEFAULT NULL COMMENT '上一节点ID',
    `next_node_id`   varchar(64)                  DEFAULT NULL COMMENT '下一节点ID',
    `operator_id`    bigint(20) unsigned NOT NULL COMMENT '操作人ID',
    `operation`      tinyint(1)          NOT NULL COMMENT '操作类型：1同意，2驳回，3转交，4终止，5撤回，6催办，7抄送，8流程开始，9流程结束',
    `opinion`        varchar(500)                 DEFAULT NULL COMMENT '操作意见',
    `operation_time` datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    `duration`       int(11)                      DEFAULT NULL COMMENT '处理耗时(秒)',
    `remark`         varchar(255)                 DEFAULT NULL COMMENT '备注',
    `created_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`     datetime            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`     datetime                     DEFAULT NULL COMMENT '删除时间',
    `tenant_id`      bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '租户ID',
    PRIMARY KEY (`id`),
    KEY `idx_instance_id` (`instance_id`),
    KEY `idx_process_id` (`process_id`),
    KEY `idx_operator_id` (`operator_id`),
    KEY `idx_deleted_at` (`deleted_at`),
    KEY `idx_tenant_id` (`tenant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='工作流程历史记录表';