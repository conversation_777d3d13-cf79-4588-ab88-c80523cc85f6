<template>
  <div class="task-detail-test">
    <el-card header="TaskDetail 组件权限测试">
      <div class="test-controls">
        <h3>权限控制测试</h3>
        
        <div class="permission-controls">
          <el-checkbox v-model="permissions.canViewComments">
            允许查看评论
          </el-checkbox>
          <el-checkbox v-model="permissions.canAddComment">
            允许添加评论
          </el-checkbox>
          <el-checkbox v-model="permissions.canViewFollows">
            允许查看跟进
          </el-checkbox>
          <el-checkbox v-model="permissions.canAddFollow">
            允许添加跟进
          </el-checkbox>
        </div>
        
        <div class="test-buttons">
          <el-button @click="openTaskDetail" type="primary">
            打开任务详情 (ID: {{ testTaskId }})
          </el-button>
          
          <el-input-number 
            v-model="testTaskId" 
            :min="1" 
            :max="100" 
            label="任务ID"
            style="margin-left: 10px; width: 120px;"
          />
        </div>
        
        <div class="current-permissions">
          <h4>当前权限设置:</h4>
          <ul>
            <li>查看评论: <span :class="permissions.canViewComments ? 'success' : 'error'">{{ permissions.canViewComments ? '✅' : '❌' }}</span></li>
            <li>添加评论: <span :class="permissions.canAddComment ? 'success' : 'error'">{{ permissions.canAddComment ? '✅' : '❌' }}</span></li>
            <li>查看跟进: <span :class="permissions.canViewFollows ? 'success' : 'error'">{{ permissions.canViewFollows ? '✅' : '❌' }}</span></li>
            <li>添加跟进: <span :class="permissions.canAddFollow ? 'success' : 'error'">{{ permissions.canAddFollow ? '✅' : '❌' }}</span></li>
          </ul>
        </div>
      </div>
    </el-card>
    
    <!-- TaskDetail 组件 -->
    <TaskDetail
      v-model:visible="taskDetailVisible"
      :task-id="testTaskId"
      :can-view-comments="permissions.canViewComments"
      :can-add-comment="permissions.canAddComment"
      :can-view-follows="permissions.canViewFollows"
      :can-add-follow="permissions.canAddFollow"
      @task-updated="handleTaskUpdated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import TaskDetail from './TaskDetail.vue'

// 测试数据
const testTaskId = ref(1)
const taskDetailVisible = ref(false)

// 权限控制
const permissions = reactive({
  canViewComments: true,
  canAddComment: true,
  canViewFollows: true,
  canAddFollow: true
})

// 方法
const openTaskDetail = () => {
  console.log('打开任务详情，权限设置:', permissions)
  taskDetailVisible.value = true
}

const handleTaskUpdated = () => {
  ElMessage.success('任务已更新')
}
</script>

<style scoped>
.task-detail-test {
  padding: 20px;
}

.test-controls {
  margin-bottom: 20px;
}

.permission-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 15px 0;
}

.test-buttons {
  display: flex;
  align-items: center;
  margin: 15px 0;
}

.current-permissions {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 6px;
}

.current-permissions h4 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.current-permissions ul {
  margin: 0;
  padding-left: 20px;
}

.current-permissions li {
  margin: 5px 0;
}

.success {
  color: #67c23a;
  font-weight: bold;
}

.error {
  color: #f56c6c;
  font-weight: bold;
}
</style>
